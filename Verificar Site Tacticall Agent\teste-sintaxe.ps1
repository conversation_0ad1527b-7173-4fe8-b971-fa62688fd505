# Script de teste para verificar sintaxe
Write-Host "Testando sintaxe do script principal..."

# Verificar se o arquivo existe
$scriptPath = "Set-TacticalSite-Cliente-Fixed.ps1"
if (-not (Test-Path $scriptPath)) {
    Write-Host "❌ Arquivo não encontrado: $scriptPath"
    exit 1
}

# Tentar carregar o script sem executar
try {
    $scriptContent = Get-Content $scriptPath -Raw
    $scriptBlock = [ScriptBlock]::Create($scriptContent)
    Write-Host "✅ Sintaxe do script está correta!"
    
    # Verificar se as funções estão definidas corretamente
    if ($scriptContent -match "function Get-LocalIP") {
        Write-Host "✅ Função Get-LocalIP encontrada"
    }
    
    if ($scriptContent -match "param\(") {
        Write-Host "✅ Parâmetros definidos corretamente"
    }
    
    Write-Host "🎉 Todos os testes de sintaxe passaram!"
    
} catch {
    Write-Host "❌ Erro de sintaxe encontrado:"
    Write-Host $_.Exception.Message
    exit 1
}
