# Estrutura do Projeto - Tactical RMM Installation Scripts

Este documento descreve a estrutura completa do projeto de instalação do Tactical RMM.

## 📁 Estrutura de Arquivos

```
Tactical-RMM-Only-Install/
├── 📄 README.md                    # Documentação principal
├── 📄 STRUCTURE.md                 # Este arquivo (estrutura do projeto)
├── 🔧 Makefile                     # Automação de comandos
├── 🚀 install-tactical-only.sh     # Script principal de instalação
├── 🗑️  uninstall-tactical.sh       # Script de desinstalação
├── 📊 check-tactical-status.sh     # Script de verificação de status
├── ⚡ quick-install.sh             # Script de instalação rápida
├── 🎭 demo.sh                      # Script de demonstração
├── ⚙️  config.example               # Exemplo de arquivo de configuração
└── 📝 config                       # Arquivo de configuração (criado pelo usuário)
```

## 📋 Descrição dos Arquivos

### Scripts Principais

#### 🚀 `install-tactical-only.sh`
- **Propósito**: Script principal de instalação do Tactical RMM Agent
- **Características**:
  - Detecção automática de sistema e arquitetura
  - Modo interativo e automático
  - Suporte para Ubuntu 20.04, 22.04, 24.04 e Debian
  - Suporte para x86_64 e ARM (Raspberry Pi)
  - Download automático com retry
  - Verificação pós-instalação

#### 🗑️ `uninstall-tactical.sh`
- **Propósito**: Remoção completa do Tactical RMM Agent
- **Características**:
  - Remove serviços, arquivos e configurações
  - Remove mesh agent associado
  - Verificação de remoção completa
  - Modo seguro com confirmações

#### 📊 `check-tactical-status.sh`
- **Propósito**: Verificação e monitoramento do status
- **Características**:
  - Menu interativo com 8 opções
  - Verificação de serviços, processos e arquivos
  - Teste de conectividade
  - Visualização de logs
  - Monitoramento em tempo real

#### ⚡ `quick-install.sh`
- **Propósito**: Instalação automatizada usando arquivo de configuração
- **Características**:
  - Usa arquivo `config` para parâmetros
  - Instalação sem interação do usuário
  - Validação de configuração
  - Verificação automática pós-instalação

#### 🎭 `demo.sh`
- **Propósito**: Demonstração interativa do projeto
- **Características**:
  - Menu com 9 opções de demonstração
  - Testes de sintaxe
  - Simulação de instalação
  - Verificação de conectividade
  - Documentação interativa

### Arquivos de Configuração

#### ⚙️ `config.example`
- **Propósito**: Modelo de arquivo de configuração
- **Conteúdo**:
  - Configurações obrigatórias (CLIENT_ID, FILIAL)
  - Configurações avançadas (URLs, timeouts)
  - Exemplos e documentação inline
  - Validação de parâmetros

#### 📝 `config`
- **Propósito**: Arquivo de configuração do usuário
- **Criação**: Copiado de `config.example`
- **Uso**: Usado pelo `quick-install.sh`

### Automação

#### 🔧 `Makefile`
- **Propósito**: Automação de comandos comuns
- **Comandos disponíveis**:
  - `make setup` - Configuração inicial
  - `make install` - Instalação interativa
  - `make quick-install` - Instalação rápida
  - `make status` - Verificação de status
  - `make uninstall` - Desinstalação
  - `make config-edit` - Editar configuração
  - `make test-connectivity` - Testar conectividade
  - `make clean` - Limpar temporários

### Documentação

#### 📄 `README.md`
- **Propósito**: Documentação principal do projeto
- **Conteúdo**:
  - Instruções de instalação
  - Comandos do Makefile
  - Solução de problemas
  - Exemplos de uso

#### 📄 `STRUCTURE.md`
- **Propósito**: Este arquivo - estrutura do projeto
- **Conteúdo**:
  - Descrição de todos os arquivos
  - Fluxos de trabalho
  - Dependências entre scripts

## 🔄 Fluxos de Trabalho

### Fluxo 1: Instalação Rápida (Recomendado)
```
1. make setup
2. make config-edit
3. make quick-install
4. make status
```

### Fluxo 2: Instalação Manual
```
1. chmod +x *.sh
2. ./install-tactical-only.sh
3. ./check-tactical-status.sh
```

### Fluxo 3: Demonstração
```
1. chmod +x demo.sh
2. ./demo.sh
3. Navegar pelo menu interativo
```

### Fluxo 4: Desinstalação
```
1. make uninstall
   ou
2. ./uninstall-tactical.sh
```

## 🔗 Dependências entre Scripts

```
install-tactical-only.sh
├── Baixa rmmagent-linux.sh (externo)
├── Usa variáveis de ambiente (modo auto)
└── Chama verificações internas

quick-install.sh
├── Carrega config
├── Chama install-tactical-only.sh --auto
└── Opcionalmente chama check-tactical-status.sh

check-tactical-status.sh
├── Independente
└── Pode ser chamado por outros scripts

uninstall-tactical.sh
├── Independente
└── Remove tudo relacionado ao Tactical RMM

demo.sh
├── Independente
├── Testa sintaxe de outros scripts
└── Demonstra funcionalidades
```

## 🌐 URLs e Configurações

### Servidores NVirtual
- **Mesh Server**: `mesh.centralmesh.nvirtual.com.br`
- **API Server**: `api.centralmesh.nvirtual.com.br`
- **Auth Key**: Configurada nos scripts

### URLs de Download
- **Script RMM**: `https://raw.githubusercontent.com/netvolt/LinuxRMM-Script/main/rmmagent-linux.sh`
- **URLs alternativas**: Configuradas com fallback automático

## 🔒 Segurança

### Práticas Implementadas
- ✅ Scripts não executam como root
- ✅ Verificação de integridade de downloads
- ✅ URLs HTTPS para downloads
- ✅ Validação de parâmetros
- ✅ Confirmações para operações destrutivas

### Arquivos Sensíveis
- `config` - Contém IDs de cliente (não versionar)
- Scripts temporários em `/tmp` (removidos automaticamente)

## 🧪 Testes

### Testes Automáticos
- Verificação de sintaxe bash (`bash -n`)
- Teste de conectividade
- Validação de configuração

### Testes Manuais Recomendados
- Instalação em Ubuntu 20.04, 22.04, 24.04
- Instalação em Debian
- Teste em arquitetura ARM (Raspberry Pi)
- Verificação de desinstalação completa

## 📊 Logs e Monitoramento

### Locais de Log
- **Sistema**: `journalctl -u tacticalagent`
- **Tactical RMM**: Logs internos do agent
- **Scripts**: Output colorido em tempo real

### Comandos de Monitoramento
```bash
make logs              # Logs recentes
make monitor           # Tempo real
make status-services   # Status dos serviços
```

## 🔄 Atualizações e Manutenção

### Versionamento
- Scripts principais: Versão no cabeçalho
- Projeto: Versão no README.md
- Compatibilidade: Documentada por versão

### Manutenção
- Atualizar URLs se necessário
- Testar com novas versões do Ubuntu
- Atualizar documentação
- Revisar configurações de segurança

## 📞 Suporte

Para suporte técnico:
- Verificar logs: `make logs`
- Executar diagnóstico: `make status`
- Testar conectividade: `make test-connectivity`
- Consultar documentação: `README.md`

---

**Desenvolvido por Paulo Matheus - NVirtual**  
**Versão do Projeto: 1.0.0**  
**Data: 2025-01-21**
