#!/bin/bash

# Script simples de configuração automática para Raspberry Pi
# Este é um exemplo mais direto que executa o instalador do Zabbix automaticamente

set -e

# Cores para output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

log "=== Configuração Automática Raspberry Pi - NVirtual ==="

# Configurações fixas
ZABBIX_HOSTNAME="temporario"
TACTICAL_CLIENT_ID="1"
TACTICAL_CLIENT_FILIAL="1"

# Aguardar rede
log "Aguardando conectividade..."
for i in {1..30}; do
    if ping -c 1 ******* >/dev/null 2>&1; then
        break
    fi
    sleep 2
done

# Atualizar sistema
log "Atualizando sistema..."
sudo apt-get update -y

# Baixar e executar instalador do Zabbix
log "Baixando instalador do Zabbix..."
cd /tmp
wget -O install_zabbix.sh "https://raw.githubusercontent.com/paulomatheusgrr/nvirtual-projects/main/Zabbix-auto-instalation/install_zabbix_tactical_rmm.sh"
chmod +x install_zabbix.sh

# Executar com respostas automáticas
log "Executando instalação automática..."
{
    echo "$ZABBIX_HOSTNAME"    # Nome do proxy
    echo "$TACTICAL_CLIENT_ID" # ID do cliente
    echo "$TACTICAL_CLIENT_FILIAL" # Filial
    echo "dhcp"                # Manter DHCP
} | ./install_zabbix.sh

log "=== Configuração concluída ==="
