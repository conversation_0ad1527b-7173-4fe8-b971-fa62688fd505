# Script de teste básico para verificar funcionamento
# Sem caracteres especiais ou acentos

Write-Host "Iniciando teste basico..." -ForegroundColor Green
Write-Host "Data/Hora: $(Get-Date)" -ForegroundColor Cyan

# Teste simples de função
function Test-Function {
    param([string]$Message)
    Write-Host "Funcao executada: $Message" -ForegroundColor Yellow
}

Test-Function -Message "Teste OK"

Write-Host "Teste concluido com sucesso!" -ForegroundColor Green
