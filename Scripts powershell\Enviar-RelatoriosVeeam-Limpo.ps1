# Script para Enviar Relatórios Veeam ONE - Versão Limpa
# Criado para resolver problemas de codificação

param(
    [string]$ReportName = "",
    [switch]$ListReports = $false,
    [switch]$Help = $false
)

# Função para exibir mensagens coloridas
function Write-ColorLog {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

# Função para exibir ajuda
function Show-Help {
    Write-Host ""
    Write-Host "=== SCRIPT ENVIO RELATÓRIOS VEEAM ONE ===" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "USO:" -ForegroundColor Yellow
    Write-Host "  .\Enviar-RelatoriosVeeam-Limpo.ps1 -ReportName 'Nome do Relatório'"
    Write-Host "  .\Enviar-RelatoriosVeeam-Limpo.ps1 -ListReports"
    Write-Host "  .\Enviar-RelatoriosVeeam-Limpo.ps1 -Help"
    Write-Host ""
    Write-Host "EXEMPLOS:" -ForegroundColor Yellow
    Write-Host "  .\Enviar-RelatoriosVeeam-Limpo.ps1 -ReportName 'Backup Summary'"
    Write-Host "  .\Enviar-RelatoriosVeeam-Limpo.ps1 -ListReports"
    Write-Host ""
}

# Função para executar relatório
function Start-VeeamReport {
    param([string]$Name)
    
    Write-ColorLog "Tentando executar relatório: $Name" "Cyan"
    
    # Método 1: Tentar via Veeam PowerShell
    try {
        if (Get-Module -ListAvailable -Name Veeam.Backup.PowerShell) {
            Import-Module Veeam.Backup.PowerShell -ErrorAction SilentlyContinue
            
            $reportJob = Get-VBRReportJob | Where-Object { $_.Name -eq $Name }
            if ($reportJob) {
                Start-VBRReportJob -ReportJob $reportJob
                Write-ColorLog "Relatório '$Name' iniciado com sucesso!" "Green"
                return $true
            }
        }
    } catch {
        Write-ColorLog "Erro no método PowerShell: $($_.Exception.Message)" "Yellow"
    }
    
    # Método 2: Tentar via linha de comando
    try {
        $veeamPath = "C:\Program Files\Veeam\Backup and Replication\Backup\Veeam.Backup.Manager.exe"
        if (Test-Path $veeamPath) {
            $arguments = "-report `"$Name`""
            Start-Process -FilePath $veeamPath -ArgumentList $arguments -NoNewWindow -Wait
            Write-ColorLog "Relatório '$Name' executado via linha de comando!" "Green"
            return $true
        }
    } catch {
        Write-ColorLog "Erro no método linha de comando: $($_.Exception.Message)" "Yellow"
    }
    
    Write-ColorLog "Não foi possível executar o relatório '$Name'" "Red"
    return $false
}

# Função para listar relatórios
function Get-AvailableReports {
    Write-ColorLog "Buscando relatórios disponíveis..." "Cyan"
    
    $reports = @()
    
    try {
        if (Get-Module -ListAvailable -Name Veeam.Backup.PowerShell) {
            Import-Module Veeam.Backup.PowerShell -ErrorAction SilentlyContinue
            $veeamReports = Get-VBRReportJob
            
            foreach ($report in $veeamReports) {
                $reports += [PSCustomObject]@{
                    Name = $report.Name
                    Description = $report.Description
                    Type = "Veeam Report"
                }
            }
        }
    } catch {
        Write-ColorLog "Erro ao buscar relatórios Veeam: $($_.Exception.Message)" "Yellow"
    }
    
    if ($reports.Count -eq 0) {
        Write-ColorLog "Nenhum relatório encontrado ou módulo Veeam não disponível" "Yellow"
        
        # Lista de relatórios comuns como exemplo
        $commonReports = @(
            "Backup Summary",
            "Job Statistics",
            "VM Protection",
            "Repository Usage",
            "Tape Summary"
        )
        
        Write-ColorLog "Relatórios comuns que você pode tentar:" "Cyan"
        foreach ($report in $commonReports) {
            Write-Host "  - $report" -ForegroundColor White
        }
    } else {
        Write-ColorLog "Relatórios encontrados:" "Green"
        $reports | Format-Table -AutoSize
    }
    
    return $reports
}

# Função principal
function Main {
    Write-Host ""
    Write-Host "=== VEEAM ONE - ENVIO DE RELATÓRIOS ===" -ForegroundColor Cyan
    Write-Host ""
    
    if ($Help) {
        Show-Help
        return
    }
    
    if ($ListReports) {
        Get-AvailableReports
        return
    }
    
    if ([string]::IsNullOrEmpty($ReportName)) {
        Write-ColorLog "Nome do relatório não especificado!" "Red"
        Write-ColorLog "Use -Help para ver as opções disponíveis" "Yellow"
        return
    }
    
    Write-ColorLog "Iniciando execução do relatório: $ReportName" "Green"
    
    $success = Start-VeeamReport -Name $ReportName
    
    if ($success) {
        Write-ColorLog "Relatório executado com sucesso!" "Green"
    } else {
        Write-ColorLog "Falha na execução do relatório" "Red"
        Write-ColorLog "Verifique se o nome está correto e se o Veeam está instalado" "Yellow"
    }
    
    Write-Host ""
}

# Executar função principal
Main
