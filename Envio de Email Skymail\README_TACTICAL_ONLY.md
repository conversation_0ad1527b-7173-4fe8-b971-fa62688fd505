# 🤖 Instalação Automatizada - Tactical RMM Only

[![Vers<PERSON>](https://img.shields.io/badge/Versão-1.17.0-blue.svg)](https://github.com/nubium-cloud/Zabbix-Proxy-Instalation)
[![Ubuntu](https://img.shields.io/badge/Ubuntu-20.04%20|%2022.04%20|%2024.04-orange.svg)](https://ubuntu.com/)
[![Arquitetura](https://img.shields.io/badge/Arquitetura-x86__64%20|%20ARM-green.svg)](https://github.com/nubium-cloud/Zabbix-Proxy-Instalation)

Script dedicado para instalação **apenas do Tactical RMM** com suporte completo para múltiplas versões do Ubuntu e arquiteturas, incluindo configuração opcional de IP fixo.

## 📋 Compatibilidade

### ✅ Sistemas Operacionais Suportados
- **Ubuntu Server 20.04 LTS** (Focal Fossa)
- **Ubuntu Server 22.04 LTS** (Jammy Jellyfish)
- **Ubuntu Server 24.04 LTS** (Noble Numbat)

### ✅ Arquiteturas Suportadas
- **x86_64** (Intel/AMD 64-bit)
- **ARM64/aarch64** (Raspberry Pi 4, ARM servers)
- **ARMv7l** (Raspberry Pi 3)

## 🌟 Funcionalidades

### 🤖 **Instalação do Tactical RMM**
- ✅ **Tactical RMM Agent** via script oficial netvolt/LinuxRMM-Script
- ✅ **MeshAgent** para conectividade remota segura
- ✅ **Configuração automática** com servidores centralmesh.nvirtual.com.br
- ✅ **URLs específicas por arquitetura**:
  - x86_64: `installflags=2&meshinstall=6`
  - ARM: `installflags=0&meshinstall=26`

### 🔍 **Detecção Automática**
- ✅ **Versão do Ubuntu** (20.04/22.04/24.04)
- ✅ **Arquitetura do sistema** (x86_64/ARM)
- ✅ **Conexões SSH** (proteção contra perda de conexão)
- ✅ **Configuração de rede** atual

### 🌐 **Configuração de Rede (Opcional)**
- ✅ **IP fixo opcional** (pergunta durante instalação)
- ✅ **Detecção automática** de IP, Gateway e DNS
- ✅ **Verificação de conflitos** de IP
- ✅ **Proteção SSH** durante configuração de rede
- ✅ **Backup automático** de configurações

### 🛡️ **Firewall Automático**
- ✅ **UFW** com instalação automática se necessário
- ✅ **Fallback para iptables** se UFW não disponível
- ✅ **Portas liberadas**: 22 (SSH), 80 (HTTP), 443 (HTTPS)

## 🚀 Como Usar

### **Instalação Rápida**
```bash
# Download e execução
wget https://raw.githubusercontent.com/nubium-cloud/Zabbix-Proxy-Instalation/main/install_tactical_rmm_only.sh
chmod +x install_tactical_rmm_only.sh
./install_tactical_rmm_only.sh
```

### **Informações Solicitadas**
Durante a execução, o script solicitará:

1. **🆔 ID do Cliente (Tactical RMM)**
   - Identificador numérico único do cliente
   - Exemplo: `123`, `456`, `789`

2. **🏢 Filial do Cliente (Tactical RMM)**
   - Nome ou código da filial/localização
   - Exemplo: `MATRIZ`, `FILIAL01`, `SP`, `Rio de Janeiro`

3. **🌐 Configuração de IP Fixo (Opcional)**
   - Pergunta se deseja configurar IP fixo
   - Se sim, detecta rede atual e propõe IP .222
   - Permite escolher IP customizado
   - Opção de manter DHCP

## ⚙️ O que é Instalado

### **Tactical RMM Agent**
- Agent principal do Tactical RMM
- MeshAgent para acesso remoto
- Configuração automática com servidores NVirtual

### **Firewall Básico**
- UFW ou iptables configurado
- Portas essenciais liberadas
- Configuração segura por padrão

### **Configuração de Rede (Se Solicitado)**
- IP fixo com netplan
- Backup da configuração original
- Proteção para conexões SSH

## 🔌 Portas Configuradas

| Serviço | Porta | Protocolo | Descrição |
|---------|-------|-----------|-----------|
| **SSH** | 22 | TCP | Acesso remoto administrativo |
| **HTTP** | 80 | TCP | Tactical RMM (redirecionamento) |
| **HTTPS** | 443 | TCP | Tactical RMM (comunicação segura) |

## 🔧 Verificação da Instalação

```bash
# Verificar status dos serviços
sudo systemctl status tacticalagent
sudo systemctl status meshagent

# Verificar logs
sudo journalctl -u tacticalagent -n 50
sudo journalctl -u meshagent -n 50

# Testar conectividade
ping api.centralmesh.nvirtual.com.br
ping mesh.centralmesh.nvirtual.com.br
```

## ⚠️ Problemas Comuns

### **1. Tactical RMM Agent Não Conecta**
```bash
# Verificar se o agente está instalado
ls -la /opt/tacticalagent/

# Verificar se o MeshAgent está rodando
ps aux | grep meshagent

# Verificar logs do agente
sudo journalctl -u tacticalagent -n 50

# Reinstalar se necessário
cd /tmp
wget https://raw.githubusercontent.com/netvolt/LinuxRMM-Script/main/rmmagent-linux.sh
chmod +x rmmagent-linux.sh
# Execute com os parâmetros corretos para sua arquitetura
```

### **2. Problemas de Conectividade**
```bash
# Testar DNS
nslookup api.centralmesh.nvirtual.com.br
nslookup mesh.centralmesh.nvirtual.com.br

# Testar conectividade HTTPS
curl -I https://api.centralmesh.nvirtual.com.br
curl -I https://mesh.centralmesh.nvirtual.com.br

# Verificar firewall
sudo ufw status
```

### **3. Problemas com IP Fixo**
```bash
# Verificar configuração
cat /etc/netplan/01-netcfg.yaml

# Restaurar backup se necessário
sudo cp /etc/netplan/01-netcfg.yaml.backup.* /etc/netplan/01-netcfg.yaml
sudo netplan apply
```

## 🆘 Suporte SSH

O script detecta automaticamente conexões SSH e:
- ⚠️ **Avisa** sobre riscos de perda de conexão
- 📋 **Mostra** informações de reconexão
- ⏳ **Aplica** configurações de forma segura
- 🔗 **Fornece** instruções para reconexão

## 💾 Arquivos de Backup

O script cria backups automáticos:
```bash
# Configuração de rede (se IP fixo configurado)
/etc/netplan/01-netcfg.yaml.backup.[YYYYMMDD_HHMMSS]

# Verificar backups criados
ls -la /etc/netplan/*.backup.*
```

## 👨‍💻 Desenvolvido por
**Paulo Matheus** - NVirtual

---

**🤖 Script Tactical RMM Only v1.17.0**  
**✅ Compatível com Ubuntu 20.04/22.04/24.04 (x86_64/ARM)**  
**🔧 Instalação 100% automatizada com detecção inteligente**
