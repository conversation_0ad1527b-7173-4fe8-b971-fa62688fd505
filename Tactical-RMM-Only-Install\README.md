# Instalador Tactical RMM Agent - Apenas

Este é um script dedicado exclusivamente para a instalação do **Tactical RMM Agent** em sistemas Linux, separado do projeto Zabbix.

## 📋 Características

- ✅ **Instalação dedicada** apenas do Tactical RMM Agent
- ✅ **Suporte multi-arquitetura**: x86_64 e ARM (Raspberry Pi)
- ✅ **Suporte multi-sistema**: Ubuntu 20.04, 22.04, 24.04 e Debian
- ✅ **Download automático** com retry e URLs alternativas
- ✅ **Verificação de status** do serviço após instalação
- ✅ **Interface interativa** para configuração
- ✅ **Logs coloridos** para melhor visualização

## 🚀 Como usar

### Método 1: Instalação Rápida com Makefile (Recomendado)

```bash
# 1. Clonar ou baixar todos os arquivos do projeto
git clone <repo-url> Tactical-RMM-Only-Install
cd Tactical-RMM-Only-Install

# 2. Configurar o ambiente
make setup

# 3. Editar o arquivo de configuração
make config-edit

# 4. Executar instalação rápida
make quick-install
```

### Método 2: Instalação Manual

```bash
# 1. Baixar o script principal
wget https://raw.githubusercontent.com/seu-repo/Tactical-RMM-Only-Install/main/install-tactical-only.sh

# 2. Dar permissão de execução
chmod +x install-tactical-only.sh

# 3. Executar o script (NÃO como root)
./install-tactical-only.sh
```

### 3. Informações solicitadas (Método Manual)

O script irá solicitar:
- **ID do Cliente**: Identificador numérico do cliente no Tactical RMM
- **Filial do Cliente**: Nome ou identificador da filial

## 🛠️ Comandos do Makefile

```bash
make help              # Mostrar ajuda
make setup             # Configurar ambiente inicial
make install           # Instalação interativa
make quick-install     # Instalação rápida (usa arquivo config)
make status            # Verificar status completo
make status-services   # Verificar apenas serviços
make logs              # Mostrar logs recentes
make monitor           # Monitoramento em tempo real
make uninstall         # Desinstalar Tactical RMM
make config-edit       # Editar configuração
make config-validate   # Validar configuração
make test-connectivity # Testar conectividade
make clean             # Limpar arquivos temporários
```

## 📋 Pré-requisitos

- Sistema Linux (Ubuntu 20.04+, Debian)
- Usuário com privilégios sudo
- Conexão com a internet
- **NÃO executar como root**

## 🔧 O que o script faz

1. **Detecta automaticamente**:
   - Versão do sistema operacional
   - Arquitetura do processador (x86_64 ou ARM)

2. **Instala dependências básicas**:
   - curl, wget, unzip, build-essential

3. **Baixa o script do Tactical RMM**:
   - Tenta múltiplas URLs com retry automático
   - Verifica integridade do arquivo baixado

4. **Instala o Tactical RMM Agent**:
   - Configura automaticamente para a arquitetura detectada
   - Usa as URLs corretas para ARM ou x86_64

5. **Verifica a instalação**:
   - Confirma se o serviço está rodando
   - Fornece comandos úteis para monitoramento

## 🌐 Configurações da NVirtual

O script está pré-configurado com:
- **Mesh Server**: `mesh.centralmesh.nvirtual.com.br`
- **API Server**: `api.centralmesh.nvirtual.com.br`
- **Auth Key**: Chave de autenticação da NVirtual

## 📊 Comandos úteis após instalação

```bash
# Verificar status do serviço
sudo systemctl status tacticalagent

# Ver logs em tempo real
sudo journalctl -u tacticalagent -f

# Reiniciar o serviço
sudo systemctl restart tacticalagent

# Parar o serviço
sudo systemctl stop tacticalagent

# Iniciar o serviço
sudo systemctl start tacticalagent
```

## 🔍 Solução de problemas

### Serviço não está rodando
```bash
# Verificar logs detalhados
sudo journalctl -u tacticalagent -n 50

# Verificar se o processo existe
ps aux | grep tactical

# Tentar reinstalar
sudo systemctl stop tacticalagent
sudo apt remove --purge tacticalagent
./install-tactical-only.sh
```

### Problemas de conectividade
```bash
# Testar conectividade com os servidores
curl -I https://mesh.centralmesh.nvirtual.com.br
curl -I https://api.centralmesh.nvirtual.com.br

# Verificar DNS
nslookup mesh.centralmesh.nvirtual.com.br
```

### Problemas de download
```bash
# Verificar conectividade com GitHub
curl -I https://github.com

# Baixar manualmente se necessário
wget https://raw.githubusercontent.com/netvolt/LinuxRMM-Script/main/rmmagent-linux.sh
chmod +x rmmagent-linux.sh
```

## 📁 Estrutura do projeto

```
Tactical-RMM-Only-Install/
├── install-tactical-only.sh    # Script principal de instalação
├── README.md                   # Este arquivo
└── logs/                       # Logs de instalação (criado automaticamente)
```

## 🔒 Segurança

- O script **não deve ser executado como root**
- Usa sudo apenas quando necessário
- Verifica integridade dos arquivos baixados
- URLs HTTPS para downloads seguros

## 📝 Logs

O script gera logs coloridos em tempo real:
- 🟢 **Verde**: Operações bem-sucedidas
- 🟡 **Amarelo**: Avisos
- 🔵 **Azul**: Informações
- 🔴 **Vermelho**: Erros

## 👨‍💻 Autor

**Paulo Matheus - NVirtual**
- Versão: 1.0.0
- Data: 2025-01-21

## 📄 Licença

Este script é propriedade da NVirtual e destinado ao uso interno da empresa.

## 🆘 Suporte

Para suporte técnico, entre em contato com a equipe de TI da NVirtual.
