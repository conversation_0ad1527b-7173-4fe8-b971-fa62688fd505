@import '../../Styles/variables';
@import '../../Styles/responsive';

.dashboard__wrapper {
    height: 100vh;
    width: 100%;
    overflow: hidden;

    .dashboard__content {
        position: relative;
        background-color: #323030;

        .content__sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            width: 70px;
            background-color: #fff;
            border-radius: 0 30px 0 0;
            padding: 0;
            z-index: 3;
            transition: width .2s cubic-bezier(0.075, 0.82, 0.165, 1);

            &.side__full__xxs{
                display: block;
                width: 100px;
                box-shadow: 5px 2px 10px #6b6b6b66;
                z-index: 40;
            }

            .close__xxs-side{
                position: absolute;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
            }
            
            @media(max-height: 720px){
                overflow-x: hidden;
                overflow-y: auto;
            }

            @include sm{
                width: 253px;
                padding: 20px 40px;
            }   

            @include md{
                width: 300px;
            }

            @include max-xxs{
                display: none;
            }

            &.side__closed{
                width: 70px;
                padding: 0;
                border-radius: 0 20px 0 0;
                transition: width .5s cubic-bezier(0.175, 0.885, 0.32, 1.275);

                .sidebar__logo{
                    img{
                        width: 40px;
                        display: block;
                        margin: 0 auto;
                        margin-top: 5px;
                    }
                }

                .menu__toggle{
                    top: auto;
                    bottom: 10px;
                    outline: none;
                }
            }

            .menu__toggle{
                z-index: 3;
                outline: none;
                position: absolute;
                top: 116px;
                right: -19px;
                padding: 10px;
                background: #fff;
                border-radius: 50%;
                box-shadow: 2px 2px 2px rgba($color: #000000, $alpha: 0.10);
                cursor: pointer;
                display: none;

                &:hover{
                    box-shadow: 4px 4px 4px rgba($color: #000000, $alpha: 0.20);

                    svg{
                        transform: scale(1.02);
                    }
                }

                @include sm{
                    display: block;
                }
            }

            .sidebar__logo{
                img{
                    width: 66px;
                    margin-left: -11px;
                }

                @include max-sm{
                    img{
                        width: 40px;
                        display: block;
                        margin: 0 auto;
                        margin-top: 5px;
                    }
                }
            }
        }

        .content__main {
            overflow-x: auto;
            margin-left: 70px;
            background-color: #F4F5F9;
            height: 100vh;
            width: calc(100% - 70px);
            position: relative;
            // padding: 30px 40px;

            &.side__closed{
                margin-left: 70px;
                width: calc(100% - 50px);
            }

            @include max-xxs{
                width: 100%;
                margin-left: 0;
            }

            .main__childs{
                padding: 30px 40px;
                position: relative;

                @include max-xxs{
                    padding: 30px 10px;
                }
            }

            .open__side__xxs {
                cursor: pointer;
                position: absolute;
                top: 20px;
                left: 20px;
                display: none;

                @include max-xxs {
                    display: block;
                }
            }

            .close__side__xxs {
                cursor: pointer;
                position: absolute;
                top: 20px;
                right: 20px;
                display: none;

                @include max-xxs {
                    display: block;
                }
            }

            // @include max-xxs {
            //     padding: 30px 10px;
            //     margin-left: 0 !important;
            //     width: 100% !important;
            // }

            @include sm {
                margin-left: 250px;
                width: calc(100% - 250px);
            }

            @include md {
                margin-left: 300px;
                width: calc(100% - 300px);
            }
        }
    }
}

.sidebar__menu{
    &.side__closed{
        .side__block{
            margin: 2px 0;

            span{
                display: none;
            }
        }
    }
}