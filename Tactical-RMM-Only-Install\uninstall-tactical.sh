#!/bin/bash

# Script de desinstalação do Tactical RMM Agent
# Autor: <PERSON> - NVirtual
# Data: 2025-01-21
# Versão: 1.0.0

set -e  # Para o script em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Banner
echo "
                                                                                                           
                      ####                                                                                  
    ################# ####                                                                                  
   #                  ####                                                                                   
   ##  ####       ###  ## ####         #### ### ############ ############        ###     ####       ###         
   ##  ######     ###  ##  ####       ####  ### ###     #####   ###   ###        ###    ######      ###         
   ##  ########   ###  ##   ####     ###    ### ###       ###   ###   ###        ###   ### ####     ###         
   ##  ###  ##### ###  ##    ####   ###     ### ###    ######   ###   ###        ###  ###   ####    ###         
   ##  ###    #######  ##      #######      ### ###    #####    ###   ###       #### ###     ####   ###         
   ##  ###      #####  ##       #####       ### ###     #####   ###    ############ ###        ###  ########### 
   ##  ###        ###  ##        ###        ### ###       ####  ###     ########## ###          ### ########### 
  ####                 #                                                                       
  #### ################                                                                              
  ####                                                                                             
                                                                                                           
"
log "Desinstalador Tactical RMM Agent - NVirtual"
log "Desenvolvido por Paulo Matheus"
log "Versão: 1.0.0"
echo

# Verificar se está rodando como root
if [[ $EUID -eq 0 ]]; then
    error "Este script não deve ser executado como root. Execute como usuário normal com sudo."
fi

# Verificar se sudo está disponível
if ! command -v sudo &> /dev/null; then
    error "sudo não está instalado."
fi

# Confirmar desinstalação
warning "⚠️  ATENÇÃO: Este script irá remover completamente o Tactical RMM Agent do sistema!"
echo
info "O que será removido:"
info "• Serviço tacticalagent/rmmagent"
info "• Arquivos de configuração"
info "• Executáveis do agent"
info "• Mesh agent (se instalado)"
echo

read -p "Tem certeza que deseja continuar? (digite 'CONFIRMO' para prosseguir): " CONFIRM
if [[ "$CONFIRM" != "CONFIRMO" ]]; then
    log "Desinstalação cancelada pelo usuário."
    exit 0
fi

echo
log "Iniciando desinstalação do Tactical RMM Agent..."

# Função para parar serviços
stop_services() {
    log "Parando serviços do Tactical RMM..."
    
    # Lista de possíveis nomes de serviços
    local services=("tacticalagent" "rmmagent" "meshagent")
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service" 2>/dev/null; then
            info "Parando serviço: $service"
            sudo systemctl stop "$service" 2>/dev/null || true
            sudo systemctl disable "$service" 2>/dev/null || true
        fi
    done
}

# Função para remover arquivos do sistema
remove_files() {
    log "Removendo arquivos do Tactical RMM..."
    
    # Lista de possíveis localizações de arquivos
    local files_and_dirs=(
        "/usr/local/bin/tacticalagent"
        "/usr/local/bin/rmmagent"
        "/opt/tacticalagent"
        "/opt/rmmagent"
        "/etc/systemd/system/tacticalagent.service"
        "/etc/systemd/system/rmmagent.service"
        "/var/log/tacticalagent"
        "/var/log/rmmagent"
        "/tmp/tacticalagent"
        "/tmp/rmmagent"
        "/home/<USER>/tacticalagent"
        "/root/tacticalagent"
    )
    
    for item in "${files_and_dirs[@]}"; do
        if [[ -e "$item" ]] || [[ -L "$item" ]]; then
            info "Removendo: $item"
            sudo rm -rf "$item" 2>/dev/null || true
        fi
    done
}

# Função para remover mesh agent
remove_mesh_agent() {
    log "Verificando e removendo Mesh Agent..."
    
    # Possíveis localizações do mesh agent
    local mesh_locations=(
        "/usr/local/mesh_services"
        "/usr/local/mesh"
        "/opt/mesh"
        "/etc/systemd/system/meshagent.service"
    )
    
    # Parar serviço mesh se existir
    if systemctl is-active --quiet meshagent 2>/dev/null; then
        info "Parando serviço meshagent"
        sudo systemctl stop meshagent 2>/dev/null || true
        sudo systemctl disable meshagent 2>/dev/null || true
    fi
    
    # Remover arquivos do mesh
    for location in "${mesh_locations[@]}"; do
        if [[ -e "$location" ]]; then
            info "Removendo mesh: $location"
            sudo rm -rf "$location" 2>/dev/null || true
        fi
    done
    
    # Procurar por processos mesh em execução
    local mesh_processes=$(ps aux | grep -i mesh | grep -v grep | awk '{print $2}' || true)
    if [[ -n "$mesh_processes" ]]; then
        warning "Finalizando processos mesh em execução..."
        echo "$mesh_processes" | while read -r pid; do
            if [[ -n "$pid" ]]; then
                info "Finalizando processo: $pid"
                sudo kill -9 "$pid" 2>/dev/null || true
            fi
        done
    fi
}

# Função para limpar processos em execução
kill_processes() {
    log "Verificando processos em execução..."
    
    # Lista de processos relacionados ao Tactical RMM
    local process_names=("tacticalagent" "rmmagent" "meshagent")
    
    for process in "${process_names[@]}"; do
        local pids=$(pgrep "$process" 2>/dev/null || true)
        if [[ -n "$pids" ]]; then
            warning "Finalizando processos $process: $pids"
            echo "$pids" | while read -r pid; do
                if [[ -n "$pid" ]]; then
                    sudo kill -TERM "$pid" 2>/dev/null || true
                    sleep 2
                    # Se ainda estiver rodando, forçar
                    if kill -0 "$pid" 2>/dev/null; then
                        sudo kill -9 "$pid" 2>/dev/null || true
                    fi
                fi
            done
        fi
    done
}

# Função para recarregar systemd
reload_systemd() {
    log "Recarregando configuração do systemd..."
    sudo systemctl daemon-reload
}

# Função para verificar se a desinstalação foi bem-sucedida
verify_removal() {
    log "Verificando se a desinstalação foi bem-sucedida..."
    
    local found_issues=false
    
    # Verificar serviços
    local services=("tacticalagent" "rmmagent" "meshagent")
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service" 2>/dev/null; then
            warning "⚠️ Serviço $service ainda está ativo"
            found_issues=true
        fi
    done
    
    # Verificar processos
    local processes=("tacticalagent" "rmmagent" "meshagent")
    for process in "${processes[@]}"; do
        if pgrep "$process" >/dev/null 2>&1; then
            warning "⚠️ Processo $process ainda está em execução"
            found_issues=true
        fi
    done
    
    # Verificar arquivos principais
    local main_files=("/usr/local/bin/tacticalagent" "/usr/local/bin/rmmagent" "/opt/tacticalagent")
    for file in "${main_files[@]}"; do
        if [[ -e "$file" ]]; then
            warning "⚠️ Arquivo $file ainda existe"
            found_issues=true
        fi
    done
    
    if [[ "$found_issues" == "false" ]]; then
        log "✅ Desinstalação verificada com sucesso!"
    else
        warning "⚠️ Alguns componentes podem não ter sido removidos completamente"
        info "Execute os comandos de verificação manual listados no final"
    fi
}

# Executar desinstalação
stop_services
kill_processes
remove_files
remove_mesh_agent
reload_systemd
verify_removal

# Resumo final
echo
log "Desinstalação concluída!"
echo "=========================================="
echo "RESUMO DA DESINSTALAÇÃO"
echo "=========================================="
echo "Componentes removidos:"
echo "• Serviços tacticalagent/rmmagent"
echo "• Arquivos de configuração"
echo "• Executáveis do agent"
echo "• Mesh agent"
echo "• Logs do sistema"
echo
echo "Comandos para verificação manual:"
echo "• Verificar serviços: sudo systemctl list-units | grep -i tactical"
echo "• Verificar processos: ps aux | grep -i tactical"
echo "• Verificar arquivos: find /usr /opt /etc -name '*tactical*' 2>/dev/null"
echo "• Verificar mesh: ps aux | grep -i mesh"
echo
echo "Se encontrar restos do sistema:"
echo "• Parar serviços: sudo systemctl stop <nome-do-servico>"
echo "• Remover arquivos: sudo rm -rf <caminho-do-arquivo>"
echo "• Finalizar processos: sudo kill -9 <PID>"
echo
echo "Desenvolvido por: Paulo Matheus - NVirtual"
echo "=========================================="

log "Script de desinstalação finalizado!"
