# 🚀 Tactical-Inventory-Complete.ps1

**Script único e completo** para execução no servidor Tactical RMM (Ubuntu Server) que gera inventário com anexo Excel e envia por email automaticamente.

## 🎯 Características Principais

### ✅ **Script Auto-Contido**
- **Tudo em um arquivo único** - não precisa de arquivos externos
- **Instalação automática** do módulo ImportExcel se necessário
- **Verificação automática** de pré-requisitos
- **Limpeza automática** de arquivos temporários
- **Logs detalhados** com cores para fácil acompanhamento

### 📊 **Funcionalidades Completas**
- **Coleta dados** via API Tactical RMM
- **Gera arquivo Excel** com múltiplas abas (uma por site)
- **Cria aba de resumo** com estatísticas consolidadas
- **Envia email HTML** profissional com anexo Excel
- **Suporte a múltiplos destinatários**
- **Filtros configuráveis** (incluir/excluir offline)

## 📋 Parâmetros

### **Obrigatórios:**
| Parâmetro | Tipo | Descrição |
|-----------|------|-----------|
| `ClientId` | int | ID do cliente no Tactical RMM |
| `EmailTo` | string | Email(s) de destino (separados por vírgula) |
| `SMTPUser` | string | Usuário para autenticação SMTP |
| `SMTPPassword` | string | Senha para autenticação SMTP |

### **Opcionais:**
| Parâmetro | Tipo | Padrão | Descrição |
|-----------|------|---------|-----------|
| `EmailSubject` | string | Auto-gerado | Assunto do email |
| `SMTPServer` | string | smtp.gmail.com | Servidor SMTP |
| `SMTPPort` | int | 587 | Porta SMTP |
| `IncludeOffline` | bool | true | Incluir estações offline |
| `OutputPath` | string | /tmp | Diretório temporário |

## 🚀 Como Usar

### **1. Execução Básica**
```bash
pwsh Tactical-Inventory-Complete.ps1 \
  -ClientId 8 \
  -EmailTo "<EMAIL>" \
  -SMTPUser "<EMAIL>" \
  -SMTPPassword "senha_app_gmail"
```

### **2. Múltiplos Destinatários**
```bash
pwsh Tactical-Inventory-Complete.ps1 \
  -ClientId 8 \
  -EmailTo "<EMAIL>,<EMAIL>,<EMAIL>" \
  -SMTPUser "<EMAIL>" \
  -SMTPPassword "senha123"
```

### **3. Configurações Personalizadas**
```bash
pwsh Tactical-Inventory-Complete.ps1 \
  -ClientId 8 \
  -EmailTo "<EMAIL>" \
  -SMTPUser "<EMAIL>" \
  -SMTPPassword "senha123" \
  -SMTPServer "smtp.office365.com" \
  -SMTPPort 587 \
  -EmailSubject "Relatório Mensal - Janeiro 2025" \
  -IncludeOffline false
```

### **4. Apenas Estações Online**
```bash
pwsh Tactical-Inventory-Complete.ps1 \
  -ClientId 8 \
  -EmailTo "<EMAIL>" \
  -SMTPUser "<EMAIL>" \
  -SMTPPassword "senha123" \
  -IncludeOffline false \
  -EmailSubject "Monitoramento - Estações Online"
```

## 🏗️ Configuração no Servidor Tactical RMM

### **1. Salvar Script no Tactical RMM**
1. Acesse o servidor Tactical RMM
2. Vá para **Settings → Script Manager**
3. Clique em **Add Script**
4. Configure:
   - **Name:** `Inventário Completo com Excel`
   - **Description:** `Gera inventário completo e envia por email com anexo Excel`
   - **Category:** `Inventory`
   - **Script Type:** `PowerShell`
   - **Shell:** `powershell` (será executado com pwsh no Linux)
   - **Timeout:** `600` (10 minutos)

5. Cole todo o conteúdo do arquivo `Tactical-Inventory-Complete.ps1`
6. Clique em **Save**

### **2. Configurar Argumentos**
No campo **Arguments** do script, use:

**Exemplo básico:**
```
-ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "sua_senha_aqui"
```

**Exemplo avançado:**
```
-ClientId 8 -EmailTo "<EMAIL>,<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "sua_senha_aqui" -EmailSubject "Relatório Semanal" -IncludeOffline false
```

### **3. Executar Script**

#### **Execução Manual:**
1. Vá para **Agents**
2. Selecione qualquer agente
3. **Tasks → Run Script**
4. Selecione **Inventário Completo com Excel**
5. Os argumentos já estarão preenchidos
6. Clique em **Run Now**

#### **Execução Agendada:**
1. **Automation Manager → Add Task**
2. Configure:
   - **Task Type:** `Run Script`
   - **Name:** `Relatório Diário Cliente 8`
   - **Script:** `Inventário Completo com Excel`
   - **Arguments:** (já configurados no script)
   - **Schedule:** Daily, 8:00 AM
   - **Run on:** Any Agent

## 📊 Conteúdo do Relatório

### **📧 Email HTML**
- **Cabeçalho elegante** com gradiente e informações do servidor
- **Cards estatísticos** com cores diferenciadas:
  - 🔵 Total de Estações
  - 🟢 Online
  - 🟡 Recente  
  - 🔴 Offline
  - 🟣 Sites
- **Tabelas por site** com dados organizados
- **Design responsivo** e profissional
- **Nota destacada** sobre o anexo Excel

### **📎 Anexo Excel**
- **Múltiplas abas** (uma para cada site)
- **Aba "Resumo_Geral"** com estatísticas consolidadas:
  - Total de estações por site
  - Contadores de status (Online/Offline/Recente)
  - Soma de falhas de serviços e checks
  - Total de RAM por site
- **Formatação automática:**
  - AutoSize (colunas ajustadas automaticamente)
  - AutoFilter (filtros nas colunas)
  - FreezeTopRow (primeira linha fixa)
  - BoldTopRow (cabeçalho em negrito)

### **📋 Dados Incluídos**
- ID do Agente, Hostname, Cliente, Site
- Status de conectividade com timestamp
- Sistema operacional e arquitetura
- Informações de hardware (CPU, RAM)
- IP público e versão do agente
- Usuário logado e domínio
- Falhas de serviços e checks
- Modo de manutenção
- Data de instalação e observações

## 🔧 Configurações SMTP

### **Gmail/Google Workspace**
```bash
-SMTPServer "smtp.gmail.com" \
-SMTPPort 587 \
-SMTPUser "<EMAIL>" \
-SMTPPassword "senha_de_aplicativo"
```

### **Office 365/Outlook**
```bash
-SMTPServer "smtp.office365.com" \
-SMTPPort 587 \
-SMTPUser "<EMAIL>" \
-SMTPPassword "sua_senha"
```

### **Servidor SMTP Personalizado**
```bash
-SMTPServer "mail.empresa.com" \
-SMTPPort 587 \
-SMTPUser "<EMAIL>" \
-SMTPPassword "senha_smtp"
```

## 📝 Logs de Execução

O script gera logs detalhados e coloridos:

```
[2025-01-03 10:30:15] [SUCCESS] === INICIANDO RELATÓRIO DE INVENTÁRIO TACTICAL RMM ===
[2025-01-03 10:30:15] [INFO] Cliente ID: 8
[2025-01-03 10:30:15] [INFO] Email(s): <EMAIL>
[2025-01-03 10:30:15] [INFO] Servidor: tactical-server
[2025-01-03 10:30:16] [SUCCESS] Módulo ImportExcel já instalado: v7.8.6
[2025-01-03 10:30:17] [SUCCESS] agentes obtidos: 45 registros
[2025-01-03 10:30:18] [SUCCESS] Cliente encontrado: Sumire
[2025-01-03 10:30:18] [INFO] Agentes do cliente: 12
[2025-01-03 10:30:19] [SUCCESS] === ESTATÍSTICAS DO RELATÓRIO ===
[2025-01-03 10:30:19] [SUCCESS] Cliente: Sumire
[2025-01-03 10:30:19] [SUCCESS] Total de estações: 12
[2025-01-03 10:30:19] [SUCCESS] Online: 8
[2025-01-03 10:30:19] [SUCCESS] Recente: 2
[2025-01-03 10:30:19] [SUCCESS] Offline: 2
[2025-01-03 10:30:19] [SUCCESS] Sites: 3
[2025-01-03 10:30:20] [SUCCESS] Arquivo Excel criado: /tmp/tactical-inventory-20250103103015/TacticalRMM_Inventario_Sumire_20250103_103020.xlsx
[2025-01-03 10:30:21] [INFO] Preparando envio de email...
[2025-01-03 10:30:21] [INFO] Destinatário(s): <EMAIL>
[2025-01-03 10:30:21] [INFO] Tamanho do anexo: 15.7 KB
[2025-01-03 10:30:23] [SUCCESS] Email enviado com sucesso!
[2025-01-03 10:30:23] [SUCCESS] === RELATÓRIO ENVIADO COM SUCESSO ===
[2025-01-03 10:30:23] [SUCCESS] Arquivos temporários removidos
[2025-01-03 10:30:23] [SUCCESS] === EXECUÇÃO CONCLUÍDA COM SUCESSO ===
```

## ⚠️ Solução de Problemas

### **Erro: "PowerShell Core necessário"**
- O script detectou que não está rodando no PowerShell Core
- Verifique se o Tactical RMM está configurado para usar `pwsh`

### **Erro: "Módulo ImportExcel não pode ser instalado"**
- Execute manualmente no servidor: `pwsh -c "Install-Module ImportExcel -Force -Scope CurrentUser"`
- Verifique conectividade com PowerShell Gallery

### **Erro: "Cliente não encontrado"**
- Verifique se o ClientId está correto
- Teste a API manualmente: `curl -H "X-API-KEY: TOKEN" https://api.centralmesh.nvirtual.com.br/clients/`

### **Erro: "Falha no envio de email"**
- Verifique credenciais SMTP
- Para Gmail: use senha de aplicativo, não a senha normal
- Teste conectividade: `telnet smtp.gmail.com 587`

### **Timeout do Script**
- Aumente o timeout no Tactical RMM para 600 segundos (10 minutos)
- Use `-IncludeOffline false` para reduzir processamento

## 🎯 Casos de Uso

### **1. Relatório Diário Automático**
- Configurar task diária às 8h
- Usar `-IncludeOffline false` para monitoramento
- Enviar para equipe de suporte

### **2. Relatório Semanal Completo**
- Configurar task semanal às segundas 7h
- Incluir todas as estações
- Enviar para gerência

### **3. Relatório Mensal Detalhado**
- Configurar task mensal no dia 1º
- Assunto personalizado com mês/ano
- Enviar para múltiplos destinatários

### **4. Monitoramento Contínuo**
- Configurar task a cada 4 horas
- Apenas estações online
- Alertas para equipe técnica

---

**Desenvolvido por NVirtual** 🚀  
*Script Completo v1.0 - Janeiro 2025*
