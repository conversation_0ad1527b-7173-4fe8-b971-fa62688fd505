<#
.SYNOPSIS
    Script alternativo para envio manual dos relatórios do Veeam ONE usando cmdlets PowerShell

.DESCRIPTION
    Este script utiliza os cmdlets PowerShell nativos do Veeam para disparar manualmente 
    os relatórios que estão configurados no "Scheduling" do Veeam ONE.
    
    Funciona como alternativa ao script baseado em API REST, utilizando comandos diretos
    do PowerShell do Veeam.

.PARAMETER ReportName
    Nome específico do relatório para executar (opcional)

.PARAMETER ExecuteAll
    Switch para executar todos os relatórios agendados

.PARAMETER ListOnly
    Switch para apenas listar os relatórios disponíveis

.PARAMETER VeeamOneServer
    Servidor do Veeam ONE para conectar (padrão: localhost)

.EXAMPLE
    .\Enviar-RelatoriosVeeamONE-Cmdlets.ps1 -ListOnly
    Lista todos os relatórios agendados

.EXAMPLE
    .\Enviar-RelatoriosVeeamONE-Cmdlets.ps1 -ReportName "Backup Status"
    Executa um relatório específico

.EXAMPLE
    .\Enviar-RelatoriosVeeamONE-Cmdlets.ps1 -ExecuteAll
    Executa todos os relatórios agendados

.NOTES
    Autor: Paulo Matheus - NVirtual
    Data: $(Get-Date -Format "dd/MM/yyyy")
    Versão: 1.0
    
    Requisitos:
    - Veeam ONE instalado
    - Módulo PowerShell do Veeam carregado
    - Permissões adequadas para executar cmdlets do Veeam
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$ReportName,
    
    [Parameter(Mandatory = $false)]
    [switch]$ExecuteAll,
    
    [Parameter(Mandatory = $false)]
    [switch]$ListOnly,
    
    [Parameter(Mandatory = $false)]
    [string]$VeeamOneServer = "localhost"
)

# Configurações
$ErrorActionPreference = "Stop"

#region Funções Auxiliares

function Write-Log {
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [ValidateSet("INFO", "WARNING", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "INFO" { "White" }
        "WARNING" { "Yellow" }
        "ERROR" { "Red" }
        "SUCCESS" { "Green" }
    }
    
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Initialize-VeeamPowerShell {
    try {
        Write-Log "Carregando módulos PowerShell do Veeam..." -Level "INFO"
        
        # Tentar carregar o snap-in do Veeam Backup & Replication
        if (Get-PSSnapin -Name VeeamPSSnapin -ErrorAction SilentlyContinue) {
            Write-Log "Snap-in VeeamPSSnapin já carregado" -Level "INFO"
        } else {
            Add-PSSnapin VeeamPSSnapin -ErrorAction SilentlyContinue
            if (Get-PSSnapin -Name VeeamPSSnapin -ErrorAction SilentlyContinue) {
                Write-Log "Snap-in VeeamPSSnapin carregado com sucesso" -Level "SUCCESS"
            }
        }
        
        # Tentar carregar módulos do Veeam ONE
        $veeamOnePath = "${env:ProgramFiles}\Veeam\Veeam ONE\PowerShell"
        if (Test-Path $veeamOnePath) {
            $moduleFiles = Get-ChildItem -Path $veeamOnePath -Filter "*.psm1" -ErrorAction SilentlyContinue
            foreach ($module in $moduleFiles) {
                try {
                    Import-Module $module.FullName -Force -ErrorAction SilentlyContinue
                    Write-Log "Módulo $($module.Name) carregado" -Level "SUCCESS"
                } catch {
                    Write-Log "Não foi possível carregar módulo $($module.Name)" -Level "WARNING"
                }
            }
        }
        
        # Verificar se temos cmdlets do Veeam disponíveis
        $veeamCmdlets = Get-Command -Module "*Veeam*" -ErrorAction SilentlyContinue
        if ($veeamCmdlets) {
            Write-Log "Encontrados $($veeamCmdlets.Count) cmdlets do Veeam disponíveis" -Level "SUCCESS"
            return $true
        } else {
            Write-Log "Nenhum cmdlet do Veeam encontrado" -Level "WARNING"
            return $false
        }
    }
    catch {
        Write-Log "Erro ao inicializar PowerShell do Veeam: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

function Connect-VeeamServer {
    param(
        [Parameter(Mandatory = $false)]
        [string]$Server = $VeeamOneServer
    )
    
    try {
        Write-Log "Conectando ao servidor Veeam: $Server" -Level "INFO"
        
        # Tentar conectar usando diferentes métodos dependendo dos cmdlets disponíveis
        if (Get-Command "Connect-VBRServer" -ErrorAction SilentlyContinue) {
            Connect-VBRServer -Server $Server
            Write-Log "Conectado ao Veeam Backup & Replication Server" -Level "SUCCESS"
            return $true
        }
        
        if (Get-Command "Connect-VeeamONE" -ErrorAction SilentlyContinue) {
            Connect-VeeamONE -Server $Server
            Write-Log "Conectado ao Veeam ONE Server" -Level "SUCCESS"
            return $true
        }
        
        Write-Log "Nenhum cmdlet de conexão encontrado. Tentando execução local..." -Level "WARNING"
        return $true
    }
    catch {
        Write-Log "Erro ao conectar ao servidor Veeam: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

function Get-VeeamReports {
    try {
        Write-Log "Obtendo lista de relatórios do Veeam..." -Level "INFO"
        
        $reports = @()
        
        # Método 1: Tentar usar cmdlets específicos do Veeam ONE
        if (Get-Command "Get-VeeamONEReport" -ErrorAction SilentlyContinue) {
            $reports = Get-VeeamONEReport
        }
        # Método 2: Tentar usar cmdlets do Veeam B&R
        elseif (Get-Command "Get-VBRReportJob" -ErrorAction SilentlyContinue) {
            $reports = Get-VBRReportJob
        }
        # Método 3: Buscar na configuração do Veeam ONE
        else {
            Write-Log "Tentando localizar relatórios através do registro/configuração..." -Level "INFO"
            
            # Buscar no registro do Windows por configurações do Veeam ONE
            $veeamOneRegPath = "HKLM:\SOFTWARE\Veeam\Veeam ONE"
            if (Test-Path $veeamOneRegPath) {
                Write-Log "Configuração do Veeam ONE encontrada no registro" -Level "INFO"
            }
            
            # Buscar arquivos de configuração
            $configPaths = @(
                "${env:ProgramData}\Veeam\Veeam ONE\Reports",
                "${env:ProgramFiles}\Veeam\Veeam ONE\Reports",
                "${env:ALLUSERSPROFILE}\Veeam\Veeam ONE\Reports"
            )
            
            foreach ($path in $configPaths) {
                if (Test-Path $path) {
                    $reportFiles = Get-ChildItem -Path $path -Filter "*.xml" -ErrorAction SilentlyContinue
                    foreach ($file in $reportFiles) {
                        $reports += [PSCustomObject]@{
                            Name = $file.BaseName
                            Path = $file.FullName
                            LastModified = $file.LastWriteTime
                        }
                    }
                }
            }
        }
        
        if ($reports.Count -gt 0) {
            Write-Log "Encontrados $($reports.Count) relatórios" -Level "SUCCESS"
        } else {
            Write-Log "Nenhum relatório encontrado" -Level "WARNING"
        }
        
        return $reports
    }
    catch {
        Write-Log "Erro ao obter relatórios: $($_.Exception.Message)" -Level "ERROR"
        return @()
    }
}

function Start-VeeamReport {
    param(
        [Parameter(Mandatory = $true)]
        [object]$Report
    )
    
    try {
        Write-Log "Iniciando execução do relatório: $($Report.Name)" -Level "INFO"
        
        # Método 1: Usar cmdlets específicos
        if (Get-Command "Start-VeeamONEReport" -ErrorAction SilentlyContinue) {
            Start-VeeamONEReport -Report $Report
            Write-Log "Relatório '$($Report.Name)' iniciado via cmdlet" -Level "SUCCESS"
            return $true
        }
        # Método 2: Usar cmdlets do Veeam B&R
        elseif (Get-Command "Start-VBRReportJob" -ErrorAction SilentlyContinue) {
            Start-VBRReportJob -ReportJob $Report
            Write-Log "Relatório '$($Report.Name)' iniciado via VBR cmdlet" -Level "SUCCESS"
            return $true
        }
        # Método 3: Executar via linha de comando
        else {
            $veeamOnePath = "${env:ProgramFiles}\Veeam\Veeam ONE"
            $reportExecutable = "$veeamOnePath\Veeam.ONE.Reporter.exe"
            
            if (Test-Path $reportExecutable) {
                $arguments = "/report:`"$($Report.Name)`" /execute"
                Start-Process -FilePath $reportExecutable -ArgumentList $arguments -NoNewWindow -Wait
                Write-Log "Relatório '$($Report.Name)' executado via linha de comando" -Level "SUCCESS"
                return $true
            } else {
                Write-Log "Executável do Veeam ONE Reporter não encontrado" -Level "ERROR"
                return $false
            }
        }
    }
    catch {
        Write-Log "Erro ao executar relatório '$($Report.Name)': $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

#endregion

#region Função Principal

function Main {
    Write-Log "=== SCRIPT DE ENVIO MANUAL DE RELATÓRIOS VEEAM ONE (CMDLETS) ===" -Level "INFO"
    Write-Log "Desenvolvido por: Paulo Matheus - NVirtual" -Level "INFO"
    Write-Log "Data: $(Get-Date -Format 'dd/MM/yyyy HH:mm:ss')" -Level "INFO"
    Write-Log "=================================================================" -Level "INFO"
    
    # Inicializar PowerShell do Veeam
    if (-not (Initialize-VeeamPowerShell)) {
        Write-Log "Não foi possível carregar módulos do Veeam. Continuando com métodos alternativos..." -Level "WARNING"
    }
    
    # Conectar ao servidor Veeam
    if (-not (Connect-VeeamServer -Server $VeeamOneServer)) {
        Write-Log "Não foi possível conectar ao servidor Veeam. Tentando execução local..." -Level "WARNING"
    }
    
    # Obter relatórios
    $reports = Get-VeeamReports
    
    if ($reports.Count -eq 0) {
        Write-Log "Nenhum relatório encontrado. Verifique se o Veeam ONE está instalado e configurado." -Level "ERROR"
        exit 1
    }
    
    # Listar relatórios se solicitado
    if ($ListOnly) {
        Write-Log "=== RELATÓRIOS DISPONÍVEIS ===" -Level "INFO"
        foreach ($report in $reports) {
            if ($report.Name) {
                Write-Host "Nome: $($report.Name)" -ForegroundColor Cyan
                if ($report.Path) {
                    Write-Host "  Caminho: $($report.Path)" -ForegroundColor Gray
                }
                if ($report.LastModified) {
                    Write-Host "  Última Modificação: $($report.LastModified)" -ForegroundColor Gray
                }
                Write-Host ""
            }
        }
        Write-Log "===============================" -Level "INFO"
        exit 0
    }
    
    # Executar relatório específico
    if ($ReportName) {
        $targetReport = $reports | Where-Object { $_.Name -like "*$ReportName*" }
        
        if (-not $targetReport) {
            Write-Log "Relatório '$ReportName' não encontrado" -Level "ERROR"
            Write-Log "Relatórios disponíveis:" -Level "INFO"
            $reports | ForEach-Object { Write-Host "- $($_.Name)" -ForegroundColor Yellow }
            exit 1
        }
        
        $success = Start-VeeamReport -Report $targetReport
        if ($success) {
            Write-Log "Relatório '$($targetReport.Name)' executado com sucesso!" -Level "SUCCESS"
        } else {
            Write-Log "Falha ao executar relatório '$($targetReport.Name)'" -Level "ERROR"
        }
        exit 0
    }
    
    # Executar todos os relatórios
    if ($ExecuteAll) {
        Write-Log "Iniciando execução de todos os relatórios..." -Level "INFO"
        $successCount = 0
        $failCount = 0
        
        foreach ($report in $reports) {
            $success = Start-VeeamReport -Report $report
            if ($success) {
                $successCount++
            } else {
                $failCount++
            }
            Start-Sleep -Seconds 2  # Pausa entre execuções
        }
        
        Write-Log "Execução concluída. Sucessos: $successCount | Falhas: $failCount" -Level "INFO"
        exit 0
    }
    
    # Menu interativo
    Write-Log "=== MENU INTERATIVO ===" -Level "INFO"
    Write-Host "1. Listar relatórios disponíveis" -ForegroundColor Cyan
    Write-Host "2. Executar relatório específico" -ForegroundColor Cyan
    Write-Host "3. Executar todos os relatórios" -ForegroundColor Cyan
    Write-Host "4. Sair" -ForegroundColor Cyan
    
    $choice = Read-Host "Escolha uma opção (1-4)"
    
    switch ($choice) {
        "1" {
            Write-Log "=== RELATÓRIOS DISPONÍVEIS ===" -Level "INFO"
            foreach ($report in $reports) {
                Write-Host "Nome: $($report.Name)" -ForegroundColor Cyan
                if ($report.Path) {
                    Write-Host "  Caminho: $($report.Path)" -ForegroundColor Gray
                }
            }
        }
        "2" {
            Write-Log "Relatórios disponíveis:" -Level "INFO"
            for ($i = 0; $i -lt $reports.Count; $i++) {
                Write-Host "$($i + 1). $($reports[$i].Name)" -ForegroundColor Yellow
            }
            
            $reportChoice = Read-Host "Digite o número do relatório para executar"
            $reportIndex = [int]$reportChoice - 1
            
            if ($reportIndex -ge 0 -and $reportIndex -lt $reports.Count) {
                $selectedReport = $reports[$reportIndex]
                $success = Start-VeeamReport -Report $selectedReport
                if ($success) {
                    Write-Log "Relatório '$($selectedReport.Name)' executado com sucesso!" -Level "SUCCESS"
                }
            } else {
                Write-Log "Opção inválida" -Level "ERROR"
            }
        }
        "3" {
            Write-Log "Executando todos os relatórios..." -Level "INFO"
            foreach ($report in $reports) {
                Start-VeeamReport -Report $report
                Start-Sleep -Seconds 2
            }
            Write-Log "Todos os relatórios foram iniciados!" -Level "SUCCESS"
        }
        "4" {
            Write-Log "Saindo..." -Level "INFO"
            exit 0
        }
        default {
            Write-Log "Opção inválida" -Level "ERROR"
        }
    }
}

#endregion

# Executar função principal
Main
