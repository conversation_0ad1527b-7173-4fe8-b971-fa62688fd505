<#
.SYNOPSIS
    Atualiza automaticamente o site de um agente Tactical RMM baseado no IP local

.DESCRIPTION
    Este script detecta o IP local da máquina na faixa 192.168.* e atualiza
    automaticamente o site do agente no Tactical RMM baseado no mapeamento
    configurado de faixas de IP para sites.

.PARAMETER ApiToken
    Token de API do Tactical RMM. Se não fornecido, usa a variável de ambiente
    TACTICAL_RMM_TOKEN ou o token padrão configurado no script.

.PARAMETER Verbose
    Ativa saída detalhada para debugging

.PARAMETER WhatIf
    Mostra o que seria feito sem executar as alterações

.EXAMPLE
    .\Set-TacticalSite-Cliente.ps1
    Executa o script com configurações padrão

.EXAMPLE
    .\Set-TacticalSite-Cliente.ps1 -ApiToken "SEU_TOKEN" -Verbose
    Executa com token específico e saída detalhada

.EXAMPLE
    .\Set-TacticalSite-Cliente.ps1 -WhatIf
    Simula a execução sem fazer alterações

.NOTES
    Versão: 2.1
    Autor: NVirtual
    Data: 2025-06-27
    
    Requisitos:
    - PowerShell 5.1 ou superior
    - Acesso à API do Tactical RMM
    - Token com permissões de leitura/escrita de agentes
#>

# ============================================================================
# CONFIGURAÇÕES E INICIALIZAÇÃO
# ============================================================================

# CONFIGURAÇÕES DO CLIENTE
$apiUrl = "https://api.centralmesh.nvirtual.com.br"
$clientIdEsperado = 1

# Mapeamento de faixa de IP para site ID
$sites = @{
    "192.168.0." = 1      # Bauru
    "192.168.250." = 4    # São Paulo
    "192.168.103." = 16   # Loja 03
}

# Token de API do Tactical RMM (gerado via interface web, atenção ao escopo)
# Recomenda-se usar variável de ambiente ou parâmetro para maior segurança
param(
    [string]$ApiToken = $env:TACTICAL_RMM_TOKEN,
    [switch]$Verbose = $false,
    [switch]$WhatIf = $false
)

# Configurar verbosidade
if ($Verbose) {
    $VerbosePreference = "Continue"
}

if (-not $ApiToken) {
    $ApiToken = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"  # Fallback - substitua por seu token
    Write-Verbose "Usando token padrão configurado no script"
} else {
    Write-Verbose "Usando token da variável de ambiente ou parâmetro"
}

# Função para pegar o IP local na faixa 192.168.*
function Get-LocalIP {
    Write-Verbose "Detectando IP local na faixa 192.168.*"
    
    $ipAddresses = Get-NetIPAddress -AddressFamily IPv4 | Where-Object {
        $_.IPAddress -like "192.168.*" -and
        $_.IPAddress -notlike "169.254.*" -and
        $_.IPAddress -notlike "127.*" -and
        ($_.PrefixOrigin -eq "Manual" -or $_.PrefixOrigin -eq "Dhcp") -and
        $_.AddressState -eq "Preferred"
    }

    if ($ipAddresses) {
        $selectedIP = $ipAddresses[0].IPAddress
        Write-Verbose "IP selecionado: $selectedIP"
        return $selectedIP
    }
    
    Write-Verbose "Nenhum IP válido encontrado na faixa 192.168.*"
    return $null
}

# Função para validar conectividade com a API
function Test-TacticalAPI {
    param([string]$ApiUrl, [string]$Token)
    
    try {
        Write-Verbose "Testando conectividade com a API: $ApiUrl"
        $null = Invoke-RestMethod -Uri "$ApiUrl/api/v1/agents/" -Headers @{
            "Authorization" = "Token $Token"
        } -Method GET -TimeoutSec 10
        
        Write-Verbose "API respondeu com sucesso"
        return $true
    } catch {
        Write-Verbose "Erro ao testar API: $($_.Exception.Message)"
        return $false
    }
}

# Validar conectividade com a API antes de prosseguir
Write-Host "Testando conectividade com a API..."
if (-not (Test-TacticalAPI -ApiUrl $apiUrl -Token $ApiToken)) {
    Write-Host "Não foi possível conectar com a API do Tactical RMM"
    Write-Host "   Verifique:"
    Write-Host "   - URL da API: $apiUrl"
    Write-Host "   - Token de autenticação"
    Write-Host "   - Conectividade de rede"
    exit 1
}

# Obter nome da máquina local
$hostname = $env:COMPUTERNAME
Write-Host "Hostname local: $hostname"

# Buscar agente pelo hostname na API
Write-Verbose "Buscando agente com hostname: $hostname"
try {
    $response = Invoke-RestMethod -Uri "$apiUrl/api/v1/agents/?hostname=$hostname" -Headers @{
        "Authorization" = "Token $ApiToken"
    } -TimeoutSec 30
} catch {
    Write-Host "Erro ao buscar agente na API: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "   Status Code: $($_.Exception.Response.StatusCode)"
    }
    exit 1
}

# Verificar se encontrou agentes
if (-not $response -or $response.count -eq 0) {
    Write-Host "Nenhum agente encontrado com esse hostname."
    exit 1
}

if ($response.count -gt 1) {
    Write-Host "Múltiplos agentes com esse hostname. Abortando por segurança."
    exit 1
}

# Obter dados do agente
$agent = $response.results[0]
$agentId = $agent.id
$clientId = $agent.client
$agentSiteAtual = $agent.site_name
$agentSiteId = $agent.site

Write-Host "Agente encontrado. ID: $agentId | Cliente ID: $clientId | Site atual: $agentSiteAtual (ID: $agentSiteId)"

# Verificar se pertence ao cliente correto
if ($clientId -ne $clientIdEsperado) {
    Write-Host "Este agente não pertence ao cliente esperado (ID $clientIdEsperado)."
    exit 1
}

# Tentar pegar IP local válido (uso interno para redes 192.168.*)
$ipLocal = Get-LocalIP

if (-not $ipLocal) {
    Write-Host "Nenhum IP local na faixa 192.168.* detectado."
    Write-Host "IPs disponíveis:"
    Get-NetIPAddress -AddressFamily IPv4 | Where-Object { $_.IPAddress -notlike "127.*" } | ForEach-Object {
        Write-Host "   - $($_.IPAddress)"
    }
    exit 1
}

Write-Host "IP detectado: $ipLocal"

# Verificar a qual site ele pertence
$siteId = $null
foreach ($prefix in $sites.Keys) {
    if ($ipLocal.StartsWith($prefix)) {
        $siteId = $sites[$prefix]
        break
    }
}

if (-not $siteId) {
    Write-Host "Faixa de IP não corresponde a nenhum site configurado."
    Write-Host "Sites configurados:"
    foreach ($prefix in $sites.Keys) {
        Write-Host "   - $prefix -> Site ID $($sites[$prefix])"
    }
    exit 1
}

Write-Host "Site correspondente detectado: ID $siteId"

# Atualizar site se for diferente (usando -ne para "not equal" em PowerShell)
if ($agentSiteId -ne $siteId) {
    Write-Host "Site atual ($agentSiteId) diferente do esperado ($siteId). Atualizando..."

    if ($WhatIf) {
        Write-Host "[WHAT-IF] Seria executada a atualização do site para ID $siteId"
        Write-Host "   Agente ID: $agentId"
        Write-Host "   Site atual: $agentSiteId -> Novo site: $siteId"
    } else {
        $body = @{
            site = $siteId
        } | ConvertTo-Json

        Write-Verbose "Enviando requisição PATCH para atualizar site"
        Write-Verbose "Body: $body"

        try {
            $updateResult = Invoke-RestMethod -Method PATCH -Uri "$apiUrl/api/v1/agents/$agentId" -Headers @{
                "Authorization" = "Token $ApiToken"
                "Content-Type" = "application/json"
            } -Body $body -TimeoutSec 30

            Write-Host "Site atualizado com sucesso para ID $siteId."
            Write-Host "Agente ID: $agentId | Novo Site: $($updateResult.site_name)"

            # Log da operação
            $logEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Agente $agentId ($hostname) movido para site $siteId"
            Write-Verbose $logEntry

        } catch {
            Write-Host "Erro ao atualizar site: $($_.Exception.Message)"
            if ($_.Exception.Response) {
                Write-Host "   Status Code: $($_.Exception.Response.StatusCode)"
                if ($_.Exception.Response.StatusCode -eq 403) {
                    Write-Host "   Possível problema: Token sem permissões suficientes"
                } elseif ($_.Exception.Response.StatusCode -eq 404) {
                    Write-Host "   Possível problema: Agente ou Site não encontrado"
                }
            }
            exit 1
        }
    }
} else {
    Write-Host "O site já está correto (ID: $siteId). Nenhuma alteração feita."
}

Write-Host ""
Write-Host "Script executado com sucesso!"
Write-Host "Resumo Final:"
Write-Host "   - Hostname: $hostname"
Write-Host "   - IP Local: $ipLocal"
Write-Host "   - Site ID: $siteId"
Write-Host "   - Agente ID: $agentId"
Write-Host "   - Cliente ID: $clientId"
Write-Host "   - Timestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

# Informações adicionais se verbose estiver ativo
Write-Verbose "=== INFORMAÇÕES DETALHADAS ==="
Write-Verbose "API URL: $apiUrl"
Write-Verbose "Sites configurados: $($sites.Count)"
foreach ($prefix in $sites.Keys) {
    Write-Verbose "  $prefix -> Site ID $($sites[$prefix])"
}
Write-Verbose "=============================="
