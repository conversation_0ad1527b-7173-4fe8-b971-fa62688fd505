import os
import smtplib
from datetime import datetime, timedelta
from email.message import EmailMessage

# CONFIGURAÇÕES
PASTA_BASE = r"D:\BKP_BANCO"
EMAIL_ORIGEM = "<EMAIL>"
EMAIL_DESTINO = "<EMAIL>"
SMTP_SERVIDOR = "smtp.skymail.net.br"
SMTP_PORTA = 465
SMTP_USUARIO = EMAIL_ORIGEM
SMTP_SENHA = "nwjdCoBE3P"

# Datas válidas: hoje e ontem
hoje = datetime.now()
ontem = hoje - timedelta(days=1)
datas_validas = {hoje.date(), ontem.date()}

def cliente_tem_arquivo_valido(pasta_cliente):
    """Verifica se há ao menos um arquivo válido (modificado ou criado hoje/ontem) e exibe o mais recente"""
    arquivos = []
    for root, _, files in os.walk(pasta_cliente):
        for file in files:
            caminho = os.path.join(root, file)
            try:
                mod_time = datetime.fromtimestamp(os.path.getmtime(caminho))
                cri_time = datetime.fromtimestamp(os.path.getctime(caminho))
                data_mais_recente = max(mod_time, cri_time)
                arquivos.append((file, data_mais_recente))
            except Exception as e:
                print(f"Erro ao verificar {caminho}: {e}")

    if not arquivos:
        print(f"📂 {os.path.basename(pasta_cliente)} → Nenhum arquivo encontrado.")
        return False

    # Encontra o arquivo mais recente da pasta
    arquivo_mais_novo = max(arquivos, key=lambda x: x[1])
    nome_arquivo, data = arquivo_mais_novo
    print(f"📂 {os.path.basename(pasta_cliente)} → Mais recente: {nome_arquivo} ({data.strftime('%Y-%m-%d %H:%M:%S')})")

    # Verifica se está dentro das datas válidas
    if data.date() in datas_validas:
        return True
    return False

def verificar_clientes():
    """Percorre as subpastas e verifica o backup"""
    clientes_com_falha = []

    for nome_cliente in os.listdir(PASTA_BASE):
        caminho_cliente = os.path.join(PASTA_BASE, nome_cliente)
        if os.path.isdir(caminho_cliente):
            if not cliente_tem_arquivo_valido(caminho_cliente):
                clientes_com_falha.append(nome_cliente)

    return clientes_com_falha

def enviar_email_alerta(lista_clientes):
    """Envia alerta por e-mail com a lista de clientes com falha"""
    msg = EmailMessage()
    msg['Subject'] = "🚨 Alerta de Backup - Falha Detectada - Alternativa"
    msg['From'] = EMAIL_ORIGEM
    msg['To'] = EMAIL_DESTINO

    corpo = "Os seguintes clientes ALternativa não possuem arquivos com backup atualizado (modificados hoje ou ontem):\n\n"
    corpo += "\n".join(f"❌ {cliente}" for cliente in lista_clientes)
    corpo += "\n\nFavor verificar o processo de backup dessas pastas."
    msg.set_content(corpo)

    try:
        with smtplib.SMTP_SSL(SMTP_SERVIDOR, SMTP_PORTA) as s:
            s.login(SMTP_USUARIO, SMTP_SENHA)
            s.send_message(msg)
        print("📧 E-mail de alerta enviado!")
    except Exception as e:
        print(f"Erro ao enviar e-mail: {e}")

def main():
    print("🔍 Verificando backup em cada pasta de cliente...\n")
    falhas = verificar_clientes()

    if falhas:
        print("\n❌ Clientes com falha no backup:")
        for cliente in falhas:
            print(f" - {cliente}")
        enviar_email_alerta(falhas)
    else:
        print("\n✅ Todos os clientes têm backups recentes (hoje ou ontem).")

if __name__ == "__main__":
    main()