import requests
import csv
from urllib3.exceptions import InsecureRequestWarning
import urllib3
urllib3.disable_warnings(InsecureRequestWarning)

# Configurações
controller_url = "https://unifi.nvirtual.com.br:8443"
username = "administrador"
password = "QA&vv7RGRQb@J0"

# Criar sessão
session = requests.Session()
session.verify = False
session.headers.update({'Content-Type': 'application/json'})

# === LOGIN ===
login_url = f"{controller_url}/api/login"
login_payload = {"username": username, "password": password}
login_resp = session.post(login_url, json=login_payload)
login_resp.raise_for_status()

# === OBTÉM LISTA DE SITES ===
sites_resp = session.get(f"{controller_url}/api/self/sites")
sites_resp.raise_for_status()
sites = sites_resp.json()['data']

# === LOOP EM TODOS OS SITES E DISPOSITIVOS ===
for site in sites:
    site_name = site['name']
    site_desc = site.get('desc', site_name)

    print(f"\n📡 Site: {site_desc}")

    devices_url = f"{controller_url}/api/s/{site_name}/stat/device"
    devices_resp = session.get(devices_url)
    devices_resp.raise_for_status()
    devices = devices_resp.json()['data']

    for device in devices:
        name = device.get('name', 'Sem nome')
        model = device.get('model', 'Desconhecido')
        version = device.get('version', 'N/A')
        upgradable = device.get('upgradable', False)
        state = device.get('state', 'Desconhecido')
        adopted = device.get('adopted', True)

        # Mapeamento do status
        if state == 9:
            status_code = 9
            status_desc = "Pendente de adoção"
        elif state == 1:
            status_code = 1
            status_desc = "Online atualizado"
        elif state == 0:
            status_code = 0
            status_desc = "Offline"
        else:
            status_code = state
            status_desc = str(state)

        print(f"{name} ({model}) - Firmware {version} - Status: {status_code} ({status_desc})")