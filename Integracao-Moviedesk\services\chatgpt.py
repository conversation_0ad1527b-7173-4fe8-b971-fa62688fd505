from openai import OpenAI
from config import CHATGPT_API_KEY

client = OpenAI(api_key=CHATGPT_API_KEY)

def gerar_resposta(prompt):
    resposta = client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[
            {"role": "system", "content": "Você é um analista de segurança da informação."},
            {"role": "user", "content": prompt}
        ]
    )
    return resposta.choices[0].message.content.strip()
