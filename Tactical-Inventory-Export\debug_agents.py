#!/usr/bin/env python3
import requests
import json

API_URL = "https://api.centralmesh.nvirtual.com.br"
API_TOKEN = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"

headers = {"X-API-KEY": API_TOKEN}

print("=== ESTRUTURA DOS AGENTES ===")
try:
    r = requests.get(f"{API_URL}/agents/", headers=headers)
    agents = r.json()
    
    if agents:
        print(f"Total de agentes: {len(agents)}")
        print("\nEstrutura do primeiro agente:")
        first_agent = agents[0]
        for key, value in first_agent.items():
            print(f"  {key}: {type(value).__name__} = {value}")
        
        print("\nClientes únicos nos agentes:")
        clients_in_agents = set()
        for agent in agents:
            if 'client' in agent and agent['client']:
                clients_in_agents.add(agent['client'])
        
        print(f"IDs de clientes encontrados: {sorted(clients_in_agents)}")
        
except Exception as e:
    print(f"Erro: {e}")
