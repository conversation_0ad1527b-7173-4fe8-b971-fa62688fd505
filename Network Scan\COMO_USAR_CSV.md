# 🚀 Como Usar - Geração Automática de CSV

## ✨ **NOVIDADE: Geração Automática**

O `network_scanner.py` agora **gera automaticamente** os arquivos CSV após cada scan, sem necessidade de flags especiais!

## 📋 **Uso Simples**

### **Comando Básico:**
```bash
python network_scanner.py -n ***********/24
```

**Resultado automático:**
- `network_scan_20250810_102215_detalhado.csv` (uma linha por máquina)
- `network_scan_20250810_102215_resumo.csv` (uma linha por cliente)

### **Com Nome Personalizado:**
```bash
python network_scanner.py -n ***********/24 -o "empresa_janeiro_2025"
```

**Resultado automático:**
- `empresa_janeiro_2025_detalhado.csv`
- `empresa_janeiro_2025_resumo.csv`

## 📊 **Estrutura dos Arquivos CSV**

### **CSV Detalhado** (todas as máquinas)
```csv
Unidade_Cliente,Rede_Cliente,Total_Maquinas,IP_Maquina,Nome_Maquina,Sistema_Operacional,Tipo_Dispositivo
Cliente_001,***********/24,5,************,PC-ADMIN,Windows,Windows Desktop
Cliente_001,***********/24,5,************,PRINTER-HP,Linux,HP Printer
Cliente_002,***********/24,3,************,SERVER-WEB,Linux,Linux Server
```

### **CSV Resumo** (estatísticas por cliente)
```csv
Unidade_Cliente,Rede_Cliente,Total_Maquinas,Sistema_Operacional_Principal,Lista_IPs
Cliente_001,***********/24,5,Windows,"************; ************; ************"
Cliente_002,***********/24,3,Linux,"************; ************; ************"
```

## 🎯 **Casos de Uso Práticos**

### **1. Auditoria de TI**
```bash
python network_scanner.py -n ***********/16 -o "auditoria_ti_2025"
```
- Escaneia toda a rede corporativa
- Gera relatórios por cliente automaticamente

### **2. Inventário de Equipamentos**
```bash
python network_scanner.py -n ***********/24 -o "inventario_filial_sp"
```
- Mapeia equipamentos de uma filial
- Organiza por unidade/cliente

### **3. Monitoramento de Rede**
```bash
python network_scanner.py -n ************/24 -o "monitoramento_$(date +%Y%m%d)"
```
- Scan diário automatizado
- Histórico de equipamentos

## 📈 **Análise dos Dados**

### **No Excel/LibreOffice:**
1. Abra o arquivo `*_detalhado.csv`
2. Aplique filtros automáticos
3. Filtre por `Unidade_Cliente` para ver equipamentos específicos
4. Use o arquivo `*_resumo.csv` para visão geral

### **Análise por Cliente:**
- **Detalhado**: Para análise técnica completa
- **Resumo**: Para relatórios executivos

## 🔧 **Opções Avançadas**

### **Controle de Performance:**
```bash
# Rede grande - menos threads
python network_scanner.py -n ***********/16 -t 50 --timeout 3 -o "rede_grande"

# Rede pequena - mais threads
python network_scanner.py -n ***********/24 -t 200 --timeout 1 -o "rede_pequena"
```

### **Script Dedicado (para controle específico):**
```bash
# Apenas CSV detalhado
python generate_client_csv.py -n ***********/24 -f detailed

# Apenas CSV resumo
python generate_client_csv.py -n ***********/24 -f summary

# Ambos (igual ao scanner principal)
python generate_client_csv.py -n ***********/24 -f both
```

## 💡 **Dicas Importantes**

### **Nomenclatura dos Clientes:**
- `192.168.1.x` → `Cliente_001`
- `192.168.2.x` → `Cliente_002`
- `192.168.10.x` → `Cliente_010`
- `192.168.100.x` → `Cliente_100`

### **Organização dos Dados:**
- Cada sub-rede `/24` representa um cliente
- Máquinas são agrupadas por cliente automaticamente
- IPs são ordenados numericamente

### **Formatos de Rede Suportados:**
```bash
# Uma sub-rede (1 cliente)
python network_scanner.py -n ***********/24

# Múltiplas sub-redes (até 256 clientes)
python network_scanner.py -n ***********/16

# Rede corporativa grande
python network_scanner.py -n 10.0.0.0/8
```

## 🆘 **Solução de Problemas**

### **Nenhum dispositivo encontrado:**
```bash
# Teste com timeout maior
python network_scanner.py -n ***********/24 --timeout 5

# Teste com rede menor
python network_scanner.py -n ***********/32
```

### **Scan muito lento:**
```bash
# Reduza threads
python network_scanner.py -n ***********/24 -t 50

# Reduza timeout
python network_scanner.py -n ***********/24 --timeout 1
```

### **Arquivos não gerados:**
- Verifique se há dispositivos encontrados
- Verifique permissões de escrita no diretório
- Veja mensagens de erro no terminal

## 📁 **Localização dos Arquivos**

Os arquivos CSV são salvos no **diretório atual** onde o comando foi executado.

### **Exemplo de arquivos gerados:**
```
📁 Diretório atual/
├── empresa_2025_detalhado.csv    (todas as máquinas)
├── empresa_2025_resumo.csv       (estatísticas por cliente)
└── network_scanner.py            (script executado)
```

## 🎉 **Resumo**

**Comando mais simples:**
```bash
python network_scanner.py -n ***********/24 -o "minha_empresa"
```

**Resultado garantido:**
- ✅ Scan completo da rede
- ✅ CSV detalhado gerado automaticamente
- ✅ CSV resumo gerado automaticamente
- ✅ Dados organizados por cliente
- ✅ Pronto para análise no Excel

---

**🚀 Agora é só executar e os CSVs são gerados automaticamente!**

*Desenvolvido por Paulo Matheus - Janeiro 2025*
