# 🌐 Network Scanner Avançado

Scanner de rede avançado que identifica dispositivos específicos, sistemas operacionais, modelos e fabricantes na rede.

## 🎯 Funcionalidades

### 📱 **Dispositivos Detectados:**
- **Smartphones**: iPhone, iPad, Android (Samsung, Xiaomi, Huawei, OnePlus)
- **Roteadores**: TP-Link, D-Link, Netgear, ASUS, Linksys, Cisco, MikroTik
- **Access Points**: UniFi, Ubiquiti
- **Firewalls**: pfSense, OPNsense, Sophos, FortiGate, Check Point
- **Impressoras**: HP, Canon, Epson, Brother, Xerox
- **Servidores**: Windows Server, Linux (Ubuntu, Debian, CentOS, Red Hat)
- **NAS**: Synology, QNAP
- **Smart Devices**: Smart TV, Chromecast, Roku, Apple TV, Fire TV

### 🔬 **Métodos de Detecção:**
1. **TTL Analysis**: Identifica OS baseado no Time-To-Live
   - Windows: TTL=128
   - Linux/Unix: TTL=64
   - Cisco/Network: TTL=255
   - macOS/iOS: TTL=60

2. **Port Scanning**: Identifica serviços e dispositivos
   - SSH (22): Linux/Unix systems
   - RDP (3389): Windows Server/Desktop
   - SMB (445): Windows machines
   - HTTP/HTTPS (80/443): Web servers, roteadores
   - SNMP (161/162): Network devices
   - mDNS (5353): Apple devices, Android
   - iTunes (62078): iPhone/iPad

3. **Hostname Analysis**: Padrões de nomenclatura
   - Fabricantes: tp-link, cisco, netgear, etc.
   - Tipos: router, printer, server, etc.
   - Modelos específicos

4. **HTTP Fingerprinting**: Análise de headers e conteúdo web
   - Server headers
   - Títulos de páginas
   - Detecção de modelos específicos

## 📊 **Exportação de Dados:**
- **JSON**: Dados estruturados para análise programática
- **Excel**: Planilhas organizadas por cliente com múltiplas abas
- **CSV**: Formato universal para análise de dados
  - **CSV Detalhado**: Uma linha por máquina encontrada
  - **CSV Resumo**: Uma linha por cliente/unidade

## 🚀 Como Usar

### **Instalação:**
```bash
# Não requer instalação, apenas Python 3
python3 network_scanner.py
```

### **Exemplos de Uso:**

```bash
# Scan básico da rede ***********/16 (padrão)
python3 network_scanner.py

# Scan de sub-rede específica (mais rápido)
python3 network_scanner.py --network ***********/24

# Scan com mais threads (mais rápido para redes grandes)
python3 network_scanner.py -n ***********/16 -t 200

# Scan de sub-rede com configurações otimizadas
python3 network_scanner.py -n ***********/24 -t 50 --timeout 1

# Salvar resultados em JSON
python3 network_scanner.py -n ***********/24 --save

# Scan completo com todas as opções
python3 network_scanner.py -n ***********/16 -t 150 --timeout 2 --save
```

### **Parâmetros:**
- `--network, -n`: Rede para escanear (padrão: ***********/16)
- `--threads, -t`: Número de threads (padrão: 100)
- `--timeout`: Timeout em segundos (padrão: 2)
- `--save, -s`: Salvar resultados em JSON
- `--help, -h`: Ajuda

## 📊 Exemplo de Saída

```
🌐 NETWORK SCANNER - Identificação Avançada de Dispositivos
========================================================================================================================
📡 Rede: ***********/24
🔍 IPs a escanear: 254
⚡ Threads: 50
⏱️  Timeout: 2s
🕐 Início: 2025-01-08 16:45:30
🔬 Métodos: TTL Analysis, Port Scanning, Hostname Analysis, HTTP Fingerprinting
📱 Detecta: Windows, Linux, Android, iPhone, Roteadores, Firewalls, Impressoras, etc.
========================================================================================================================

IP              | Hostname              | OS              | Método       | Dispositivo/Modelo
------------------------------------------------------------------------------------------------------------------------
✅ ***********   | tplink-router.local   | Network Device  | TTL=255+HTTP | TP-Link Archer C7
✅ ***********0  | iPhone-Paulo.local    | macOS/iOS       | TTL=60+Ports | iPhone
✅ ************  | android-samsung.local | Linux/Unix      | Hostname     | Samsung Device
✅ ************  | ubuntu-server.local   | Linux/Unix      | TTL=64+Ports | Ubuntu Server
✅ ************  | windows-pc.local      | Windows         | TTL=128+RDP  | Windows Server
✅ ************  | hp-printer.local      | Desconhecido    | Hostname     | HP Printer
✅ ************  | unifi-ap.local        | Network Device  | Hostname     | UniFi Access Point
✅ ************  | synology-nas.local    | Linux/Unix      | Hostname     | Synology NAS

📊 RESUMO DO SCAN AVANÇADO
========================================================================================================================
⏱️  Tempo total: 45.32 segundos
🎯 Hosts encontrados: 8
📡 Total de IPs escaneados: 254

🖥️  Sistemas Operacionais encontrados:
   Linux/Unix: 4
   Network Device: 2
   Windows: 1
   macOS/iOS: 1

📱 Tipos de dispositivos encontrados:
   HP Printer: 1
   iPhone: 1
   Samsung Device: 1
   Synology NAS: 1
   TP-Link Archer C7: 1
   Ubuntu Server: 1
   UniFi Access Point: 1
   Windows Server: 1

🔍 Métodos de detecção utilizados:
   Hostname: 4
   TTL=128+RDP: 1
   TTL=255+HTTP: 1
   TTL=60+Ports: 1
   TTL=64+Ports: 1
```

## 📁 Arquivos de Saída

### **JSON (--save):**
```json
{
  "scan_info": {
    "network": "***********/24",
    "scan_time": "2025-01-08T16:45:30",
    "total_hosts": 8
  },
  "results": [
    {
      "ip": "***********",
      "hostname": "tplink-router.local",
      "ttl": 255,
      "os": "Network Device",
      "confidence": 95,
      "detection_method": "TTL=255+HTTP",
      "device_type": "TP-Link Archer C7",
      "open_ports": [[80, "HTTP"], [443, "HTTPS"], [23, "Telnet"]],
      "http_info": {
        "server": "TP-LINK",
        "title": "TP-LINK Archer C7",
        "model": "TP-Link Archer C7"
      }
    }
  ]
}
```

### **CSV (--csv / --csv-summary):**

#### **CSV Detalhado (uma linha por máquina):**
```csv
Unidade_Cliente,Rede_Cliente,Total_Maquinas,IP_Maquina,Nome_Maquina,Sistema_Operacional,Tipo_Dispositivo,Metodo_Deteccao,TTL,Confianca_Percent,Portas_Abertas,Data_Hora_Scan
Cliente_001,***********/24,5,***********0,PC-ADMIN,Windows,Windows Desktop,TTL=128+Ports,128,90,"135(RPC); 445(SMB); 3389(RDP)",08/01/2025 14:30:15
Cliente_001,***********/24,5,************,PRINTER-HP,Linux,HP LaserJet P1102 Printer,TTL=64+Ports,64,95,"80(HTTP); 9100(Printer)",08/01/2025 14:30:18
Cliente_002,***********/24,3,************,SERVER-WEB,Linux,Linux Server,TTL=64+Ports,64,85,"22(SSH); 80(HTTP); 443(HTTPS)",08/01/2025 14:30:25
```

#### **CSV Resumo (uma linha por cliente):**
```csv
Unidade_Cliente,Rede_Cliente,Total_Maquinas,Sistema_Operacional_Principal,Dispositivo_Principal,Lista_IPs,Lista_Hostnames,Data_Scan
Cliente_001,***********/24,5,Windows,Windows Desktop,"***********0; ************; ************","PC-ADMIN; PRINTER-HP; SERVER-01",08/01/2025 14:30:15
Cliente_002,***********/24,3,Linux,Linux Server,"************; ************; ************","SERVER-WEB; SERVER-DB; FIREWALL",08/01/2025 14:30:20
```

#### **Comandos CSV:**
```bash
# ✨ AUTOMÁTICO: CSVs são gerados automaticamente após qualquer scan
python network_scanner.py -n ***********/24

# Com nome personalizado
python network_scanner.py -n ***********/24 -o "empresa_2025"

# Script dedicado para CSV (mais opções)
python generate_client_csv.py -n ***********/24

# Apenas CSV detalhado ou resumo (script dedicado)
python generate_client_csv.py -n ***********/24 -f detailed
python generate_client_csv.py -n ***********/24 -f summary
```

**🎯 Comportamento Automático:**
- O `network_scanner.py` **sempre gera ambos os CSVs** após o scan
- Não é necessário usar flags `--csv` ou `--csv-summary`
- Arquivos gerados: `nome_detalhado.csv` e `nome_resumo.csv`

📖 **Para mais detalhes sobre CSV, consulte:** [CSV_EXPORT_GUIDE.md](CSV_EXPORT_GUIDE.md)

## ⚡ Performance

### **Recomendações de Threads:**
- **Sub-rede /24 (254 IPs)**: 50-100 threads
- **Sub-rede /16 (65,534 IPs)**: 100-200 threads
- **Redes pequenas**: 20-50 threads

### **Tempos Estimados:**
- **/24 (254 IPs)**: 30-60 segundos
- **/16 (65,534 IPs)**: 30-60 minutos

## 🔧 Troubleshooting

### **Problemas Comuns:**
1. **Timeout muito baixo**: Aumente `--timeout`
2. **Muitas threads**: Reduza `-t` se houver erros
3. **Firewall bloqueando**: Alguns dispositivos podem não responder
4. **Permissões**: Execute como usuário normal (não root)

### **Melhorar Detecção:**
- Use timeout maior para redes lentas
- Reduza threads se houver muitos erros
- Alguns dispositivos só respondem a pings específicos

## 📋 Requisitos

- **Python 3.6+**
- **Bibliotecas padrão**: socket, subprocess, threading, json
- **Sistema**: Windows, Linux, macOS
- **Rede**: Acesso à rede que será escaneada

## 🎯 Casos de Uso

- **Auditoria de rede**: Inventário de dispositivos por cliente/unidade
- **Segurança**: Identificação de dispositivos não autorizados
- **Troubleshooting**: Localizar dispositivos específicos
- **Documentação**: Mapear infraestrutura de rede
- **Monitoramento**: Detectar novos dispositivos
- **Relatórios CSV**: Análise de dados por cliente em Excel/LibreOffice
- **Gestão de clientes**: Controle de equipamentos por unidade
- **Auditoria de TI**: Relatórios executivos e técnicos
