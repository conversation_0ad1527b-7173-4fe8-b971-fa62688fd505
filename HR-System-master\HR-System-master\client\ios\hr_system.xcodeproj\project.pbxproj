// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {
/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* hr_systemTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* hr_systemTests.m */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		13B07FBD1A68108700A75B9A /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB11A68108700A75B9A /* LaunchScreen.xib */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		2D02E4BC1E0B4A80006451C7 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		2D02E4BD1E0B4A84006451C7 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		2D02E4BF1E0B4AB3006451C7 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		2DCD954D1E0B4F2C00145EB5 /* hr_systemTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* hr_systemTests.m */; };
		71E0BFE80A254D6B99D0FB94 /* Roboto_medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4BAB168DB1464DCD9DCCABD9 /* Roboto_medium.ttf */; };
		95684421D2964472BF8981E0 /* Roboto.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5CBE7368034B4F40AE9BD997 /* Roboto.ttf */; };
		3D211027B5DF4FBFB2110140 /* rubicon-icon-font.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 51A3CA6583BA4454A074F987 /* rubicon-icon-font.ttf */; };
		B80FEB5920184F90ABA6151A /* AntDesign.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BB7F92ED7BE04F9BA49E24EF /* AntDesign.ttf */; };
		FC9CC1D04DA24FAAA3A97BB3 /* Entypo.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 889A55A70BB143F1A68E994C /* Entypo.ttf */; };
		8AD08489F8334FAEAA70866D /* EvilIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 103635A848ED4CB09079ADB7 /* EvilIcons.ttf */; };
		82C440EF55634EC5A4067EB3 /* Feather.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C7CEEAC5A5B84DEBBA7ADE64 /* Feather.ttf */; };
		EDBB09D3FA534951ACF8D935 /* FontAwesome.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 164B8C28A74148088E17CAF8 /* FontAwesome.ttf */; };
		3F8C5641F99E48B798DD9016 /* FontAwesome5_Brands.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 3162174D443540859871EE05 /* FontAwesome5_Brands.ttf */; };
		2A1045037712427691DCEA8C /* FontAwesome5_Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C852A77F27854700B860BC2A /* FontAwesome5_Regular.ttf */; };
		32A4360E3566462288D6BD1E /* FontAwesome5_Solid.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E37E2E0A03C2430D884B6F19 /* FontAwesome5_Solid.ttf */; };
		8D7B431C11B040F3BEA86E10 /* Fontisto.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 72A1E2FBB8174E969803D7FD /* Fontisto.ttf */; };
		AAF8038F188448199AB1590B /* Foundation.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9F00B84D147B451DB808AC64 /* Foundation.ttf */; };
		F7080C8FAD6846D092AF5EAA /* Ionicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 11B86AFB0A3749A5A2D1BFAB /* Ionicons.ttf */; };
		17ABC32413674396A0489EB8 /* MaterialCommunityIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D6161819E5084BF29C34FD82 /* MaterialCommunityIcons.ttf */; };
		9A07C7EA72F6401483522743 /* MaterialIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 82D14568A7D74C66936CB4ED /* MaterialIcons.ttf */; };
		00E0BB5DBCE448F28522CB2E /* Octicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0E61F9DA07904A08B9C023BA /* Octicons.ttf */; };
		5D76FEC260664688B14D8BFF /* SimpleLineIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 751C33D91EA44A2DB7FB0B41 /* SimpleLineIcons.ttf */; };
		559913FDCB3349068E572F87 /* Zocial.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C88B41CB101B4C1290E986C5 /* Zocial.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = hr_system;
		};
		2D02E4911E0B4A5D006451C7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2D02E47A1E0B4A5D006451C7;
			remoteInfo = "hr_system-tvOS";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		008F07F21AC5B25A0029DE68 /* main.jsbundle */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = main.jsbundle; sourceTree = "<group>"; };
		00E356EE1AD99517003FC87E /* hr_systemTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = hr_systemTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* hr_systemTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = hr_systemTests.m; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* hr_system.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = hr_system.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = hr_system/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.m; path = hr_system/AppDelegate.m; sourceTree = "<group>"; };
		13B07FB21A68108700A75B9A /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = hr_system/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = hr_system/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = hr_system/main.m; sourceTree = "<group>"; };
		2D02E47B1E0B4A5D006451C7 /* hr_system-tvOS.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "hr_system-tvOS.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		2D02E4901E0B4A5D006451C7 /* hr_system-tvOSTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "hr_system-tvOSTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		ED2971642150620600B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS12.0.sdk/System/Library/Frameworks/JavaScriptCore.framework; sourceTree = DEVELOPER_DIR; };
		47AD9A88C2DE49C48E5464CA /* AntDesign.ttf */ = {isa = PBXFileReference; name = "AntDesign.ttf"; path = "../node_modules/native-base/Fonts/AntDesign.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		CB78664225B040FAAEB6255E /* Entypo.ttf */ = {isa = PBXFileReference; name = "Entypo.ttf"; path = "../node_modules/native-base/Fonts/Entypo.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		44F28E6F252F472586CD79AB /* EvilIcons.ttf */ = {isa = PBXFileReference; name = "EvilIcons.ttf"; path = "../node_modules/native-base/Fonts/EvilIcons.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		2DADA19A193F424A8DCD5E89 /* Feather.ttf */ = {isa = PBXFileReference; name = "Feather.ttf"; path = "../node_modules/native-base/Fonts/Feather.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		F4847D75A1814D7393DB1AF5 /* FontAwesome.ttf */ = {isa = PBXFileReference; name = "FontAwesome.ttf"; path = "../node_modules/native-base/Fonts/FontAwesome.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		D4ED2F4D289F439DBCBF93B2 /* FontAwesome5_Brands.ttf */ = {isa = PBXFileReference; name = "FontAwesome5_Brands.ttf"; path = "../node_modules/native-base/Fonts/FontAwesome5_Brands.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		775B472FD3C34C1D907F200F /* FontAwesome5_Regular.ttf */ = {isa = PBXFileReference; name = "FontAwesome5_Regular.ttf"; path = "../node_modules/native-base/Fonts/FontAwesome5_Regular.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		FABE1FC1F910436585F7F4FC /* FontAwesome5_Solid.ttf */ = {isa = PBXFileReference; name = "FontAwesome5_Solid.ttf"; path = "../node_modules/native-base/Fonts/FontAwesome5_Solid.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		F59C9DDE8F5145D8958617AE /* Foundation.ttf */ = {isa = PBXFileReference; name = "Foundation.ttf"; path = "../node_modules/native-base/Fonts/Foundation.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		A157756C4801438B950401B3 /* Ionicons.ttf */ = {isa = PBXFileReference; name = "Ionicons.ttf"; path = "../node_modules/native-base/Fonts/Ionicons.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		C78AD324A9184B22B8D12E9E /* MaterialCommunityIcons.ttf */ = {isa = PBXFileReference; name = "MaterialCommunityIcons.ttf"; path = "../node_modules/native-base/Fonts/MaterialCommunityIcons.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		2F181EF9BAEC479E9A82FFA5 /* MaterialIcons.ttf */ = {isa = PBXFileReference; name = "MaterialIcons.ttf"; path = "../node_modules/native-base/Fonts/MaterialIcons.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		8F104F21B66B4A609FFE41D5 /* Octicons.ttf */ = {isa = PBXFileReference; name = "Octicons.ttf"; path = "../node_modules/native-base/Fonts/Octicons.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		4BAB168DB1464DCD9DCCABD9 /* Roboto_medium.ttf */ = {isa = PBXFileReference; name = "Roboto_medium.ttf"; path = "../node_modules/native-base/Fonts/Roboto_medium.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		5CBE7368034B4F40AE9BD997 /* Roboto.ttf */ = {isa = PBXFileReference; name = "Roboto.ttf"; path = "../node_modules/native-base/Fonts/Roboto.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		51A3CA6583BA4454A074F987 /* rubicon-icon-font.ttf */ = {isa = PBXFileReference; name = "rubicon-icon-font.ttf"; path = "../node_modules/native-base/Fonts/rubicon-icon-font.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		8D3C26FC32684429B3658607 /* SimpleLineIcons.ttf */ = {isa = PBXFileReference; name = "SimpleLineIcons.ttf"; path = "../node_modules/native-base/Fonts/SimpleLineIcons.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		F09541ED55B34F9EB74E8152 /* Zocial.ttf */ = {isa = PBXFileReference; name = "Zocial.ttf"; path = "../node_modules/native-base/Fonts/Zocial.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		BB7F92ED7BE04F9BA49E24EF /* AntDesign.ttf */ = {isa = PBXFileReference; name = "AntDesign.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/AntDesign.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		889A55A70BB143F1A68E994C /* Entypo.ttf */ = {isa = PBXFileReference; name = "Entypo.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/Entypo.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		103635A848ED4CB09079ADB7 /* EvilIcons.ttf */ = {isa = PBXFileReference; name = "EvilIcons.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/EvilIcons.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		C7CEEAC5A5B84DEBBA7ADE64 /* Feather.ttf */ = {isa = PBXFileReference; name = "Feather.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/Feather.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		164B8C28A74148088E17CAF8 /* FontAwesome.ttf */ = {isa = PBXFileReference; name = "FontAwesome.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		3162174D443540859871EE05 /* FontAwesome5_Brands.ttf */ = {isa = PBXFileReference; name = "FontAwesome5_Brands.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		C852A77F27854700B860BC2A /* FontAwesome5_Regular.ttf */ = {isa = PBXFileReference; name = "FontAwesome5_Regular.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		E37E2E0A03C2430D884B6F19 /* FontAwesome5_Solid.ttf */ = {isa = PBXFileReference; name = "FontAwesome5_Solid.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		72A1E2FBB8174E969803D7FD /* Fontisto.ttf */ = {isa = PBXFileReference; name = "Fontisto.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/Fontisto.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		9F00B84D147B451DB808AC64 /* Foundation.ttf */ = {isa = PBXFileReference; name = "Foundation.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/Foundation.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		11B86AFB0A3749A5A2D1BFAB /* Ionicons.ttf */ = {isa = PBXFileReference; name = "Ionicons.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		D6161819E5084BF29C34FD82 /* MaterialCommunityIcons.ttf */ = {isa = PBXFileReference; name = "MaterialCommunityIcons.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		82D14568A7D74C66936CB4ED /* MaterialIcons.ttf */ = {isa = PBXFileReference; name = "MaterialIcons.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/MaterialIcons.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		0E61F9DA07904A08B9C023BA /* Octicons.ttf */ = {isa = PBXFileReference; name = "Octicons.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/Octicons.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		751C33D91EA44A2DB7FB0B41 /* SimpleLineIcons.ttf */ = {isa = PBXFileReference; name = "SimpleLineIcons.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/SimpleLineIcons.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		C88B41CB101B4C1290E986C5 /* Zocial.ttf */ = {isa = PBXFileReference; name = "Zocial.ttf"; path = "../node_modules/react-native-vector-icons/Fonts/Zocial.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4781E0B4A5D006451C7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48D1E0B4A5D006451C7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* hr_systemTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* hr_systemTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = hr_systemTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* hr_system */ = {
			isa = PBXGroup;
			children = (
				008F07F21AC5B25A0029DE68 /* main.jsbundle */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.m */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB11A68108700A75B9A /* LaunchScreen.xib */,
				13B07FB71A68108700A75B9A /* main.m */,
			);
			name = hr_system;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				ED2971642150620600B7C4FE /* JavaScriptCore.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* hr_system */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* hr_systemTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				107E32F38008446BB8E0A8AB /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* hr_system.app */,
				00E356EE1AD99517003FC87E /* hr_systemTests.xctest */,
				2D02E47B1E0B4A5D006451C7 /* hr_system-tvOS.app */,
				2D02E4901E0B4A5D006451C7 /* hr_system-tvOSTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		107E32F38008446BB8E0A8AB /* Resources */ = {
			isa = "PBXGroup";
			children = (
				47AD9A88C2DE49C48E5464CA /* AntDesign.ttf */,
				CB78664225B040FAAEB6255E /* Entypo.ttf */,
				44F28E6F252F472586CD79AB /* EvilIcons.ttf */,
				2DADA19A193F424A8DCD5E89 /* Feather.ttf */,
				F4847D75A1814D7393DB1AF5 /* FontAwesome.ttf */,
				D4ED2F4D289F439DBCBF93B2 /* FontAwesome5_Brands.ttf */,
				775B472FD3C34C1D907F200F /* FontAwesome5_Regular.ttf */,
				FABE1FC1F910436585F7F4FC /* FontAwesome5_Solid.ttf */,
				F59C9DDE8F5145D8958617AE /* Foundation.ttf */,
				A157756C4801438B950401B3 /* Ionicons.ttf */,
				C78AD324A9184B22B8D12E9E /* MaterialCommunityIcons.ttf */,
				2F181EF9BAEC479E9A82FFA5 /* MaterialIcons.ttf */,
				8F104F21B66B4A609FFE41D5 /* Octicons.ttf */,
				4BAB168DB1464DCD9DCCABD9 /* Roboto_medium.ttf */,
				5CBE7368034B4F40AE9BD997 /* Roboto.ttf */,
				51A3CA6583BA4454A074F987 /* rubicon-icon-font.ttf */,
				8D3C26FC32684429B3658607 /* SimpleLineIcons.ttf */,
				F09541ED55B34F9EB74E8152 /* Zocial.ttf */,
				BB7F92ED7BE04F9BA49E24EF /* AntDesign.ttf */,
				889A55A70BB143F1A68E994C /* Entypo.ttf */,
				103635A848ED4CB09079ADB7 /* EvilIcons.ttf */,
				C7CEEAC5A5B84DEBBA7ADE64 /* Feather.ttf */,
				164B8C28A74148088E17CAF8 /* FontAwesome.ttf */,
				3162174D443540859871EE05 /* FontAwesome5_Brands.ttf */,
				C852A77F27854700B860BC2A /* FontAwesome5_Regular.ttf */,
				E37E2E0A03C2430D884B6F19 /* FontAwesome5_Solid.ttf */,
				72A1E2FBB8174E969803D7FD /* Fontisto.ttf */,
				9F00B84D147B451DB808AC64 /* Foundation.ttf */,
				11B86AFB0A3749A5A2D1BFAB /* Ionicons.ttf */,
				D6161819E5084BF29C34FD82 /* MaterialCommunityIcons.ttf */,
				82D14568A7D74C66936CB4ED /* MaterialIcons.ttf */,
				0E61F9DA07904A08B9C023BA /* Octicons.ttf */,
				751C33D91EA44A2DB7FB0B41 /* SimpleLineIcons.ttf */,
				C88B41CB101B4C1290E986C5 /* Zocial.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
			path = "";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* hr_systemTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "hr_systemTests" */;
			buildPhases = (
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = hr_systemTests;
			productName = hr_systemTests;
			productReference = 00E356EE1AD99517003FC87E /* hr_systemTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* hr_system */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "hr_system" */;
			buildPhases = (
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = hr_system;
			productName = "hr_system";
			productReference = 13B07F961A680F5B00A75B9A /* hr_system.app */;
			productType = "com.apple.product-type.application";
		};
		2D02E47A1E0B4A5D006451C7 /* hr_system-tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2D02E4BA1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "hr_system-tvOS" */;
			buildPhases = (
				FD10A7F122414F3F0027D42C /* Start Packager */,
				2D02E4771E0B4A5D006451C7 /* Sources */,
				2D02E4781E0B4A5D006451C7 /* Frameworks */,
				2D02E4791E0B4A5D006451C7 /* Resources */,
				2D02E4CB1E0B4B27006451C7 /* Bundle React Native Code And Images */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "hr_system-tvOS";
			productName = "hr_system-tvOS";
			productReference = 2D02E47B1E0B4A5D006451C7 /* hr_system-tvOS.app */;
			productType = "com.apple.product-type.application";
		};
		2D02E48F1E0B4A5D006451C7 /* hr_system-tvOSTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2D02E4BB1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "hr_system-tvOSTests" */;
			buildPhases = (
				2D02E48C1E0B4A5D006451C7 /* Sources */,
				2D02E48D1E0B4A5D006451C7 /* Frameworks */,
				2D02E48E1E0B4A5D006451C7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2D02E4921E0B4A5D006451C7 /* PBXTargetDependency */,
			);
			name = "hr_system-tvOSTests";
			productName = "hr_system-tvOSTests";
			productReference = 2D02E4901E0B4A5D006451C7 /* hr_system-tvOSTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 940;
				ORGANIZATIONNAME = Facebook;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					2D02E47A1E0B4A5D006451C7 = {
						CreatedOnToolsVersion = 8.2.1;
						ProvisioningStyle = Automatic;
					};
					2D02E48F1E0B4A5D006451C7 = {
						CreatedOnToolsVersion = 8.2.1;
						ProvisioningStyle = Automatic;
						TestTargetID = 2D02E47A1E0B4A5D006451C7;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "hr_system" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* hr_system */,
				00E356ED1AD99517003FC87E /* hr_systemTests */,
				2D02E47A1E0B4A5D006451C7 /* hr_system-tvOS */,
				2D02E48F1E0B4A5D006451C7 /* hr_system-tvOSTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				13B07FBD1A68108700A75B9A /* LaunchScreen.xib in Resources */,
				8E150925C27A43E9B3B89F59 /* AntDesign.ttf in Resources */,
				B1B886E4383A4AB1913DB163 /* Entypo.ttf in Resources */,
				1BDF985F83794DE6A7F53561 /* EvilIcons.ttf in Resources */,
				0162DD1A38D649D1AD735627 /* Feather.ttf in Resources */,
				5E67A468E9244D2592A6F3D8 /* FontAwesome.ttf in Resources */,
				432514C48620460AA775A657 /* FontAwesome5_Brands.ttf in Resources */,
				7A3E74DFB9FB49A2ADE493B4 /* FontAwesome5_Regular.ttf in Resources */,
				735F406427A8496EB2871462 /* FontAwesome5_Solid.ttf in Resources */,
				5F868588D1A746C3921327A8 /* Foundation.ttf in Resources */,
				1E7C9F75244545F59B3C0B26 /* Ionicons.ttf in Resources */,
				2F537DB1EA034A679497ADFC /* MaterialCommunityIcons.ttf in Resources */,
				B0739E2C30F9423AA711741A /* MaterialIcons.ttf in Resources */,
				EE19F0F910D1423E9CC6919F /* Octicons.ttf in Resources */,
				71E0BFE80A254D6B99D0FB94 /* Roboto_medium.ttf in Resources */,
				95684421D2964472BF8981E0 /* Roboto.ttf in Resources */,
				3D211027B5DF4FBFB2110140 /* rubicon-icon-font.ttf in Resources */,
				0F7D6016E9E7464682D18623 /* SimpleLineIcons.ttf in Resources */,
				9B3AB02D6176492E83F67863 /* Zocial.ttf in Resources */,
				B80FEB5920184F90ABA6151A /* AntDesign.ttf in Resources */,
				FC9CC1D04DA24FAAA3A97BB3 /* Entypo.ttf in Resources */,
				8AD08489F8334FAEAA70866D /* EvilIcons.ttf in Resources */,
				82C440EF55634EC5A4067EB3 /* Feather.ttf in Resources */,
				EDBB09D3FA534951ACF8D935 /* FontAwesome.ttf in Resources */,
				3F8C5641F99E48B798DD9016 /* FontAwesome5_Brands.ttf in Resources */,
				2A1045037712427691DCEA8C /* FontAwesome5_Regular.ttf in Resources */,
				32A4360E3566462288D6BD1E /* FontAwesome5_Solid.ttf in Resources */,
				8D7B431C11B040F3BEA86E10 /* Fontisto.ttf in Resources */,
				AAF8038F188448199AB1590B /* Foundation.ttf in Resources */,
				F7080C8FAD6846D092AF5EAA /* Ionicons.ttf in Resources */,
				17ABC32413674396A0489EB8 /* MaterialCommunityIcons.ttf in Resources */,
				9A07C7EA72F6401483522743 /* MaterialIcons.ttf in Resources */,
				00E0BB5DBCE448F28522CB2E /* Octicons.ttf in Resources */,
				5D76FEC260664688B14D8BFF /* SimpleLineIcons.ttf in Resources */,
				559913FDCB3349068E572F87 /* Zocial.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4791E0B4A5D006451C7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D02E4BD1E0B4A84006451C7 /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48E1E0B4A5D006451C7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh";
		};
		2D02E4CB1E0B4B27006451C7 /* Bundle React Native Code And Images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native Code And Images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh";
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F122414F3F0027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00E356F31AD99517003FC87E /* hr_systemTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4771E0B4A5D006451C7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D02E4BF1E0B4AB3006451C7 /* main.m in Sources */,
				2D02E4BC1E0B4A80006451C7 /* AppDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48C1E0B4A5D006451C7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2DCD954D1E0B4F2C00145EB5 /* hr_systemTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* hr_system */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
		2D02E4921E0B4A5D006451C7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2D02E47A1E0B4A5D006451C7 /* hr_system-tvOS */;
			targetProxy = 2D02E4911E0B4A5D006451C7 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		13B07FB11A68108700A75B9A /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				13B07FB21A68108700A75B9A /* Base */,
			);
			name = LaunchScreen.xib;
			path = hr_system;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = hr_systemTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/hr_system.app/hr_system";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = hr_systemTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/hr_system.app/hr_system";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = NO;
				INFOPLIST_FILE = hr_system/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = hr_system;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = hr_system/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = hr_system;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		2D02E4971E0B4A5E006451C7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "App Icon & Top Shelf Image";
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "hr_system-tvOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.hr_system-tvOS";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 9.2;
			};
			name = Debug;
		};
		2D02E4981E0B4A5E006451C7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "App Icon & Top Shelf Image";
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "hr_system-tvOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.hr_system-tvOS";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 9.2;
			};
			name = Release;
		};
		2D02E4991E0B4A5E006451C7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "hr_system-tvOSTests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.hr_system-tvOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/hr_system-tvOS.app/hr_system-tvOS";
				TVOS_DEPLOYMENT_TARGET = 10.1;
			};
			name = Debug;
		};
		2D02E49A1E0B4A5E006451C7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "hr_system-tvOSTests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.hr_system-tvOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/hr_system-tvOS.app/hr_system-tvOS";
				TVOS_DEPLOYMENT_TARGET = 10.1;
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "hr_systemTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "hr_system" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D02E4BA1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "hr_system-tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D02E4971E0B4A5E006451C7 /* Debug */,
				2D02E4981E0B4A5E006451C7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D02E4BB1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "hr_system-tvOSTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D02E4991E0B4A5E006451C7 /* Debug */,
				2D02E49A1E0B4A5E006451C7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "hr_system" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
