# Script de Instalacao TacticalRMM - NVirtual
# Baseado no script original de: https://github.com/bradhawkins85
# Modificado para solicitar Client ID e Site ID interativamente

# Configuracao da janela
$Host.UI.RawUI.WindowTitle = "Instalacao TacticalRMM - NVirtual"

# Funcao para exibir cabecalho
function Show-Header {
    Clear-Host
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "   INSTALACAO TACTICALRMM - NVIRTUAL    " -ForegroundColor Green
    Write-Host "      Desenvoldo por: Paulo Matheus     " -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Este Windows acabou de ser instalado neste computador e DEVE ser adicionada ao sistema imediatamente." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Caso nao saiba como prosseguir, veja o artigo: Instalacao do TacticalRMM no Moviedesk ou contacte a equipe Omega." -ForegroundColor Blue
    Write-Host ""
    
}

# Funcao para validar entrada numerica
function Get-ValidatedInput {
    param(
        [string]$Prompt,
        [string]$Type = "ID"
    )

    do {
        $userInput = Read-Host $Prompt
        if ([string]::IsNullOrWhiteSpace($userInput)) {
            Write-Host "Erro: $Type nao pode estar vazio!" -ForegroundColor Red
            Write-Host ""
            continue
        }
        if ($userInput -notmatch '^\d+$') {
            Write-Host "Erro: $Type deve conter apenas numeros!" -ForegroundColor Red
            Write-Host ""
            continue
        }
        return $userInput
    } while ($true)
}

# Inicio do script
Show-Header

# Solicita dados do usuario
$clientid = Get-ValidatedInput "Digite o ID do Cliente" "ID do Cliente"
$siteid = Get-ValidatedInput "Digite o ID do Site" "ID do Site"

# Exibe configuracoes
Write-Host ""
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "Configuracoes:" -ForegroundColor Yellow
Write-Host "Cliente ID: $clientid" -ForegroundColor White
Write-Host "Site ID: $siteid" -ForegroundColor White
Write-Host "Tipo: Workstation" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Yellow
Write-Host ""

# Confirma instalacao
do {
    $confirm = Read-Host "Confirma a instalacao? (S/N)"
} while ($confirm -notmatch '^[SsNn]$')

if ($confirm -match '^[Nn]$') {
    Write-Host "Instalacao cancelada pelo usuario." -ForegroundColor Yellow
    Read-Host "Pressione Enter para sair"
    exit
}

Write-Host ""
Write-Host "Iniciando instalacao do TacticalRMM..." -ForegroundColor Green
Write-Host ""

# Configuracoes do script original
$innosetup = 'tacticalagent-v2.9.1-windows-amd64.exe'
$api = '"https://api.centralmesh.nvirtual.com.br"'
$agenttype = '"workstation"'
$power = 0
$rdp = 0
$ping = 1
$auth = '"b21afb9812ea2676e30e8b9a40f32a4beb5b8033a782ca3612fc379d4d76787a"'
$downloadlink = 'https://github.com/amidaware/rmmagent/releases/download/v2.9.1/tacticalagent-v2.9.1-windows-amd64.exe'
$apilink = $downloadlink.split('/')

[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12

$serviceName = 'tacticalrmm'
If (Get-Service $serviceName -ErrorAction SilentlyContinue) {
    Write-Host 'Tactical RMM ja esta instalado!' -ForegroundColor Yellow
    Read-Host "Pressione Enter para sair"
    exit
} Else {
    $OutPath = $env:TMP
    $output = $innosetup

    $installArgs = @('-m install --api ', "$api", '--client-id', $clientid, '--site-id', $siteid, '--agent-type', "$agenttype", '--auth', "$auth")

    if ($power) {
        $installArgs += "--power"
    }

    if ($rdp) {
        $installArgs += "--rdp"
    }

    if ($ping) {
        $installArgs += "--ping"
    }

    Try
    {
        $DefenderStatus = Get-MpComputerStatus | Select-Object AntivirusEnabled
        if ($DefenderStatus -match "True") {
            Write-Host "Configurando exclusoes no Windows Defender..." -ForegroundColor Cyan
            Add-MpPreference -ExclusionPath 'C:\Program Files\TacticalAgent\*'
            Add-MpPreference -ExclusionPath 'C:\Program Files\Mesh Agent\*'
            Add-MpPreference -ExclusionPath 'C:\ProgramData\TacticalRMM\*'
        }
    }
    Catch {
        # pass
    }

    Write-Host "Verificando conectividade com o servidor..." -ForegroundColor Cyan
    $X = 0
    do {
      Write-Output "Aguardando rede..."
      Start-Sleep -s 5
      $X += 1
    } until(($connectresult = Test-NetConnection $apilink[2] -Port 443 | Where-Object { $_.TcpTestSucceeded }) -or $X -eq 3)

    if ($connectresult.TcpTestSucceeded -eq $true){
        Try
        {
            Write-Host "Baixando o agente TacticalRMM..." -ForegroundColor Cyan
            Invoke-WebRequest -Uri $downloadlink -OutFile $OutPath\$output

            Write-Host "Instalando o agente..." -ForegroundColor Cyan
            Start-Process -FilePath $OutPath\$output -ArgumentList ('/VERYSILENT /SUPPRESSMSGBOXES') -Wait

            Write-Host "Extraindo arquivos..." -ForegroundColor Cyan
            Start-Sleep -s 5

            Write-Host "Configurando o agente..." -ForegroundColor Cyan
            Start-Process -FilePath "C:\Program Files\TacticalAgent\tacticalrmm.exe" -ArgumentList $installArgs -Wait

            Write-Host ""
            Write-Host "========================================" -ForegroundColor Green
            Write-Host "  INSTALACAO CONCLUIDA COM SUCESSO!" -ForegroundColor Green
            Write-Host "========================================" -ForegroundColor Green
            Write-Host "Cliente ID: $clientid" -ForegroundColor White
            Write-Host "Site ID: $siteid" -ForegroundColor White
            Write-Host "Tipo: Workstation" -ForegroundColor White
            Write-Host "========================================" -ForegroundColor Green

            exit 0
        }
        Catch
        {
            $ErrorMessage = $_.Exception.Message
            $FailedItem = $_.Exception.ItemName
            Write-Host ""
            Write-Host "========================================" -ForegroundColor Red
            Write-Host "     ERRO NA INSTALACAO!" -ForegroundColor Red
            Write-Host "========================================" -ForegroundColor Red
            Write-Host "Erro: $ErrorMessage $FailedItem" -ForegroundColor Red
            Write-Host "========================================" -ForegroundColor Red
            exit 1
        }
        Finally
        {
            Remove-Item -Path $OutPath\$output -ErrorAction SilentlyContinue
        }
    } else {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Red
        Write-Host "     ERRO DE CONECTIVIDADE!" -ForegroundColor Red
        Write-Host "========================================" -ForegroundColor Red
        Write-Host "Nao foi possivel conectar ao servidor" -ForegroundColor Red
        Write-Host "Verifique a conexao com a internet" -ForegroundColor Red
        Write-Host "========================================" -ForegroundColor Red
    }
}

Write-Host ""
Read-Host "Pressione Enter para sair"
