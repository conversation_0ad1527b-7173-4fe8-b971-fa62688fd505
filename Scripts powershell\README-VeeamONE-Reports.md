# 📊 Scripts para Envio Manual de Relatórios Veeam ONE

Este conjunto de scripts PowerShell permite disparar manualmente os relatórios que estão configurados no "Scheduling" do Veeam ONE, sem precisar aguardar o horário agendado.

## 📁 Arquivos Incluídos

### 1. **Enviar-RelatoriosVeeamONE.ps1** (Principal)
Script completo que utiliza a API REST do Veeam ONE para gerenciar relatórios.

**Características:**
- ✅ Utiliza API REST do Veeam ONE
- ✅ Autenticação segura
- ✅ Monitoramento de execução
- ✅ Logs detalhados
- ✅ Menu interativo

### 2. **Enviar-RelatoriosVeeamONE-Cmdlets.ps1** (Alternativo)
Script que utiliza cmdlets PowerShell nativos do Veeam.

**Características:**
- ✅ Usa cmdlets PowerShell do Veeam
- ✅ Funciona sem API REST
- ✅ Busca automática de relatórios
- ✅ Métodos alternativos de execução

### 3. **Enviar-RelatoriosVeeamONE-Simples.ps1** (Simplificado)
Versão simplificada e direta para uso rápido.

**Características:**
- ✅ Interface simples e intuitiva
- ✅ Execução direta via linha de comando
- ✅ Menu colorido e amigável
- ✅ Ideal para uso cotidiano

### 4. **VeeamONE-Config.json** (Configuração)
Arquivo de configuração com parâmetros personalizáveis.

## 🚀 Como Usar

### Método 1: Script Principal (Recomendado)
```powershell
# Listar relatórios disponíveis
.\Enviar-RelatoriosVeeamONE.ps1 -ListOnly

# Executar relatório específico
.\Enviar-RelatoriosVeeamONE.ps1 -ReportName "Backup Status Report"

# Executar todos os relatórios
.\Enviar-RelatoriosVeeamONE.ps1 -ExecuteAll

# Menu interativo
.\Enviar-RelatoriosVeeamONE.ps1
```

### Método 2: Script Simplificado (Uso Rápido)
```powershell
# Menu interativo
.\Enviar-RelatoriosVeeamONE-Simples.ps1

# Executar relatório específico
.\Enviar-RelatoriosVeeamONE-Simples.ps1 -ReportName "Backup Status"

# Executar todos os relatórios
.\Enviar-RelatoriosVeeamONE-Simples.ps1 -All
```

### Método 3: Script com Cmdlets
```powershell
# Usar quando a API REST não estiver disponível
.\Enviar-RelatoriosVeeamONE-Cmdlets.ps1 -ListOnly
.\Enviar-RelatoriosVeeamONE-Cmdlets.ps1 -ReportName "Infrastructure Overview"
```

## ⚙️ Configuração

### Pré-requisitos
- ✅ Veeam ONE instalado e configurado
- ✅ PowerShell 5.1 ou superior
- ✅ Permissões adequadas para acessar o Veeam ONE
- ✅ Relatórios já configurados no "Scheduling" do Veeam ONE

### Configuração do Arquivo JSON
Edite o arquivo `VeeamONE-Config.json` para personalizar:

```json
{
    "VeeamOneServer": "seu-servidor-veeam",
    "Port": 9393,
    "UseHTTPS": true,
    "ReportSettings": {
        "ExecutionTimeout": 300,
        "RetryAttempts": 3
    }
}
```

## 📋 Relatórios Suportados

Os scripts funcionam com qualquer relatório configurado no Veeam ONE, incluindo:

- 📊 **Backup Status Report** - Status dos backups
- 🏗️ **Infrastructure Overview** - Visão geral da infraestrutura  
- ⚡ **VM Performance Report** - Performance das VMs
- 💾 **Storage Report** - Relatório de armazenamento
- 📈 **Capacity Planning Report** - Planejamento de capacidade
- 📊 **Backup Job Statistics** - Estatísticas dos jobs
- 🖥️ **Virtual Machine Report** - Relatório de VMs
- 💿 **Datastore Report** - Relatório de datastores
- 🖥️ **Host Performance Report** - Performance dos hosts

## 🔧 Solução de Problemas

### Problema: "Veeam ONE não encontrado"
**Solução:**
1. Verifique se o Veeam ONE está instalado
2. Confirme o caminho de instalação
3. Execute como Administrador

### Problema: "Erro de autenticação"
**Solução:**
1. Verifique as credenciais
2. Confirme permissões no Veeam ONE
3. Teste conectividade com o servidor

### Problema: "Nenhum relatório encontrado"
**Solução:**
1. Verifique se há relatórios configurados no Scheduling
2. Confirme permissões de leitura
3. Use o script alternativo com cmdlets

### Problema: "Cmdlets não encontrados"
**Solução:**
1. Instale o PowerShell do Veeam
2. Execute: `Add-PSSnapin VeeamPSSnapin`
3. Use o script baseado em API REST

## 📝 Logs e Monitoramento

### Localização dos Logs
- **Windows Event Log**: Application > Veeam
- **Arquivo de Log**: `C:\Logs\VeeamONE-Reports.log`
- **Console**: Output colorido em tempo real

### Níveis de Log
- 🔵 **INFO**: Informações gerais
- 🟡 **WARNING**: Avisos importantes
- 🔴 **ERROR**: Erros que impedem execução
- 🟢 **SUCCESS**: Operações bem-sucedidas

## 🔄 Automação

### Agendamento via Task Scheduler
```powershell
# Criar tarefa agendada para execução diária
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\Scripts\Enviar-RelatoriosVeeamONE-Simples.ps1 -All"
$trigger = New-ScheduledTaskTrigger -Daily -At "02:00"
Register-ScheduledTask -TaskName "Veeam ONE Reports Manual" -Action $action -Trigger $trigger
```

### Integração com Outros Sistemas
Os scripts podem ser integrados com:
- 📧 **Sistemas de email** para notificações
- 📊 **Dashboards** para monitoramento
- 🔔 **Sistemas de alerta** para falhas
- 📋 **ITSM** para tickets automáticos

## 🆘 Suporte

### Contato
- **Desenvolvedor**: Paulo Matheus - NVirtual
- **Email**: [seu-email]
- **Versão**: 1.0

### Contribuições
Para melhorias ou correções:
1. Teste em ambiente de desenvolvimento
2. Documente as alterações
3. Mantenha compatibilidade com versões anteriores

## 📄 Licença

Este script é fornecido "como está" para uso interno da NVirtual.
Teste sempre em ambiente de desenvolvimento antes de usar em produção.

---

**Última atualização**: $(Get-Date -Format "dd/MM/yyyy")
**Versão**: 1.0
