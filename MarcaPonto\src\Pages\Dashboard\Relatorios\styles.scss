@import '../../../Styles/responsive';

.relatorios__wrapper{
    display: flex;
    flex-direction: row;

    .rel__todos{
        flex: 1;
        margin: 0;

        @include sm{
            margin: 0 50px 0 0;
        }

        .curr__rel{
            padding: 20px;
            background-color: #fff;
            margin: 20px 0;
            border-radius: 20px;

            &.has__bridge{
                position: relative;
                // display: none;

                &::after{
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    top: 0;
                    width: 4px;
                    background-color: #3839ff;
                }

                @include sm{
                    &::before{
                        content: '';
                        position: absolute;
                        left: 80%;
                        top: 0;
                        bottom: 0;
                        width: 38%;
                        background-color: #fff;
    
                        @include xxl{
                            width: 30%;
                        }
                    }
                }
            }

            &:first-child{
                margin-top: 10px;
            }

            h3{
                color: #040e4b;
                margin-bottom: 2px;
            }

            p{
                color: #9c9c9cee;
                font-size: 13px;
            }

            &:hover{
                box-shadow: 1px 7px 7px #e7e7e7;
                cursor: pointer;
                transform: scale(1.02);
            }
        }
    }

    .rel__preview{
        flex: 2;
        padding: 40px;
        display: none;
        justify-content: center;
        align-items: center;
        background: #fff;
        margin-top: 10px;
        border-radius: 20px;

        iframe{
            width: 100%;
            height: 100%;
        }

        @include sm{
            display: flex;
        }
    }
}