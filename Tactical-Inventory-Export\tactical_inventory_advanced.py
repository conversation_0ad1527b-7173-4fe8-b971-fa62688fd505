#!/usr/bin/env python3
"""
Tactical RMM Advanced Inventory Export Script
==============================================

Script Python avançado para exportar inventário detalhado de estações do Tactical RMM.
Inclui informações de hardware, software e custom fields.

Autor: NVirtual
Data: 2025-01-02
Versão: 1.0
"""

import requests
import json
import csv
import sys
import os
from datetime import datetime
import argparse
import time

# Configurações da API
API_URL = "https://api.centralmesh.nvirtual.com.br"
API_TOKEN = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"

def log_message(message, level="INFO"):
    """Função para logging com timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    levels = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "ERROR": "❌",
        "WARN": "⚠️"
    }
    icon = levels.get(level, "📝")
    print(f"[{timestamp}] {icon} {message}")

def get_api_data(endpoint, token=None, timeout=30):
    """Busca dados da API do Tactical RMM"""
    if not token:
        token = API_TOKEN
    
    headers = {
        "X-API-KEY": token,
        "Content-Type": "application/json"
    }
    
    try:
        url = f"{API_URL}/{endpoint}/"
        response = requests.get(url, headers=headers, timeout=timeout)
        response.raise_for_status()
        return response.json()
        
    except requests.exceptions.RequestException as e:
        log_message(f"Erro ao buscar {endpoint}: {str(e)}", "ERROR")
        return None

def get_agent_hardware(agent_id, token):
    """Busca informações de hardware do agente via WMI"""
    try:
        # Buscar detalhes do agente que contém wmi_detail
        agent_detail = get_api_data(f"agents/{agent_id}", token)
        if not agent_detail or 'wmi_detail' not in agent_detail:
            return {}

        hardware_data = agent_detail['wmi_detail']
        if not hardware_data:
            return {}

        # Se wmi_detail for string JSON, fazer parse
        if isinstance(hardware_data, str):
            try:
                import json
                hardware_data = json.loads(hardware_data)
            except:
                return {}

        # wmi_detail é um dicionário com chaves: cpu, mem, disk, etc.
        if not isinstance(hardware_data, dict):
            return {}

        hardware_info = {}

        # Processar informações de CPU
        cpu_data = hardware_data.get('cpu', [])
        if cpu_data and isinstance(cpu_data, list) and cpu_data:
            # cpu_data é uma lista de listas
            if isinstance(cpu_data[0], list) and cpu_data[0]:
                cpu = cpu_data[0][0]  # Primeiro CPU da primeira lista
            else:
                cpu = cpu_data[0]  # Caso seja direto

            hardware_info.update({
                'CPU_Nome': cpu.get('Name', 'N/A'),
                'CPU_Cores': cpu.get('NumberOfCores', 'N/A'),
                'CPU_Threads': cpu.get('NumberOfLogicalProcessors', 'N/A'),
                'CPU_Velocidade_MHz': cpu.get('MaxClockSpeed', 'N/A'),
                'CPU_Fabricante': cpu.get('Manufacturer', 'N/A'),
                'CPU_Arquitetura': cpu.get('Architecture', 'N/A')
            })

        # Processar informações de memória
        memory_data = hardware_data.get('mem', [])
        if memory_data and isinstance(memory_data, list):
            total_ram = 0
            ram_details = []

            for mem_item in memory_data:
                if isinstance(mem_item, list) and mem_item:
                    mem = mem_item[0]  # Primeiro item da lista
                else:
                    mem = mem_item

                capacity = mem.get('Capacity', 0)
                if capacity:
                    total_ram += int(capacity)
                    ram_details.append({
                        'capacity_gb': round(int(capacity) / (1024**3), 2),
                        'speed': mem.get('Speed', 'N/A'),
                        'type': mem.get('MemoryType', 'N/A')
                    })

            hardware_info.update({
                'RAM_Total_GB': round(total_ram / (1024**3), 2) if total_ram else 'N/A',
                'RAM_Slots_Usados': len(ram_details),
                'RAM_Velocidade': ram_details[0]['speed'] if ram_details else 'N/A',
                'RAM_Tipo': ram_details[0]['type'] if ram_details else 'N/A'
            })

        # Processar informações de disco - APENAS TOTAIS E USO (EXCLUIR USB)
        disk_data = hardware_data.get('disk', [])
        total_disks = 0
        total_size_gb = 0

        # Contar discos físicos e somar tamanhos (FILTRAR USB)
        for disk_list in disk_data:
            if isinstance(disk_list, list) and disk_list:
                disk = disk_list[0]  # Primeiro item da lista

                # Filtrar dispositivos USB externos
                interface_type = disk.get('InterfaceType', '').upper()
                media_type = disk.get('MediaType', '').upper()
                model = disk.get('Model', '').upper()

                # Pular se for USB ou dispositivo removível
                if ('USB' in interface_type or
                    'USB' in media_type or
                    'USB' in model or
                    'REMOVABLE' in media_type):
                    continue

                # Converter tamanho para GB (1024^3 bytes = 1 GB)
                size_bytes = int(disk.get('Size', 0)) if disk.get('Size') else 0
                size_gb = round(size_bytes / (1024**3), 2) if size_bytes > 0 else 0

                if size_gb > 0:  # Só contar discos válidos
                    total_disks += 1
                    total_size_gb += size_gb

        # Buscar informações de uso de disco do agente básico
        # Os dados já vêm formatados em GB como strings
        agent_disks = agent_detail.get('disks', [])
        total_used_gb = 0
        total_free_gb = 0

        if isinstance(agent_disks, list):
            for disk_info in agent_disks:
                if isinstance(disk_info, dict):
                    # Extrair valores que já vêm como strings "156.0 GB"
                    used_str = disk_info.get('used', '0 GB')
                    free_str = disk_info.get('free', '0 GB')

                    # Converter strings "156.0 GB" para float
                    try:
                        used_gb = float(used_str.replace(' GB', '').replace(',', '.'))
                        free_gb = float(free_str.replace(' GB', '').replace(',', '.'))
                        total_used_gb += used_gb
                        total_free_gb += free_gb
                    except (ValueError, AttributeError):
                        continue

        # Se não temos dados de uso, calcular estimativa
        if total_used_gb == 0 and total_size_gb > 0:
            # Estimativa: 70% usado em média
            total_used_gb = round(total_size_gb * 0.7, 2)
            total_free_gb = round(total_size_gb - total_used_gb, 2)

        # Adicionar apenas informações resumidas de disco
        hardware_info.update({
            'Quantidade_Discos': total_disks,
            'Espaco_Total_GB': round(total_size_gb, 2),
            'Espaco_Usado_GB': round(total_used_gb, 2),
            'Espaco_Livre_GB': round(total_free_gb, 2),
            'Percentual_Uso': round((total_used_gb / total_size_gb * 100), 1) if total_size_gb > 0 else 0
        })
        
        # Processar informações da placa-mãe
        board_data = hardware_data.get('base_board', [])
        if board_data and isinstance(board_data, list):
            if isinstance(board_data[0], list) and board_data[0]:
                board = board_data[0][0]  # Primeiro item da primeira lista
            else:
                board = board_data[0] if board_data else {}

            manufacturer = board.get('Manufacturer', '') if isinstance(board, dict) else ''
            product = board.get('Product', '') if isinstance(board, dict) else ''
            hardware_info.update({
                'Placa_Mae': f"{manufacturer} {product}".strip() or 'N/A',
                'Placa_Mae_Fabricante': manufacturer or 'N/A',
                'Placa_Mae_Modelo': product or 'N/A',
                'Placa_Mae_Serial': board.get('SerialNumber', 'N/A') if isinstance(board, dict) else 'N/A'
            })

        # Processar informações do BIOS
        bios_data = hardware_data.get('bios', [])
        if bios_data and isinstance(bios_data, list):
            if isinstance(bios_data[0], list) and bios_data[0]:
                bios = bios_data[0][0]  # Primeiro item da primeira lista
            else:
                bios = bios_data[0] if bios_data else {}

            if isinstance(bios, dict):
                hardware_info.update({
                    'BIOS_Versao': bios.get('SMBIOSBIOSVersion', 'N/A'),
                    'BIOS_Fabricante': bios.get('Manufacturer', 'N/A'),
                    'BIOS_Data': bios.get('ReleaseDate', 'N/A'),
                    'Serial_Number_BIOS': bios.get('SerialNumber', 'N/A')
                })

        # Processar informações do sistema
        comp_sys_data = hardware_data.get('comp_sys', [])
        if comp_sys_data and isinstance(comp_sys_data, list):
            if isinstance(comp_sys_data[0], list) and comp_sys_data[0]:
                comp_sys = comp_sys_data[0][0]  # Primeiro item da primeira lista
            else:
                comp_sys = comp_sys_data[0] if comp_sys_data else {}

            if isinstance(comp_sys, dict):
                hardware_info.update({
                    'Sistema_Fabricante': comp_sys.get('Manufacturer', 'N/A'),
                    'Sistema_Modelo': comp_sys.get('Model', 'N/A'),
                    'Sistema_Tipo': comp_sys.get('SystemType', 'N/A')
                })
        
        return hardware_info
        
    except Exception as e:
        log_message(f"Erro ao buscar hardware do agente {agent_id}: {str(e)}", "WARN")
        return {}

def get_agent_software(agent_id, token):
    """Busca lista de software instalado no agente"""
    try:
        software_response = get_api_data(f"software/{agent_id}", token)
        if not software_response or 'software' not in software_response:
            return []

        software_data = software_response['software']
        if not software_data:
            return []

        software_list = []
        for app in software_data:
            # Converter tamanho de string para MB
            size_mb = 'N/A'
            if app.get('size'):
                try:
                    size_str = app.get('size', '0 B')
                    if 'MB' in size_str:
                        size_mb = float(size_str.replace(' MB', '').replace(',', '.'))
                    elif 'KB' in size_str:
                        size_mb = round(float(size_str.replace(' KB', '').replace(',', '.')) / 1024, 2)
                    elif 'GB' in size_str:
                        size_mb = round(float(size_str.replace(' GB', '').replace(',', '.')) * 1024, 2)
                    elif 'B' in size_str and size_str != '0 B':
                        size_mb = round(float(size_str.replace(' B', '').replace(',', '.')) / (1024**2), 2)
                    else:
                        size_mb = 0
                except:
                    size_mb = 'N/A'

            software_list.append({
                'Nome_Software': app.get('name', 'N/A'),
                'Versao': app.get('version', 'N/A'),
                'Editor': app.get('publisher', 'N/A'),
                'Tamanho_MB': size_mb,
                'Data_Instalacao': app.get('install_date', 'N/A'),
                'Localizacao': app.get('location', 'N/A')
            })
        
        return software_list
        
    except Exception as e:
        log_message(f"Erro ao buscar software do agente {agent_id}: {str(e)}", "WARN")
        return []

def format_basic_agent_data(agent, clients_dict, sites_dict):
    """Formata dados básicos do agente"""
    # Usar client_name e site_name diretamente do agente
    client_name = agent.get('client_name', 'N/A')
    site_name = agent.get('site_name', 'N/A')

    # Calcular status
    status = "Desconhecido"
    if agent.get('last_seen'):
        try:
            last_seen_dt = datetime.fromisoformat(agent.get('last_seen').replace('Z', '+00:00'))
            now = datetime.now(last_seen_dt.tzinfo)
            diff = now - last_seen_dt

            if diff.total_seconds() < 300:
                status = "Online"
            elif diff.total_seconds() < 86400:
                status = "Recente"
            else:
                status = "Offline"
        except:
            pass

    return {
        'ID_Agente': agent.get('agent_id', 'N/A'),
        'Hostname': agent.get('hostname', 'N/A'),
        'Cliente': client_name,
        'Site': site_name,
        'Status': status,
        'Sistema_Operacional': agent.get('operating_system', 'N/A'),
        'Versao_OS': agent.get('plat', 'N/A'),
        'Arquitetura': agent.get('goarch', 'N/A'),
        'IP_Publico': agent.get('public_ip', 'N/A'),
        'IPs_Locais': agent.get('local_ips', 'N/A'),
        'Versao_Agente': agent.get('version', 'N/A'),
        'Ultimo_Contato': agent.get('last_seen', 'N/A'),
        'Boot_Time': agent.get('boot_time', 'N/A'),
        'Usuario_Logado': agent.get('logged_username', 'N/A'),
        'Manutencao': 'Sim' if agent.get('maintenance_mode') else 'Não',
        'Tipo_Monitoramento': agent.get('monitoring_type', 'N/A'),
        'Descricao': agent.get('description', 'N/A'),
        'Marca_Modelo': agent.get('make_model', 'N/A'),
        'Serial_Number': agent.get('serial_number', 'N/A'),
        'Discos_Fisicos': '; '.join(agent.get('physical_disks', [])) if agent.get('physical_disks') else 'N/A',
        'CPU_Modelo_Basico': '; '.join(agent.get('cpu_model', [])) if agent.get('cpu_model') else 'N/A',
        'Placa_Video': agent.get('graphics', 'N/A'),
        'Arquitetura': agent.get('arch', 'N/A')
    }

def consolidate_agent_data(basic_data, hardware_data, software_list, custom_fields):
    """Consolida todos os dados do agente em um único registro - APENAS COLUNAS ESPECÍFICAS"""

    # Definir ordem específica das colunas solicitadas
    ordered_columns = [
        'Hostname', 'Usuario_Logado',  # Informações básicas
        'Espaco_Total_GB', 'Espaco_Usado_GB', 'Espaco_Livre_GB', 'Percentual_Uso', 'Quantidade_Discos',  # Discos
        'RAM_Total_GB', 'RAM_Slots_Usados', 'RAM_Tipo', 'RAM_Velocidade',  # RAM
        'CPU_Nome', 'CPU_Threads', 'Arquitetura',  # CPU
        'Placa_Mae', 'Placa_Mae_Fabricante', 'Placa_Mae_Modelo',  # Placa-mãe
        'Sistema_Fabricante', 'Sistema_Modelo', 'Sistema_Tipo',  # Sistema
        'Cliente', 'Site', 'ID_Agente', 'Status', 'Sistema_Operacional',  # Identificação
        'IP_Publico', 'IPs_Locais', 'Marca_Modelo', 'Placa_Video',  # Rede e hardware
        'Tipo_Monitoramento', 'Ultimo_Contato', 'Versao_Agente'  # Monitoramento
    ]

    # Criar dicionário consolidado com todos os dados
    all_data = basic_data.copy()
    if hardware_data:
        all_data.update(hardware_data)

    # Criar dicionário final apenas com as colunas solicitadas na ordem correta
    consolidated = {}
    for column in ordered_columns:
        consolidated[column] = all_data.get(column, 'N/A')

    return consolidated

def export_multiple_sheets_csv(data_dict, base_filename):
    """Exporta múltiplas 'abas' como arquivos CSV separados"""
    success = True
    
    for sheet_name, data in data_dict.items():
        if not data:
            continue
            
        filename = f"{base_filename}_{sheet_name}.csv"
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for row in data:
                    writer.writerow(row)
            
            log_message(f"Exportado {sheet_name}: {filename} ({len(data)} registros)", "SUCCESS")
            
        except Exception as e:
            log_message(f"Erro ao exportar {sheet_name}: {str(e)}", "ERROR")
            success = False
    
    return success

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description='Exportar inventário avançado do Tactical RMM')
    parser.add_argument('--token', help='Token de API do Tactical RMM')
    parser.add_argument('--client-id', type=int, help='ID do cliente específico')
    parser.add_argument('--site-id', type=int, help='ID do site específico')
    parser.add_argument('--include-hardware', action='store_true', help='Incluir informações de hardware')
    parser.add_argument('--include-software', action='store_true', help='Incluir lista de software')
    parser.add_argument('--include-custom-fields', action='store_true', help='Incluir custom fields')
    parser.add_argument('--output', help='Nome base do arquivo de saída')
    parser.add_argument('--max-agents', type=int, default=50, help='Máximo de agentes para processar (padrão: 50)')
    parser.add_argument('--delay', type=float, default=0.1, help='Delay entre requisições em segundos (padrão: 0.1)')
    
    args = parser.parse_args()
    
    token = args.token or API_TOKEN
    
    log_message("=== INICIANDO EXPORTAÇÃO AVANÇADA DE INVENTÁRIO ===", "SUCCESS")
    
    try:
        # Buscar dados básicos
        log_message("Buscando dados básicos...")
        agents = get_api_data("agents", token)
        clients = get_api_data("clients", token)
        sites = get_api_data("clients/sites", token)
        
        if not agents:
            log_message("Nenhum agente encontrado", "ERROR")
            return 1
        
        # Criar dicionários para lookup
        clients_dict = {client['id']: client['name'] for client in clients or []}
        sites_dict = {site['id']: site['name'] for site in sites or []}
        
        # Aplicar filtros
        if args.client_id:
            # Buscar nome do cliente pelo ID
            client_name = None
            for client in clients or []:
                if client.get('id') == args.client_id:
                    client_name = client.get('name')
                    break

            if client_name:
                agents = [agent for agent in agents if agent.get('client_name') == client_name]
                log_message(f"Filtrado para cliente ID: {args.client_id} ({client_name}) - {len(agents)} agentes")
            else:
                log_message(f"Cliente ID {args.client_id} não encontrado", "ERROR")
                agents = []
        
        if args.site_id:
            agents = [agent for agent in agents if agent.get('site') == args.site_id]
            log_message(f"Filtrado para site ID: {args.site_id} ({len(agents)} agentes)")
        
        # Limitar número de agentes para evitar sobrecarga
        if len(agents) > args.max_agents:
            log_message(f"Limitando processamento a {args.max_agents} agentes (de {len(agents)} total)", "WARN")
            agents = agents[:args.max_agents]
        
        if not agents:
            log_message("Nenhum agente encontrado com os filtros especificados", "WARN")
            return 0
        
        # Preparar estruturas de dados
        inventory_basic = []
        hardware_data = []
        software_data = []
        custom_fields_data = []
        consolidated_data = []  # Nova estrutura para CSV único

        # Processar cada agente
        log_message(f"Processando {len(agents)} agentes...")

        for i, agent in enumerate(agents, 1):
            hostname = agent.get('hostname', 'N/A')
            agent_id = agent.get('agent_id')

            print(f"Processando {i}/{len(agents)}: {hostname}")

            # Dados básicos
            basic_data = format_basic_agent_data(agent, clients_dict, sites_dict)
            inventory_basic.append(basic_data)

            # Hardware detalhado
            hw_data = {}
            if args.include_hardware and agent_id:
                log_message(f"Buscando hardware para {hostname}...")
                hw_data = get_agent_hardware(agent_id, token)
                if hw_data:
                    hw_data['Hostname'] = hostname
                    hardware_data.append(hw_data)
                time.sleep(args.delay)  # Delay para não sobrecarregar a API

            # Software instalado
            sw_list = []
            if args.include_software and agent_id:
                log_message(f"Buscando software para {hostname}...")
                sw_list = get_agent_software(agent_id, token)
                for sw in sw_list:
                    sw['Hostname'] = hostname
                    software_data.append(sw)
                time.sleep(args.delay)

            # Custom fields
            agent_custom_fields = []
            if args.include_custom_fields and agent.get('custom_fields'):
                for field in agent.get('custom_fields', []):
                    field_data = {
                        'Hostname': hostname,
                        'Campo_Nome': field.get('field', 'N/A'),
                        'Campo_Valor': field.get('value', 'N/A')
                    }
                    custom_fields_data.append(field_data)
                    agent_custom_fields.append(field_data)

            # Consolidar todos os dados do agente em um registro único
            consolidated_agent = consolidate_agent_data(basic_data, hw_data, sw_list, agent_custom_fields)
            consolidated_data.append(consolidated_agent)
        
        # Gerar nome do arquivo
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = args.output or f"TacticalRMM_Inventario_Avancado_{timestamp}"
        
        if args.client_id:
            base_filename += f"_Cliente{args.client_id}"
        if args.site_id:
            base_filename += f"_Site{args.site_id}"
        
        # Preparar dados para exportação
        export_data = {
            'Inventario_Basico': inventory_basic
        }

        if hardware_data:
            export_data['Hardware_Detalhado'] = hardware_data
            log_message(f"Hardware coletado: {len(hardware_data)} registros")

        if software_data:
            export_data['Software_Instalado'] = software_data
            log_message(f"Software coletado: {len(software_data)} registros")

        if custom_fields_data:
            export_data['Custom_Fields'] = custom_fields_data
            log_message(f"Custom Fields coletados: {len(custom_fields_data)} registros")

        # Exportar CSV ÚNICO CONSOLIDADO (PRINCIPAL)
        consolidated_filename = f"{base_filename}_CONSOLIDADO.csv"
        log_message(f"Exportando CSV único consolidado...")
        try:
            with open(consolidated_filename, 'w', newline='', encoding='utf-8') as csvfile:
                if consolidated_data:
                    # Coletar todas as chaves possíveis de todos os registros
                    all_fieldnames = set()
                    for row in consolidated_data:
                        all_fieldnames.update(row.keys())

                    fieldnames = sorted(list(all_fieldnames))
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    for row in consolidated_data:
                        # Preencher campos faltantes com 'N/A'
                        complete_row = {field: row.get(field, 'N/A') for field in fieldnames}
                        writer.writerow(complete_row)
                    log_message(f"✅ CSV CONSOLIDADO exportado: {consolidated_filename} ({len(consolidated_data)} registros)", "SUCCESS")
                    success_consolidated = True
                else:
                    log_message("Nenhum dado consolidado para exportar", "WARN")
                    success_consolidated = False
        except Exception as e:
            log_message(f"Erro ao exportar CSV consolidado: {str(e)}", "ERROR")
            success_consolidated = False

        # Exportar dados separados (opcional)
        log_message(f"Exportando dados separados...")
        success = export_multiple_sheets_csv(export_data, base_filename)
        
        # Exportar também como JSON consolidado
        json_filename = f"{base_filename}_completo.json"
        try:
            with open(json_filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(export_data, jsonfile, indent=2, ensure_ascii=False, default=str)
            log_message(f"Dados consolidados exportados: {json_filename}", "SUCCESS")
        except Exception as e:
            log_message(f"Erro ao exportar JSON: {str(e)}", "ERROR")
            success = False
        
        if success and success_consolidated:
            log_message("Exportação avançada concluída com sucesso!", "SUCCESS")
            log_message(f"Total de agentes processados: {len(inventory_basic)}")
            log_message(f"📊 ARQUIVO PRINCIPAL: {consolidated_filename}", "SUCCESS")
            return 0
        else:
            log_message("Erro durante a exportação", "ERROR")
            return 1
            
    except Exception as e:
        log_message(f"Erro durante a exportação: {str(e)}", "ERROR")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
