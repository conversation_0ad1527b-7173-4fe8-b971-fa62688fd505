{"name": "marca-ponto", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "@types/jest": "^24.0.0", "@types/node": "^12.0.0", "@types/react": "^16.9.0", "@types/react-dom": "^16.9.0", "axios": "^0.21.1", "detect-browser-language": "^0.0.2", "formik": "^2.1.5", "js-cookie": "^2.2.1", "jspdf": "^2.1.1", "jspdf-autotable": "^3.5.13", "leaflet": "^1.7.1", "leaflet-draw": "^1.0.4", "node-sass": "^4.14.1", "query-string": "^6.13.5", "rc-table": "^7.10.0", "react": "^16.13.1", "react-animate-on-scroll": "^2.1.5", "react-confirm-alert": "^2.6.2", "react-content-loader": "^5.1.2", "react-data-table-component": "^6.11.5", "react-date-picker": "^8.0.3", "react-datepicker": "^3.2.2", "react-dom": "^16.13.1", "react-icons": "^3.11.0", "react-intl": "^5.8.3", "react-leaflet": "2.7.0", "react-leaflet-draw": "^0.19.0", "react-lottie": "^1.2.3", "react-router-dom": "^5.2.0", "react-scripts": "3.4.3", "react-tabs": "^3.1.1", "react-text-mask": "^5.4.3", "react-timekeeper": "^2.1.3", "react-toastify": "^6.0.8", "recharts": "^1.8.5", "styled-components": "^5.2.0", "typescript": "3.8.3", "use-position": "^0.0.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/js-cookie": "^2.2.6", "@types/leaflet-geosearch": "^2.7.1", "@types/query-string": "^6.3.0", "@types/react-animate-on-scroll": "^2.1.2", "@types/react-datepicker": "^3.1.1", "@types/react-intl": "^3.0.0", "@types/react-leaflet": "1.0.7", "@types/react-lottie": "^1.2.5", "@types/react-router-dom": "^5.1.5", "@types/react-table": "^7.0.23", "@types/react-tabs": "^2.3.2", "@types/react-text-mask": "^5.4.6", "@types/react-toastify": "^4.1.0", "@types/recharts": "^1.8.16", "@types/use-position": "^0.0.0"}}