#!/bin/bash

# Script de configuração automática para Raspberry Pi - NVirtual
# Este script será executado automaticamente no primeiro boot
# Autor: <PERSON> - NVirtual
# Data: $(date +%Y-%m-%d)

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a /var/log/raspberry-setup.log
}

error() {
    echo -e "${RED}[ERRO] $1${NC}" | tee -a /var/log/raspberry-setup.log
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}" | tee -a /var/log/raspberry-setup.log
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}" | tee -a /var/log/raspberry-setup.log
}

log "=== Iniciando configuração automática do Raspberry Pi - NVirtual ==="

# Configurações padrão
ZABBIX_HOSTNAME="temporario"
TACTICAL_CLIENT_ID="1"
TACTICAL_CLIENT_FILIAL="1"

# Detectar informações do sistema
DEVICE_SERIAL=$(cat /proc/cpuinfo | grep Serial | cut -d ' ' -f 2)
DEVICE_MODEL=$(cat /proc/device-tree/model 2>/dev/null | tr -d '\0' || echo "Unknown")

log "Informações do dispositivo:"
info "Modelo: $DEVICE_MODEL"
info "Serial: $DEVICE_SERIAL"

# Atualizar hostname baseado no serial (últimos 6 dígitos)
if [ -n "$DEVICE_SERIAL" ] && [ "$DEVICE_SERIAL" != "0000000000000000" ]; then
    SHORT_SERIAL=${DEVICE_SERIAL: -6}
    ZABBIX_HOSTNAME="rpi-$SHORT_SERIAL"
    log "Hostname definido como: $ZABBIX_HOSTNAME"
    
    # Atualizar hostname do sistema
    echo "$ZABBIX_HOSTNAME" | sudo tee /etc/hostname
    sudo sed -i "s/*********.*/*********\t$ZABBIX_HOSTNAME/" /etc/hosts
fi

# Aguardar conectividade de rede
log "Aguardando conectividade de rede..."
for i in {1..60}; do
    if ping -c 1 ******* >/dev/null 2>&1; then
        log "Conectividade de rede estabelecida"
        break
    fi
    if [ $i -eq 60 ]; then
        error "Não foi possível estabelecer conectividade de rede após 2 minutos"
    fi
    sleep 2
done

# Atualizar sistema
log "Atualizando sistema..."
sudo apt-get update -y
sudo apt-get upgrade -y

# Instalar dependências básicas
log "Instalando dependências básicas..."
sudo apt-get install -y \
    curl \
    wget \
    git \
    vim \
    htop \
    net-tools \
    traceroute \
    build-essential

# Baixar e executar o script de instalação do Zabbix
log "Baixando script de instalação do Zabbix/Tactical RMM..."
cd /tmp

# Baixar o script principal
wget -O install_zabbix.sh "https://raw.githubusercontent.com/paulomatheusgrr/nvirtual-projects/main/Zabbix-auto-instalation/install_zabbix_tactical_rmm.sh"
chmod +x install_zabbix.sh

# Criar script de automação para simular entradas do usuário
log "Criando script de automação..."
cat > auto_responses.sh << 'EOF'
#!/bin/bash

# Script para automatizar as respostas do instalador
echo "temporario"  # Nome do Zabbix Proxy
echo "1"           # ID do Cliente
echo "1"           # Filial do Cliente  
echo "dhcp"        # Manter DHCP
EOF

chmod +x auto_responses.sh

# Executar instalação automatizada
log "Executando instalação automatizada do Zabbix e Tactical RMM..."
log "Parâmetros: Hostname=$ZABBIX_HOSTNAME, Cliente=$TACTICAL_CLIENT_ID, Filial=$TACTICAL_CLIENT_FILIAL"

# Executar com timeout para evitar travamentos
timeout 1800 bash -c './auto_responses.sh | ./install_zabbix.sh' || {
    warning "Instalação demorou mais que 30 minutos ou falhou"
    log "Verificando se os serviços foram instalados..."
}

# Verificar se a instalação foi bem-sucedida
log "Verificando instalação..."

# Verificar Zabbix
if systemctl is-active --quiet zabbix-proxy && systemctl is-active --quiet zabbix-agent; then
    log "✅ Zabbix Proxy e Agent instalados e ativos"
else
    warning "❌ Problemas com serviços Zabbix"
    info "Status Zabbix Proxy: $(systemctl is-active zabbix-proxy)"
    info "Status Zabbix Agent: $(systemctl is-active zabbix-agent)"
fi

# Verificar Tactical RMM
if pgrep -f "tacticalagent" > /dev/null; then
    log "✅ Tactical RMM Agent está rodando"
else
    warning "❌ Tactical RMM Agent não encontrado"
fi

# Configurações adicionais específicas do Raspberry Pi
log "Aplicando configurações específicas do Raspberry Pi..."

# Habilitar SSH se não estiver habilitado
sudo systemctl enable ssh
sudo systemctl start ssh

# Configurar timezone para Brasil
sudo timedatectl set-timezone America/Sao_Paulo

# Otimizações para Raspberry Pi
log "Aplicando otimizações para Raspberry Pi..."

# Aumentar swap se necessário (para modelos com pouca RAM)
TOTAL_RAM=$(free -m | awk 'NR==2{print $2}')
if [ "$TOTAL_RAM" -lt 2048 ]; then
    log "RAM baixa detectada ($TOTAL_RAM MB). Configurando swap..."
    sudo dphys-swapfile swapoff
    sudo sed -i 's/CONF_SWAPSIZE=.*/CONF_SWAPSIZE=1024/' /etc/dphys-swapfile
    sudo dphys-swapfile setup
    sudo dphys-swapfile swapon
fi

# Configurar log rotation para evitar enchimento do SD card
log "Configurando rotação de logs..."
sudo tee /etc/logrotate.d/raspberry-setup > /dev/null << 'EOF'
/var/log/raspberry-setup.log {
    weekly
    rotate 4
    compress
    delaycompress
    missingok
    notifempty
}
EOF

# Criar arquivo de status da configuração
log "Criando arquivo de status..."
cat > /home/<USER>/setup-status.txt << EOF
Raspberry Pi Setup - NVirtual
=============================
Data da configuração: $(date)
Hostname: $ZABBIX_HOSTNAME
Device Serial: $DEVICE_SERIAL
Device Model: $DEVICE_MODEL

Serviços instalados:
- Zabbix Proxy: $(systemctl is-active zabbix-proxy 2>/dev/null || echo "não instalado")
- Zabbix Agent: $(systemctl is-active zabbix-agent 2>/dev/null || echo "não instalado")
- Tactical RMM: $(pgrep -f "tacticalagent" > /dev/null && echo "ativo" || echo "não encontrado")
- SSH: $(systemctl is-active ssh)

Configurações de rede:
- IP atual: $(hostname -I | awk '{print $1}')
- Gateway: $(ip route | grep default | awk '{print $3}')
- DNS: $(cat /etc/resolv.conf | grep nameserver | awk '{print $2}' | tr '\n' ' ')

Para verificar logs: tail -f /var/log/raspberry-setup.log
EOF

chown pi:pi /home/<USER>/setup-status.txt

log "=== Configuração automática concluída ==="
log "Arquivo de status criado em: /home/<USER>/setup-status.txt"
log "Logs salvos em: /var/log/raspberry-setup.log"

# Opcional: Reiniciar após configuração (descomente se necessário)
# log "Reiniciando sistema em 30 segundos..."
# sleep 30
# sudo reboot
