# 📊 Guia de Exportação CSV - Clientes e Máquinas

Este guia explica como gerar arquivos CSV que mostram as unidades dos clientes e quais máquinas existem em cada unidade.

## 🎯 Objetivo

Gerar relatórios CSV organizados por cliente (unidade) mostrando:
- Identificação da unidade do cliente
- Lista de máquinas em cada unidade
- Detalhes técnicos de cada máquina
- Resumos estatísticos por cliente

## 📋 Formatos de CSV Disponíveis

### 1. **CSV Detalhado** (uma linha por máquina)
```csv
Unidade_Cliente,Rede_Cliente,Total_Maquinas,IP_Maquina,Nome_Maquina,Sistema_Operacional,Tipo_Dispositivo,Metodo_Deteccao,TTL,Confianca_Percent,Portas_Abertas,Data_Hora_Scan
Cliente_001,***********/24,5,************,PC-<PERSON><PERSON><PERSON>,<PERSON>,Windows Desktop,TTL=128+Ports,128,90,"135(RPC); 445(SMB); 3389(RDP)",08/01/2025 14:30:15
Cliente_001,***********/24,5,************,PRINTER-HP,Linux,HP LaserJet P1102 Printer,TTL=64+Ports,64,95,"80(HTTP); 9100(Printer)",08/01/2025 14:30:18
```

### 2. **CSV Resumo** (uma linha por cliente)
```csv
Unidade_Cliente,Rede_Cliente,Total_Maquinas,Sistema_Operacional_Principal,Dispositivo_Principal,Lista_IPs,Lista_Hostnames,Data_Scan
Cliente_001,***********/24,5,Windows,Windows Desktop,"************; ************; ************","PC-ADMIN; PRINTER-HP; SERVER-01",08/01/2025 14:30:15
Cliente_002,***********/24,3,Linux,Linux Server,"************; ************; ************","SERVER-WEB; SERVER-DB; FIREWALL",08/01/2025 14:30:20
```

## 🚀 Como Usar

### **Método 1: Script Dedicado (Recomendado)**

```bash
# Gerar ambos os formatos CSV
python generate_client_csv.py -n ***********/24

# Apenas CSV detalhado
python generate_client_csv.py -n ***********/16 -f detailed

# Apenas CSV resumo
python generate_client_csv.py -n ***********/24 -f summary

# Com nome personalizado
python generate_client_csv.py -n ***********/24 -o "empresa_janeiro_2025"

# Scan rápido
python generate_client_csv.py -n ***********/24 -t 50 --timeout 1
```

### **Método 2: Scanner Principal (Automático)**

```bash
# ✨ AUTOMÁTICO: Gera ambos os CSVs automaticamente
python network_scanner.py -n ***********/24

# Com nome personalizado
python network_scanner.py -n ***********/24 -o "scan_empresa"

# Resultado automático:
# - scan_empresa_detalhado.csv
# - scan_empresa_resumo.csv
```

**🎯 Novidade:** O scanner principal agora **sempre gera CSVs automaticamente** após o scan, sem necessidade de flags especiais.

### **Método 3: Scanner Tático (Tactical RMM)**

```bash
# CSV para Tactical RMM
python tactical_network_scanner.py ***********/24 --csv

# Com arquivo personalizado
python tactical_network_scanner.py ***********/24 --csv --output "tactical_scan"
```

## 📊 Estrutura dos Dados

### **Organização por Cliente**
- **Cliente ID**: Baseado no terceiro octeto do IP
  - `192.168.1.x` = Cliente 001
  - `192.168.2.x` = Cliente 002
  - `192.168.10.x` = Cliente 010
- **Rede do Cliente**: Sub-rede /24 completa
- **Máquinas**: Todos os dispositivos encontrados na sub-rede

### **Informações por Máquina**
- **IP e Hostname**: Identificação da máquina
- **Sistema Operacional**: Windows, Linux, etc.
- **Tipo de Dispositivo**: Desktop, Server, Printer, Router, etc.
- **Método de Detecção**: Como o OS foi identificado
- **Confiança**: Percentual de certeza da identificação
- **Portas Abertas**: Serviços detectados

## 📁 Arquivos Gerados

### **Nomenclatura Automática**
```
network_scan_clientes_20250108_143015.csv      # CSV detalhado
resumo_clientes_20250108_143015.csv            # CSV resumo
```

### **Nomenclatura Personalizada**
```bash
# Comando:
python generate_client_csv.py -n ***********/24 -o "empresa_filial_sp"

# Arquivos gerados:
empresa_filial_sp_detalhado.csv                # CSV detalhado
empresa_filial_sp_resumo.csv                   # CSV resumo
```

## 🔧 Parâmetros Importantes

### **Rede (--network)**
```bash
-n ***********/24      # Uma sub-rede (1 cliente)
-n ***********/16      # Múltiplas sub-redes (256 clientes possíveis)
-n 10.0.0.0/8          # Rede corporativa grande
```

### **Threads (--threads)**
```bash
-t 50                  # Scan mais lento, menos carga na rede
-t 100                 # Padrão balanceado
-t 200                 # Scan mais rápido, mais carga na rede
```

### **Timeout (--timeout)**
```bash
--timeout 1            # Scan rápido, pode perder dispositivos lentos
--timeout 2            # Padrão balanceado
--timeout 5            # Scan completo, inclui dispositivos lentos
```

## 📈 Casos de Uso

### **1. Auditoria de TI por Cliente**
```bash
# Scanner principal (automático)
python network_scanner.py -n ***********/16 -o "auditoria_ti_2025"

# Ou script dedicado (mais controle)
python generate_client_csv.py -n ***********/16 -o "auditoria_ti_2025"
```

### **2. Inventário Rápido**
```bash
# Scanner principal (gera ambos automaticamente)
python network_scanner.py -n ***********/24 -o "inventario_rapido"

# Ou apenas resumo (script dedicado)
python generate_client_csv.py -n ***********/24 -f summary
```

### **3. Análise Detalhada de Segurança**
```bash
# Scanner principal com timeout maior
python network_scanner.py -n ***********/16 --timeout 5 -o "analise_seguranca"

# Ou script dedicado para controle específico
python generate_client_csv.py -n ***********/16 -f detailed --timeout 5
```

### **4. Monitoramento Tactical RMM**
```bash
# Para integração com Tactical RMM
python tactical_network_scanner.py ***********/24 --csv --json
```

## 💡 Dicas de Análise

### **Excel/LibreOffice**
1. Abra o CSV no Excel
2. Use filtros automáticos
3. Filtre por `Unidade_Cliente` para ver máquinas de um cliente específico
4. Ordene por `Sistema_Operacional` para agrupar por OS
5. Use tabelas dinâmicas para estatísticas

### **Análise de Dados**
- **CSV Detalhado**: Para análise técnica detalhada
- **CSV Resumo**: Para relatórios executivos
- **Combine ambos**: Para análise completa

### **Identificação de Problemas**
- Máquinas sem hostname: Possíveis dispositivos não gerenciados
- Baixa confiança de detecção: Dispositivos que precisam investigação
- Portas abertas incomuns: Possíveis riscos de segurança

## ⚠️ Considerações

### **Performance**
- Redes grandes (>1000 IPs) podem demorar vários minutos
- Use menos threads em redes congestionadas
- Aumente timeout para dispositivos lentos

### **Precisão**
- Alguns dispositivos podem não responder a ping
- Firewalls podem bloquear detecção de portas
- Hostnames podem não estar configurados

### **Privacidade**
- Os arquivos CSV contêm informações sensíveis da rede
- Mantenha os arquivos seguros
- Não compartilhe sem autorização

## 🆘 Solução de Problemas

### **Nenhum dispositivo encontrado**
```bash
# Teste com rede menor
python generate_client_csv.py -n ***********/24

# Aumente o timeout
python generate_client_csv.py -n ***********/24 --timeout 5
```

### **Scan muito lento**
```bash
# Reduza threads
python generate_client_csv.py -n ***********/24 -t 50

# Reduza timeout
python generate_client_csv.py -n ***********/24 --timeout 1
```

### **Erro de permissão**
```bash
# Execute como administrador (Windows)
# ou com sudo (Linux)
```

---

**Desenvolvido por Paulo Matheus**  
**Data: Janeiro 2025**
