
<p align="center">
  <a>
    <img width="250" height="250" src="readme/linkage_icon_rounded_4x.png" alt="App Icon">
  </a>

  ![Dependencies](https://img.shields.io/badge/dependencies-up%20to%20date-brightgreen.svg) [![License](https://img.shields.io/badge/license-MIT-blue.svg)](https://opensource.org/licenses/MIT)

</p>


## Overview
  A HR System that allow track attendacne, tasks and vacations of the employee of the company. The system consists of two main parts. First one is the mobile application for the employee, other one is a website for HR manager.


## Mobile Application Features

* Employee can check-in and check out Using GPS (GEOFENCING) through the mobile app.
* Employee can request vacations, see their status, and view their vacation history through the year.
* Employee can receive general tasks over their mobile app and mark them as done.
* Employee can receive general and specific notifications about their tasks and vacation requests status.
* Employee can receive feedback from <PERSON><PERSON> in real-time chat.

## Web Application Features

* HR can add new employees to the system and update their profiles.
* HR can view the attendance of all employees.
* <PERSON><PERSON> can view and respond to all vacation requests of the employee with acceptance or rejection with notes.
* <PERSON><PERSON> can view, assign tasks to the employee and update them.
* HR can send notifications to all employees and specific ones too.
* HR can configure some settings like work hours, holidays, and location of the company (Geofence).

## Technology Stack
Module                          |  Technology Stack
----------------------------------|------------------------------------------------------------------------------------
Server                         |  - NodeJS <br/>- Express  <br/>- MonogDB  <br/>- Firebase
Web Application                |  - ReactJS <br/>- Bootstrap (Customized AdminLTE)
Mobile Application             |  - React Native
## Mobile Application

<p>
  <img src="readme/Screenshoots/MOBILE/Screenshot_2020-12-12-18-25-04-85_b67a2b0e66a41c6712a67287069a2214.jpg" width="250">
  <img src="readme/Screenshoots/MOBILE/Screenshot_2020-12-12-18-25-01-01_b67a2b0e66a41c6712a67287069a2214.jpg" width="250">
  <img src="readme/Screenshoots/MOBILE/Screenshot_2020-12-12-20-37-29-32_b67a2b0e66a41c6712a67287069a2214.jpg" width="250">
</p>

<p>
  <img src="readme/Screenshoots/MOBILE/Screenshot_2020-12-12-20-57-41-35_b67a2b0e66a41c6712a67287069a2214.jpg" width="250">
  <img src="readme/Screenshoots/MOBILE/Screenshot_2020-12-12-20-49-56-69_b67a2b0e66a41c6712a67287069a2214.jpg" width="250">
  <img src="readme/Screenshoots/MOBILE/Screenshot_2020-12-12-21-02-05-66_b67a2b0e66a41c6712a67287069a2214.jpg" width="250">
</p>

<p>
  <img src="readme/Screenshoots/MOBILE/Screenshot_2020-12-12-21-01-11-42_b67a2b0e66a41c6712a67287069a2214.jpg" width="250">
  <img src="readme/Screenshoots/MOBILE/Screenshot_2020-12-12-20-44-09-95_b67a2b0e66a41c6712a67287069a2214.jpg" width="250">
  <img src="readme/Screenshoots/MOBILE/Screenshot_2020-12-12-20-39-52-82_b67a2b0e66a41c6712a67287069a2214.jpg" width="250">
</p>


## Web Application

<br>

#### Login Page
<img src="readme/Screenshoots/WEB/Screenshot_from_2020-12-12 21-12-48.png">

<br>

<br>

#### Users
<img src="readme/Screenshoots/WEB/Screenshot from 2020-12-12 20-13-28.png">

<br>

<br>

#### New User Form
<img src="readme/Screenshoots/WEB/Screenshot from 2020-12-12 20-13-39.png">

<br>

<br>

#### User Profile
<img src="readme/Screenshoots/WEB/Screenshot from 2020-12-12 20-40-07.png">

<br>

<br>

#### Attendence
<img src="readme/Screenshoots/WEB/Screenshot from 2020-12-12 20-15-03.png">
<img src="readme/Screenshoots/WEB/Screenshot from 2020-12-12 20-14-51.png">

<br>

<br>

#### Vacations Requests
<img src="readme/Screenshoots/WEB/Screenshot from 2020-12-12 20-15-58.png">

<br>

<br>

#### Vacation Respond Form
<img src="readme/Screenshoots/WEB/Screenshot from 2020-12-12 20-16-16.png">

<br>

<br>

#### Tasks
<img src="readme/Screenshoots/WEB/Screenshot from 2020-12-12 20-10-48.png">
<img src="readme/Screenshoots/WEB/Screenshot from 2020-12-12 20-11-00.png">
<br>


<br>

#### Notifications
<img src="readme/Screenshoots/WEB/Screenshot from 2020-12-12 20-14-14.png">
<img src="readme/Screenshoots/WEB/Screenshot from 2020-12-12 20-17-36.png">
<br>

<br>

#### Settings
<img src="readme/Screenshoots/WEB/Screenshot from 2020-12-12 20-14-25.png">
<img src="readme/Screenshoots/WEB/Screenshot from 2020-12-12 20-14-40.png">
<br>



## License

The app is MIT licensed.
