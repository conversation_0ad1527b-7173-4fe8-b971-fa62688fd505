@import '../../Styles/responsive';

.nt__wrapper {
    height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    overflow: hidden;

    img {
        position: absolute;
        width: 700px;
        opacity: 10%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        display: block;
        max-width: 100%;
    }

    .nt__info {
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        @include md{
            svg{
                transform: scale(2.5);
            }
        }

        h2 {
            font-size: 50px;
            font-weight: 700;
            margin-bottom: 20px;
            z-index: 4;
        }

        p {
            font-size: 20px;
            margin-bottom: 20px;
            text-align: center;
            line-height: 30px;
            z-index: 4;
        }

        a{
            z-index: 4;
        }
    }
}