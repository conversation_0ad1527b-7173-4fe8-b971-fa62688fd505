Unidade_Cliente,<PERSON>e_<PERSON><PERSON><PERSON>,<PERSON>_<PERSON><PERSON><PERSON>,<PERSON>_Ma<PERSON><PERSON>,Nome_Ma<PERSON>a,Sistema_Operacional,Tipo_Dispositivo,<PERSON><PERSON><PERSON>_Deteccao,<PERSON><PERSON>,<PERSON>fia<PERSON><PERSON>_<PERSON><PERSON>,<PERSON><PERSON>_<PERSON>,Data_Hora_Scan
Cliente_001,***********/24,8,***********,ROUTER-<PERSON><PERSON><PERSON><PERSON>,Network Device,TP-Link Archer C7 Router,TTL=255+HTTP,255,95,"80(HTTP); 443(HTTPS); 23(Telnet)",08/01/2025 14:30:10
Cliente_001,***********/24,8,***********0,PC-GERENCIA,Windows,Windows Desktop,TTL=128+Ports,128,90,"135(RPC); 445(SMB); 3389(RDP)",08/01/2025 14:30:15
Cliente_001,***********/24,8,***********5,NOTEBOOK-VENDAS,Windows,Windows Machine,TTL=128+Ports,128,85,"135(RPC); 445(SMB)",08/01/2025 14:30:18
Cliente_001,***********/24,8,************,PRINTER-HP-001,Linux,HP LaserJet P1102 Printer,TTL=64+Ports,64,95,"80(HTTP); 9100(Printer)",08/01/2025 14:30:22
Cliente_001,***********/24,8,************,IPHONE-PAULO,macOS/iOS,iPhone,TTL=60+Ports,60,90,"62078(iPhone-Sync); 5353(mDNS)",08/01/2025 14:30:25
Cliente_001,***********/24,8,************,ANDROID-SAMSUNG,Linux/Unix,Samsung Device,Hostname,64,75,"5353(mDNS); 1900(UPnP)",08/01/2025 14:30:28
Cliente_001,***********/24,8,************,SERVER-BACKUP,Linux,Ubuntu Server,TTL=64+Ports,64,85,"22(SSH); 80(HTTP); 443(HTTPS)",08/01/2025 14:30:32
Cliente_001,***********/24,8,************,NAS-SYNOLOGY,Linux,Synology NAS,Hostname,64,90,"80(HTTP); 443(HTTPS); 22(SSH)",08/01/2025 14:30:35
Cliente_002,***********/24,5,***********,FIREWALL-PFSENSE,Network Device,pfSense Firewall,TTL=64+HTTP,64,95,"80(HTTP); 443(HTTPS); 22(SSH)",08/01/2025 14:30:40
Cliente_002,***********/24,5,***********0,SERVER-WEB,Linux,Linux Server,TTL=64+Ports,64,85,"22(SSH); 80(HTTP); 443(HTTPS)",08/01/2025 14:30:45
Cliente_002,***********/24,5,***********5,SERVER-DB,Linux,Linux Server,TTL=64+Ports,64,85,"22(SSH); 3306(MySQL)",08/01/2025 14:30:48
Cliente_002,***********/24,5,************,WORKSTATION-001,Windows,Windows Server,TTL=128+Ports,128,90,"135(RPC); 445(SMB); 3389(RDP)",08/01/2025 14:30:52
Cliente_002,***********/24,5,************,PRINTER-CANON,Linux,Canon Printer,Hostname,64,80,"80(HTTP); 9100(Printer)",08/01/2025 14:30:55
Cliente_003,***********/24,3,***********,ROUTER-NETGEAR,Network Device,Netgear R7000 Nighthawk Router,TTL=255+HTTP,255,95,"80(HTTP); 443(HTTPS); 8080(HTTP-Alt)",08/01/2025 14:31:00
Cliente_003,***********/24,3,***********0,PC-RECEPCAO,Windows,Windows Machine,TTL=128+Ports,128,85,"135(RPC); 445(SMB)",08/01/2025 14:31:05
Cliente_003,***********/24,3,***********5,SMART-TV-LG,Linux/Unix,Smart Device/Media Player,TTL=64+Ports,64,70,"80(HTTP); 1900(UPnP)",08/01/2025 14:31:08
Cliente_010,************/24,12,************,ROUTER-CISCO,Network Device,Cisco Device,TTL=255+Ports,255,90,"80(HTTP); 443(HTTPS); 23(Telnet); 161(SNMP)",08/01/2025 14:31:15
Cliente_010,************/24,12,************,SWITCH-CISCO,Network Device,Cisco SG220 Switch,TTL=255+Ports,255,85,"80(HTTP); 161(SNMP); 162(SNMP)",08/01/2025 14:31:18
Cliente_010,************/24,12,*************,SERVER-DOMAIN,Windows,Windows Server,TTL=128+Ports,128,95,"135(RPC); 445(SMB); 3389(RDP); 53(DNS); 389(LDAP)",08/01/2025 14:31:22
Cliente_010,************/24,12,************5,SERVER-FILE,Windows,Windows Server,TTL=128+Ports,128,90,"135(RPC); 445(SMB); 3389(RDP)",08/01/2025 14:31:25
Cliente_010,************/24,12,192.168.10.20,SERVER-SQL,Windows,Windows Server,TTL=128+Ports,128,90,"135(RPC); 445(SMB); 3389(RDP); 1433(SQL)",08/01/2025 14:31:28
Cliente_010,************/24,12,192.168.10.25,WORKSTATION-001,Windows,Windows Machine,TTL=128+Ports,128,85,"135(RPC); 445(SMB); 3389(RDP)",08/01/2025 14:31:32
Cliente_010,************/24,12,192.168.10.30,WORKSTATION-002,Windows,Windows Machine,TTL=128+Ports,128,85,"135(RPC); 445(SMB); 3389(RDP)",08/01/2025 14:31:35
Cliente_010,************/24,12,192.168.10.35,WORKSTATION-003,Windows,Windows Machine,TTL=128+Ports,128,85,"135(RPC); 445(SMB)",08/01/2025 14:31:38
Cliente_010,************/24,12,*************,PRINTER-HP-LASER,Linux,HP LaserJet M404 Printer,TTL=64+Ports,64,95,"80(HTTP); 443(HTTPS); 9100(Printer); 161(SNMP)",08/01/2025 14:31:42
Cliente_010,************/24,12,*************,PRINTER-BROTHER,Linux,Brother MFC-T910DW Printer,TTL=64+Ports,64,90,"80(HTTP); 9100(Printer)",08/01/2025 14:31:45
Cliente_010,************/24,12,************0,ACCESS-POINT-UNIFI,Network Device,UniFi Access Point,Hostname,64,85,"80(HTTP); 443(HTTPS); 22(SSH)",08/01/2025 14:31:48
Cliente_010,************/24,12,************5,NAS-QNAP,Linux,QNAP NAS,Hostname,64,90,"80(HTTP); 443(HTTPS); 22(SSH); 21(FTP)",08/01/2025 14:31:52
Cliente_050,************/24,6,************,ROUTER-ASUS,Network Device,ASUS RT-AC68U Router,TTL=255+HTTP,255,95,"80(HTTP); 443(HTTPS); 8080(HTTP-Alt)",08/01/2025 14:32:00
Cliente_050,************/24,6,************0,NOTEBOOK-ADMIN,Windows,Windows Machine,TTL=128+Ports,128,85,"135(RPC); 445(SMB); 3389(RDP)",08/01/2025 14:32:05
Cliente_050,************/24,6,************5,DESKTOP-DESIGN,Windows,Windows Machine,TTL=128+Ports,128,85,"135(RPC); 445(SMB)",08/01/2025 14:32:08
Cliente_050,************/24,6,*************,IPAD-VENDAS,macOS/iOS,iPad,TTL=60+Ports,60,90,"62078(iPhone-Sync); 5353(mDNS)",08/01/2025 14:32:12
Cliente_050,************/24,6,*************,ANDROID-TABLET,Linux/Unix,Android Device,Hostname,64,75,"5353(mDNS); 1900(UPnP)",08/01/2025 14:32:15
Cliente_050,************/24,6,*************,CHROMECAST-SALA,Linux/Unix,Google Chromecast,Hostname,64,80,"8008(HTTP); 8009(HTTPS)",08/01/2025 14:32:18
