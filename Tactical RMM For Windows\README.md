# Scripts de Instalação TacticalRMM - NVirtual

Este diretório contém scripts automatizados para instalação do agente TacticalRMM.

## Arquivos Disponíveis

### 1. `instalar_tactical_rmm.bat`
- **Tipo**: <PERSON><PERSON><PERSON>ch (Windows)
- **Uso**: Duplo clique para executar
- **Compatibilidade**: Todas as versões do Windows

### 2. `instalar_tactical_rmm.ps1`
- **Tipo**: Script PowerShell
- **Uso**: Clique direito → "Executar com PowerShell" ou duplo clique (se configurado)
- **Compatibilidade**: Windows com PowerShell (recomendado para Windows 10/11)

## Pré-requisitos

1. **Conexão com Internet**: O script PowerShell baixa automaticamente o agente. O script Batch requer o arquivo local.

2. **Permissões**: Execute os scripts como Administrador para garantir a instalação correta.

3. **Arquivo do Agente (apenas para Batch)**: Para o script `.bat`, certifique-se de que o arquivo `tacticalagent-v2.9.1-windows-amd64.exe` esteja na mesma pasta.

## Como Usar

### Método 1 - Script PowerShell (Recomendado - Download Automático)
1. Clique direito em `instalar_tactical_rmm.ps1` → "Executar com PowerShell"
   - Ou duplo clique se o PowerShell estiver configurado como padrão
2. Digite o ID do Cliente (apenas números)
3. Digite o ID do Site (apenas números)
4. Confirme a instalação digitando "S"
5. **O script baixa automaticamente o agente da internet**

### Método 2 - Script Batch (Compatibilidade - Arquivo Local)
1. Coloque o arquivo `tacticalagent-v2.9.1-windows-amd64.exe` na pasta
2. Duplo clique em `instalar_tactical_rmm.bat`
3. Digite o ID do Cliente quando solicitado
4. Digite o ID do Site quando solicitado
5. Confirme a instalação digitando "S"

## Configurações Automáticas

Os scripts automaticamente configuram:
- **Tipo de Agente**: Workstation (fixo)
- **API**: https://api.centralmesh.nvirtual.com.br
- **Token de Autenticação**: Pré-configurado
- **Ping**: Habilitado
- **Modo Silencioso**: Ativado

## Validações Incluídas

- ✅ **PowerShell**: Download automático do agente
- ✅ **Batch**: Verificação se o arquivo do agente existe
- ✅ Validação de entrada (IDs não podem estar vazios)
- ✅ Validação numérica (PowerShell)
- ✅ Verificação de conectividade com servidor
- ✅ Detecção se TacticalRMM já está instalado
- ✅ Configuração automática de exclusões no Windows Defender
- ✅ Confirmação antes da instalação
- ✅ Verificação do resultado da instalação
- ✅ Mensagens de erro detalhadas

## Solução de Problemas

### Erro: "Arquivo não encontrado"
- Certifique-se de que `tacticalagent-v2.9.1-windows-amd64.exe` está na mesma pasta

### Erro: "Acesso negado"
- Execute o script como Administrador

### PowerShell não executa
- Abra PowerShell como Administrador e execute:
  ```powershell
  Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
  ```

### Script Batch não abre
- Clique direito → "Executar como administrador"

## Informações Técnicas

- **Tempo de espera**: 5 segundos entre instalação e configuração
- **Modo de instalação**: Silencioso (sem interação adicional)
- **Logs**: Verificar logs do TacticalRMM em caso de erro
- **Localização do agente**: `C:\Program Files\TacticalAgent\`

## Suporte

Para suporte técnico, entre em contato com a equipe da NVirtual.
