# Teste rápido do script corrigido
Write-Host "=== TESTE DO SCRIPT CORRIGIDO ==="
Write-Host ""

# Testar modo WhatIf (simulação)
Write-Host "1. Testando modo WhatIf (simulação):"
Write-Host "   Comando: .\Set-TacticalSite-Cliente copy.ps1 -WhatIf"
Write-Host ""

# Testar Get-Help
Write-Host "2. Testando documentação integrada:"
Write-Host "   Comando: Get-Help .\Set-TacticalSite-Cliente copy.ps1"
Write-Host ""

# Mostrar parâmetros disponíveis
Write-Host "3. <PERSON>r<PERSON><PERSON><PERSON> disponíveis:"
Write-Host "   -ApiToken    : Token de API específico"
Write-Host "   -Verbose     : <PERSON><PERSON><PERSON> de<PERSON><PERSON><PERSON>"
Write-Host "   -WhatIf      : Simulação sem alterações"
Write-Host ""

Write-Host "4. Exemplos de uso:"
Write-Host "   # Execução normal"
Write-Host "   .\Set-TacticalSite-Cliente copy.ps1"
Write-Host ""
Write-Host "   # Com saída detalhada"
Write-Host "   .\Set-TacticalSite-Cliente copy.ps1 -Verbose"
Write-Host ""
Write-Host "   # Modo simulação"
Write-Host "   .\Set-TacticalSite-Cliente copy.ps1 -WhatIf"
Write-Host ""
Write-Host "   # Com token específico"
Write-Host "   .\Set-TacticalSite-Cliente copy.ps1 -ApiToken 'SEU_TOKEN' -Verbose"
Write-Host ""

Write-Host "=== SCRIPT PRONTO PARA USO ==="
