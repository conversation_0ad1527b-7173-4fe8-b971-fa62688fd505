# Script de Instalação do Zabbix Agent para Linux

## 🐧 Script Único para Tactical RMM

Este script automatiza a instalação do Zabbix Agent em sistemas Linux através do Tactical RMM.

### 📁 Arquivo Principal
**`Install-ZabbixAgent-Linux-Simple.sh`**

## 🚀 Como usar no Tactical RMM

### **Exemplos de uso:**

#### **Instalação básica:**
```bash
--server *************
```

#### **Com hostname personalizado:**
```bash
--server ************* --hostname SERVIDOR-LINUX-01
```

#### **Com correção automática de problemas:**
```bash
--server ************* --hostname SERVIDOR-LINUX-01 --force
```

#### **Diagnóstico apenas:**
```bash
--server ************* --diagnose-only
```

#### **Instalação offline:**
```bash
--server ************* --local-tar /tmp/zabbix_agent.tar.gz
```

## 📋 Parâmetros Disponíveis

| Parâmetro | Obrigatório | Descrição | Exemplo |
|-----------|-------------|-----------|---------|
| `--server` | ✅ Sim | IP do servidor Zabbix | `*************` |
| `--hostname` | ❌ Não | Nome do host | `SERVIDOR-01` |
| `--version` | ❌ Não | Versão do Zabbix | `7.0.10` |
| `--install-path` | ❌ Não | Caminho de instalação | `/opt/zabbix` |
| `--config-path` | ❌ Não | Caminho de configuração | `/etc/zabbix` |
| `--force` | ❌ Não | Força reinstalação | `--force` |
| `--diagnose-only` | ❌ Não | Apenas diagnóstico | `--diagnose-only` |
| `--local-tar` | ❌ Não | Arquivo tar.gz local | `/tmp/zabbix.tar.gz` |

## ✨ Funcionalidades

### **1. Suporte Multi-Distribuição**
- ✅ **Ubuntu/Debian** (apt-get)
- ✅ **CentOS/RHEL/Rocky/AlmaLinux** (yum/dnf)
- ✅ **Fedora** (dnf)
- ✅ **Detecção automática** da distribuição

### **2. Suporte Multi-Arquitetura**
- ✅ **AMD64/x86_64** (Intel/AMD 64-bit)
- ✅ **ARM64/aarch64** (ARM 64-bit)
- ✅ **Detecção automática** da arquitetura

### **3. Diagnóstico Completo**
- ✅ Verifica privilégios de root
- ✅ Detecta serviços Zabbix existentes
- ✅ Identifica processos Zabbix rodando
- ✅ Verifica diretórios residuais
- ✅ Valida espaço em disco

### **4. Correção Automática**
- ✅ Para e remove serviços conflitantes
- ✅ Termina processos Zabbix em execução
- ✅ Remove diretórios residuais
- ✅ Limpa instalações anteriores

### **5. Instalação Robusta**
- ✅ Download automático com fallback
- ✅ Instalação a partir de binários estáticos
- ✅ Criação automática de usuário `zabbix`
- ✅ Configuração de permissões adequadas

### **6. Configuração Completa**
- ✅ Arquivo de configuração otimizado
- ✅ Serviço systemd configurado
- ✅ Firewall configurado automaticamente
- ✅ Inicialização automática habilitada

## 🔧 O que o script faz

### **1. Verificações iniciais**
- Verifica se está executando como root
- Detecta arquitetura do sistema (AMD64/ARM64)
- Identifica distribuição Linux
- Instala dependências necessárias

### **2. Diagnóstico do sistema**
- Verifica instalações Zabbix existentes
- Identifica conflitos de serviços/processos
- Valida espaço em disco disponível
- Corrige problemas automaticamente (com `--force`)

### **3. Download e instalação**
- Baixa binários estáticos do Zabbix Agent v7.0.10
- Extrai e instala em `/opt/zabbix`
- Cria usuário `zabbix` se não existir
- Define permissões adequadas

### **4. Configuração**
- Cria arquivo `/etc/zabbix/zabbix_agentd.conf`
- Configura servidor e hostname
- Cria serviço systemd
- Habilita inicialização automática

### **5. Firewall e conectividade**
- Configura regras de firewall (UFW/firewalld/iptables)
- Abre porta 10050/tcp
- Testa conectividade com servidor Zabbix

## 📁 Arquivos criados

- **Binário**: `/opt/zabbix/zabbix_agentd`
- **Configuração**: `/etc/zabbix/zabbix_agentd.conf`
- **Logs**: `/var/log/zabbix/zabbix_agentd.log`
- **PID**: `/var/run/zabbix/zabbix_agentd.pid`
- **Serviço**: `/etc/systemd/system/zabbix-agent.service`

## 🎮 Modos de Operação

### **1. Diagnóstico Apenas**
```bash
--server ************* --diagnose-only
```
- Verifica problemas sem fazer alterações
- Retorna código 0 se sistema estiver limpo
- Retorna código 1 se houver problemas

### **2. Instalação Normal**
```bash
--server ************* --hostname SERVIDOR-01
```
- Executa diagnóstico básico
- Instala se não houver problemas críticos
- Exibe avisos sobre problemas encontrados

### **3. Instalação com Correção**
```bash
--server ************* --hostname SERVIDOR-01 --force
```
- Executa diagnóstico completo
- **Corrige automaticamente** todos os problemas
- Instala após limpeza completa

### **4. Instalação Offline**
```bash
--server ************* --local-tar /tmp/zabbix_agent.tar.gz
```
- Usa arquivo tar.gz baixado manualmente
- Ideal para ambientes sem internet

## 🔍 Verificação da instalação

### **Comandos úteis:**
```bash
# Verificar status do serviço
systemctl status zabbix-agent

# Ver logs em tempo real
tail -f /var/log/zabbix/zabbix_agentd.log

# Verificar configuração
cat /etc/zabbix/zabbix_agentd.conf

# Testar conectividade
nc -z SERVIDOR_ZABBIX 10051

# Reiniciar serviço
systemctl restart zabbix-agent

# Verificar porta
netstat -tlnp | grep :10050
```

## 🚨 Troubleshooting

### **Problemas comuns:**

#### **1. Erro de permissão**
- **Causa**: Script não executado como root
- **Solução**: Execute com `sudo` ou como root

#### **2. Serviço não inicia**
- **Causa**: Configuração incorreta ou conflitos
- **Solução**: Use `--force` para limpeza completa

#### **3. Não conecta ao servidor**
- **Causa**: Firewall ou rede
- **Solução**: Verificar conectividade e firewall

### **Comandos de diagnóstico:**
```bash
# Verificar logs do sistema
journalctl -u zabbix-agent -f

# Testar configuração
/opt/zabbix/zabbix_agentd -t -c /etc/zabbix/zabbix_agentd.conf

# Verificar processos
ps aux | grep zabbix

# Verificar portas
ss -tlnp | grep :10050
```

## 🌐 Distribuições Testadas

- ✅ **Ubuntu** 18.04, 20.04, 22.04, 24.04
- ✅ **Debian** 10, 11, 12
- ✅ **CentOS** 7, 8
- ✅ **RHEL** 8, 9
- ✅ **Rocky Linux** 8, 9
- ✅ **AlmaLinux** 8, 9
- ✅ **Fedora** 35+

## 📦 URLs de Download

### **AMD64 (x86_64):**
```
https://cdn.zabbix.com/zabbix/binaries/stable/7.0/7.0.10/zabbix_agent-7.0.10-linux-3.0-amd64-static.tar.gz
```

### **ARM64 (aarch64):**
```
https://cdn.zabbix.com/zabbix/binaries/stable/7.0/7.0.10/zabbix_agent-7.0.10-linux-3.0-arm64-static.tar.gz
```

## 🎯 Exemplo para Tactical RMM

### **Comando recomendado:**
```bash
--server ************* --hostname $(hostname)-zabbix --force
```

### **Para ambientes específicos:**
```bash
# Servidor de produção
--server ********* --hostname PROD-WEB-01 --force

# Servidor de desenvolvimento
--server ************* --hostname DEV-APP-01

# Diagnóstico apenas
--server ************* --diagnose-only
```

---

**💡 Este script instala o Zabbix Agent de forma completamente automatizada em qualquer distribuição Linux suportada!**
