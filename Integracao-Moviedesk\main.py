from services import movidesk
from handlers import saas_antivirus
from utils.logger import logger

def main():
    logger.info("🔍 Buscando tickets dos últimos 3 meses...")
    # 🚀 Busca otimizada: 90 dias (3 meses) de histórico
    tickets = movidesk.buscar_tickets_por_titulo("[SAAS]", dias_historico=90)

    if not tickets:
        logger.warning("⚠️ Nenhum ticket encontrado com esse título.")
        return

    logger.info(f"📋 {len(tickets)} ticket(s) encontrados.")

    for ticket in tickets:
        # 🔒 Verifica se o ticket está aberto (status == 1)
        if ticket.get("status") != 1:
            logger.info(f"🚫 Ignorando ticket #{ticket['id']} (status fechado: {ticket.get('status')})")
            continue

        logger.info(f"➡️ Processando ticket #{ticket['id']}...")
        saas_antivirus.processar_ticket(ticket)

if __name__ == "__main__":
    main()
