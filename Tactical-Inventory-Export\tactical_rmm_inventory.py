#!/usr/bin/env python3
"""
Tactical RMM Inventory Script - Para execução via Tactical RMM
==============================================================

Script simplificado para ser executado diretamente via Tactical RMM.
Gera relatório de inventário básico em CSV e JSON.

Autor: NVirtual
Data: 2025-01-02
Versão: 1.0
"""

import requests
import json
import csv
import os
from datetime import datetime

# Configurações fixas
API_URL = "https://api.centralmesh.nvirtual.com.br"
API_TOKEN = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"
OUTPUT_DIR = "C:\\TacticalInventory"

def log(message):
    """Log simples com timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def create_output_dir():
    """Cria diretório de saída se não existir"""
    try:
        if not os.path.exists(OUTPUT_DIR):
            os.makedirs(OUTPUT_DIR)
            log(f"Diretório criado: {OUTPUT_DIR}")
        return True
    except Exception as e:
        log(f"Erro ao criar diretório: {e}")
        return False

def get_api_data(endpoint):
    """Busca dados da API"""
    headers = {"X-API-KEY": API_TOKEN}
    
    try:
        url = f"{API_URL}/{endpoint}/"
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        log(f"Erro ao buscar {endpoint}: {e}")
        return []

def calculate_status(last_seen):
    """Calcula status do agente"""
    if not last_seen:
        return "Desconhecido"
    
    try:
        last_dt = datetime.fromisoformat(last_seen.replace('Z', '+00:00'))
        now = datetime.now(last_dt.tzinfo)
        diff = (now - last_dt).total_seconds()
        
        if diff < 300:  # 5 minutos
            return "Online"
        elif diff < 86400:  # 24 horas
            return "Recente"
        else:
            return "Offline"
    except:
        return "Desconhecido"

def format_bytes_to_gb(bytes_val):
    """Converte bytes para GB"""
    try:
        return round(int(bytes_val) / (1024**3), 2)
    except:
        return 0

def format_datetime(dt_str):
    """Formata datetime para string legível"""
    if not dt_str:
        return "N/A"
    try:
        dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except:
        return dt_str

def process_agents(agents, clients, sites):
    """Processa dados dos agentes"""
    inventory = []

    for agent in agents:
        try:
            data = {
                'ID_Agente': agent.get('agent_id', ''),
                'Hostname': agent.get('hostname', ''),
                'Cliente': agent.get('client_name', 'N/A'),
                'Site': agent.get('site_name', 'N/A'),
                'Status': calculate_status(agent.get('last_seen')),
                'Sistema_Operacional': agent.get('operating_system', ''),
                'Versao_OS': agent.get('plat', ''),
                'Arquitetura': agent.get('arch', ''),
                'IP_Publico': agent.get('public_ip', ''),
                'Versao_Agente': agent.get('version', ''),
                'Ultimo_Contato': format_datetime(agent.get('last_seen')),
                'Tempo_Boot': format_datetime(agent.get('boot_time')),
                'CPU_Modelo': agent.get('cpu_model', ''),
                'RAM_Total_GB': format_bytes_to_gb(agent.get('total_ram')),
                'RAM_Usado_GB': format_bytes_to_gb(agent.get('used_ram')),
                'Antivirus': agent.get('antivirus', ''),
                'Dominio': agent.get('domain', ''),
                'Usuario_Logado': agent.get('logged_in_username', ''),
                'Servicos_Falhas': agent.get('services_failing', 0),
                'Checks_Falhas': agent.get('checks_failing', 0),
                'Manutencao': 'Sim' if agent.get('maintenance_mode') else 'Não',
                'Tipo_Monitoramento': agent.get('monitoring_type', ''),
                'Data_Instalacao': format_datetime(agent.get('install_time')),
                'Descricao': agent.get('description', '')
            }
            inventory.append(data)
        except Exception as e:
            log(f"Erro ao processar agente {agent.get('hostname', 'N/A')}: {e}")
    
    return inventory

def export_csv(data, filename):
    """Exporta para CSV"""
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            if data:
                writer = csv.DictWriter(f, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
        return True
    except Exception as e:
        log(f"Erro ao exportar CSV: {e}")
        return False

def export_json(data, filename):
    """Exporta para JSON"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        log(f"Erro ao exportar JSON: {e}")
        return False

def generate_summary(inventory):
    """Gera resumo estatístico"""
    total = len(inventory)
    online = sum(1 for item in inventory if item['Status'] == 'Online')
    offline = sum(1 for item in inventory if item['Status'] == 'Offline')
    recent = sum(1 for item in inventory if item['Status'] == 'Recente')
    
    # Contar por cliente
    clients = {}
    for item in inventory:
        client = item['Cliente']
        clients[client] = clients.get(client, 0) + 1
    
    summary = {
        'timestamp': datetime.now().isoformat(),
        'total_agentes': total,
        'status': {
            'online': online,
            'offline': offline,
            'recente': recent
        },
        'por_cliente': clients
    }
    
    return summary

def main():
    """Função principal"""
    log("=== INICIANDO EXPORTAÇÃO DE INVENTÁRIO TACTICAL RMM ===")
    
    try:
        # Criar diretório de saída
        if not create_output_dir():
            return 1
        
        # Buscar dados da API
        log("Buscando dados da API...")
        agents = get_api_data("agents")
        clients = get_api_data("clients")
        sites = get_api_data("clients/sites")
        
        if not agents:
            log("ERRO: Nenhum agente encontrado")
            return 1
        
        log(f"Encontrados: {len(agents)} agentes, {len(clients)} clientes, {len(sites)} sites")
        
        # Processar dados
        log("Processando dados dos agentes...")
        inventory = process_agents(agents, clients, sites)
        
        if not inventory:
            log("ERRO: Nenhum dado processado")
            return 1
        
        # Gerar nomes de arquivo
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = f"TacticalRMM_Inventario_{timestamp}"
        
        csv_file = os.path.join(OUTPUT_DIR, f"{base_name}.csv")
        json_file = os.path.join(OUTPUT_DIR, f"{base_name}.json")
        summary_file = os.path.join(OUTPUT_DIR, f"{base_name}_resumo.json")
        
        # Exportar dados
        log("Exportando dados...")
        
        success = True
        if export_csv(inventory, csv_file):
            log(f"CSV exportado: {csv_file}")
        else:
            success = False
        
        if export_json(inventory, json_file):
            log(f"JSON exportado: {json_file}")
        else:
            success = False
        
        # Gerar e exportar resumo
        summary = generate_summary(inventory)
        if export_json(summary, summary_file):
            log(f"Resumo exportado: {summary_file}")
        
        # Mostrar estatísticas
        log("=== ESTATÍSTICAS ===")
        log(f"Total de agentes: {summary['total_agentes']}")
        log(f"Online: {summary['status']['online']}")
        log(f"Recente: {summary['status']['recente']}")
        log(f"Offline: {summary['status']['offline']}")
        
        log("=== DISTRIBUIÇÃO POR CLIENTE ===")
        for client, count in summary['por_cliente'].items():
            log(f"{client}: {count} agentes")
        
        if success:
            log("=== EXPORTAÇÃO CONCLUÍDA COM SUCESSO ===")
            return 0
        else:
            log("=== EXPORTAÇÃO CONCLUÍDA COM ERROS ===")
            return 1
            
    except Exception as e:
        log(f"ERRO GERAL: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
