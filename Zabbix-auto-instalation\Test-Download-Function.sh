#!/bin/bash
# Script para testar a função de download isoladamente
# Autor: <PERSON> - NVirtual

# URLs de download
DOWNLOAD_URL_AMD64="https://repo.zabbix.com/zabbix/7.0/ubuntu/pool/main/z/zabbix/zabbix-agent_7.0.10-1+ubuntu22.04_amd64.deb"

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "SUCCESS")
            echo -e "[$timestamp] [${GREEN}SUCCESS${NC}] $message"
            ;;
        "WARNING")
            echo -e "[$timestamp] [${YELLOW}WARNING${NC}] $message"
            ;;
        "ERROR")
            echo -e "[$timestamp] [${RED}ERROR${NC}] $message"
            ;;
        *)
            echo -e "[$timestamp] [INFO] $message"
            ;;
    esac
}

# Funcao de download (copiada do script principal)
download_zabbix_agent() {
    local architecture="$1"
    local download_url=""
    
    case "$architecture" in
        amd64) download_url="$DOWNLOAD_URL_AMD64" ;;
        *) 
            echo "ERROR: Arquitetura nao suportada" >&2
            exit 1 
            ;;
    esac
    
    local filename=$(basename "$download_url")
    local download_path="/tmp/$filename"
    
    # Remover arquivo anterior
    rm -f "$download_path"
    
    # Download com wget (silencioso)
    if command -v wget > /dev/null 2>&1; then
        if wget -q "$download_url" -O "$download_path" 2>/dev/null; then
            if [ -f "$download_path" ] && [ -s "$download_path" ]; then
                echo "$download_path"
                return 0
            fi
        fi
    fi
    
    # Fallback para curl (silencioso)
    if command -v curl > /dev/null 2>&1; then
        if curl -sL "$download_url" -o "$download_path" 2>/dev/null; then
            if [ -f "$download_path" ] && [ -s "$download_path" ]; then
                echo "$download_path"
                return 0
            fi
        fi
    fi
    
    echo "ERROR: Falha no download" >&2
    exit 1
}

main() {
    log_message "SUCCESS" "=== TESTE DA FUNCAO DE DOWNLOAD ==="
    
    # Testar download
    log_message "INFO" "Testando download para arquitetura amd64..."
    
    local result=$(download_zabbix_agent "amd64")
    local exit_code=$?
    
    log_message "INFO" "Resultado da funcao: '$result'"
    log_message "INFO" "Codigo de saida: $exit_code"
    
    # Verificar se resultado é apenas um caminho
    if [[ "$result" == /tmp/* ]] && [[ ! "$result" == *$'\n'* ]]; then
        log_message "SUCCESS" "Funcao retornou apenas o caminho do arquivo"
        
        if [ -f "$result" ]; then
            local file_size=$(stat -c%s "$result" 2>/dev/null || stat -f%z "$result" 2>/dev/null)
            local file_size_kb=$((file_size / 1024))
            log_message "SUCCESS" "Arquivo existe - Tamanho: ${file_size_kb}KB"
            log_message "INFO" "Caminho: $result"
            
            # Verificar tipo do arquivo
            log_message "INFO" "Tipo do arquivo: $(file "$result")"
        else
            log_message "ERROR" "Arquivo nao existe: $result"
        fi
    else
        log_message "ERROR" "Funcao retornou dados incorretos (logs misturados)"
        log_message "ERROR" "Conteudo: $result"
    fi
    
    log_message "SUCCESS" "=== TESTE CONCLUIDO ==="
}

main
