
# 🗜️ Conversor Automático de Vídeos com FFmpeg

Este script PowerShell realiza a compressão automática de vídeos em um ambiente Windows, utilizando o `ffmpeg` com o codec `libx265` para reduzir o tamanho dos arquivos. Ele preserva a estrutura de diretórios e ignora vídeos já processados, ideal para uso contínuo em servidores ou pastas monitoradas.

---

## ✨ Funcionalidades

- Interface gráfica para seleção das pastas de origem e destino
- Compressão eficiente com `libx265` e bitrate de 280k
- Redução de resolução para 1280x720
- Remoção de metadados e áudio (se desejado)
- Evita retrabalho: só converte vídeos ainda não processados
- Estrutura de pastas mantida no destino
- Log detalhado de conversões e arquivos ignorados
- Configuração persistente entre execuções

---

## 📦 Pré-requisitos

- Windows com PowerShell 5.0 ou superior
- Atualizado. Mas caso não queira baixar o FFmpeg, este git ja vem com uma versão embarcada e operacional.
- Este repositório DEVE estar localizado em: 'C:\Arquivos de Programas\'

```
.infmpeg.exe
```

---

## 📂 Estrutura esperada

```
Compressao de Videos/
├── compressar-videos.ps1                  # Script principal
├── compressar-videos.config               # Gerado automaticamente na primeira execução
├── Agendador - Comprimir Arquivos de Video NCloud.xml # Agendador de Tarefas do Windows
└── bin/
    └── ffmpeg.exe                # Executável do FFmpeg
```

---

## ▶️ Como usar

1. **Clone o repositório** ou copie os arquivos para uma pasta local.
2. **Coloque o repositório na pasta 'C:\Arquivos de Programas** e coloque o `ffmpeg.exe` na subpasta `bin` caso não o tenha.
3. **Abra o arquivo `compressar-videos.config` e edite as pastas de origem e destino.**
4. Execute `compressar-videos.ps1` com o botão direito > **Executar com PowerShell** ou importe o xml `Agendador - Comprimir Arquivos de Video NCloud.xml` no Agendador de Tarefas do Windows.
5. Os vídeos serão convertidos automaticamente e salvos no diretório de destino.

---

## 🔁 Execução Automática (Opcional)

Você pode configurar a execução automática a cada 1 hora via Agendador de Tarefas do Windows:

1. Abra o **Agendador de Tarefas**.
2. Clique em Importar e selecione o arquivo `Agendador - Comprimir Arquivos de Video NCloud.xml`.
3. Edite a tarefa para apontar para o caminho correto do script `compressar-videos.ps1`.
4. Mude o `<UserId>` para um usuário administrador.
5. Clique em Salvar.
6. Digite a senha do usuário administrador.

- **Ação**:
  - Programa/script: `powershell`
  - Argumentos:
    ```
    -ExecutionPolicy Bypass -File "C:\Arquivos de Programas\Compressao de Videos\compressar-videos.ps1"
    ```

- **Gatilho**:
  - Iniciar a tarefa a cada 1 hora.

---

## ⚙️ Configuração aplicada no FFmpeg

- 🎞️ Codec de vídeo: `libx265` (compressão HEVC)
- 🎚️ Bitrate: `280k` 
- 🔄 Resolução: `1280x720`
- 🔇 Áudio: removido (`-an`) — opcional
- 🧹 Metadados: removidos (`-map_metadata -1`)
- 🐌 Preset: `slow` (alta compressão com desempenho aceitável)

> ❗ Caso deseje **manter o áudio**, edite o script e remova o parâmetro `-an`.

---

## 📊 Exemplo de execução

```
[1/6] Convertendo: 1. Bem Vindo!.mp4
  Origem: C:\VideosOriginais\1. Bem Vindo!.mp4
  Destino: C:\VideosConvertidos\1. Bem Vindo!-inmetro.mp4
  Conversao concluida com sucesso!

[2/6] Ja convertido: video-antigo.mp4

Resumo da operacao do conversor Nubium:
  Total de arquivos: 6
  Arquivos convertidos: 4
  Arquivos ja existentes (pulados): 2
```

---

## 📌 Observações

- A estrutura de pastas da origem será mantida na pasta de destino.
- Vídeos com extensões suportadas: `.mp4`, `.avi`, `.mov`, `.wmv`, `.mkv`, `.dav`
- A recompactação altera os metadados e a hash do arquivo, o que pode impactar em processos de auditoria ou análise forense.
- Se após a conversão você apagar o arquivo original, o script não o reconhecerá como já convertido e fará a conversão novamente.

---

## 👨‍💻 Autor

Script desenvolvido por **Paulo matheus da Nubium Tecnologia** para otimizar o armazenamento e padronização de vídeos em conformidade com requisitos técnicos de compactação.
