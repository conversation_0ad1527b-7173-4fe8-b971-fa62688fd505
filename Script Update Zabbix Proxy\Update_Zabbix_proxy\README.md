# Script de Atualização Zabbix Proxy para 7.0 LTS com SQLite3

Este script automatiza a atualização do Zabbix Proxy para a versão 7.0 LTS, mantendo a configuração e dados do banco SQLite3.

## 🎯 Funcionalidades

- ✅ **Backup completo** da configuração e banco SQLite3
- ✅ **Detecção automática** da distribuição Linux (Ubuntu/Debian/RHEL/CentOS)
- ✅ **Instalação do Zabbix 7.0 LTS** com SQLite3
- ✅ **Restauração automática** da configuração e dados
- ✅ **Verificação de integridade** do banco SQLite3
- ✅ **Logs detalhados** com cores e timestamps
- ✅ **Rollback automático** em caso de erro

## 📋 Pré-requisitos

- Sistema Linux (Ubuntu/Debian/RHEL/CentOS/Rocky/AlmaLinux)
- Zabbix Proxy já instalado com SQLite3
- Acesso root (sudo)
- Conexão com internet

## 🚀 Como usar

### 1. Download e execução
```bash
# Tornar executável
chmod +x update_zabbix_proxy_to_7.sh

# Executar como root
sudo ./update_zabbix_proxy_to_7.sh
```

### 2. O que o script faz automaticamente

1. **Detecção do sistema** - Identifica a distribuição Linux
2. **Verificação da versão atual** - Mostra versão instalada
3. **Backup completo:**
   - Arquivo de configuração (`/etc/zabbix/zabbix_proxy.conf`)
   - Banco de dados SQLite3 (`/var/lib/zabbix/zabbix_proxy.db`)
   - Dump SQL do banco para migração
   - Logs recentes
   - Permissões dos arquivos

4. **Parada dos serviços** - Para o Zabbix Proxy com segurança
5. **Instalação do Zabbix 7.0 LTS** - Adiciona repositório e instala
6. **Restauração:**
   - Configuração original
   - Banco de dados SQLite3
   - Permissões corretas

7. **Verificações:**
   - Integridade do banco SQLite3
   - Sintaxe da configuração
   - Funcionamento do serviço

8. **Inicialização** - Inicia e habilita o serviço

## 📁 Estrutura do Backup

O backup é salvo em `/tmp/zabbix_backup_YYYYMMDD_HHMMSS/`:

```
zabbix_backup_20250701_120000/
├── zabbix_proxy.conf.backup      # Configuração original
├── zabbix_proxy.db.backup        # Banco SQLite3 original
├── zabbix_proxy_dump.sql         # Dump SQL do banco
├── zabbix_proxy.log.backup       # Logs recentes
├── etc_zabbix_backup/             # Backup completo do /etc/zabbix
├── db_permissions.txt             # Permissões do diretório do banco
├── zabbix_proxy.conf.new          # Configuração nova (para rollback)
└── zabbix_proxy.db.new            # Banco novo (para rollback)
```

## 🔧 Comandos Úteis Pós-Atualização

### Verificar status
```bash
# Status do serviço
sudo systemctl status zabbix-proxy

# Logs em tempo real
sudo tail -f /var/log/zabbix/zabbix_proxy.log

# Versão instalada
zabbix_proxy --version
```

### Verificar banco SQLite3
```bash
# Integridade do banco
sqlite3 /var/lib/zabbix/zabbix_proxy.db "PRAGMA integrity_check;"

# Listar tabelas
sqlite3 /var/lib/zabbix/zabbix_proxy.db ".tables"

# Tamanho do banco
du -h /var/lib/zabbix/zabbix_proxy.db

# Otimizar banco (se necessário)
sqlite3 /var/lib/zabbix/zabbix_proxy.db "VACUUM;"
```

## 🚨 Solução de Problemas

### Se o serviço não iniciar:
```bash
# Verificar logs
sudo journalctl -u zabbix-proxy -f

# Testar configuração
sudo zabbix_proxy -c /etc/zabbix/zabbix_proxy.conf -t

# Verificar permissões
ls -la /var/lib/zabbix/zabbix_proxy.db
```

### Restaurar backup manualmente:
```bash
# Parar serviço
sudo systemctl stop zabbix-proxy

# Restaurar configuração
sudo cp /tmp/zabbix_backup_*/zabbix_proxy.conf.backup /etc/zabbix/zabbix_proxy.conf

# Restaurar banco
sudo cp /tmp/zabbix_backup_*/zabbix_proxy.db.backup /var/lib/zabbix/zabbix_proxy.db

# Ajustar permissões
sudo chown zabbix:zabbix /var/lib/zabbix/zabbix_proxy.db
sudo chmod 660 /var/lib/zabbix/zabbix_proxy.db

# Iniciar serviço
sudo systemctl start zabbix-proxy
```

## 📝 Logs do Script

O script gera logs coloridos com timestamps:
- 🔵 **Azul**: Informações gerais
- 🟢 **Verde**: Sucesso
- 🟡 **Amarelo**: Avisos
- 🔴 **Vermelho**: Erros

## ⚠️ Avisos Importantes

1. **Sempre faça backup** antes de executar (o script já faz automaticamente)
2. **Teste em ambiente de desenvolvimento** primeiro
3. **Verifique conectividade** com o Zabbix Server após a atualização
4. **Monitore os logs** nas primeiras horas após a atualização
5. **Mantenha o backup** até confirmar que tudo está funcionando

## 🆘 Suporte

Em caso de problemas:
1. Verifique os logs do script
2. Consulte os logs do Zabbix: `/var/log/zabbix/zabbix_proxy.log`
3. Use os comandos de verificação listados acima
4. O backup está sempre disponível para restauração manual

---

**Versão do Script**: 1.0  
**Data**: 2025-07-01  
**Compatível com**: Zabbix Proxy 7.0 LTS + SQLite3
