# CONFIGURAÇÕES
# Usar Windows Forms para caixas de diálogo
Add-Type -AssemblyName System.Windows.Forms

# Arquivo de configuração
$configFile = Join-Path $PSScriptRoot "compressar-videos.config"

# Função para selecionar pasta
function Select-FolderDialog([string]$Description="Selecione uma pasta", [string]$InitialDirectory="C:\") {
    $folderBrowser = New-Object System.Windows.Forms.FolderBrowserDialog
    $folderBrowser.Description = $Description
    $folderBrowser.SelectedPath = $InitialDirectory
    
    if ($folderBrowser.ShowDialog() -eq "OK") {
        return $folderBrowser.SelectedPath
    }
    return $null
}

# Verificar se já existe configuração
if (Test-Path $configFile) {
    try {
        $config = Get-Content -Path $configFile -Raw | ConvertFrom-Json
        $sourceFolder = $config.SourceFolder
        $destinationFolder = $config.DestinationFolder
        
        # Verificar se as pastas ainda existem
        if (-not (Test-Path $sourceFolder) -or -not (Test-Path $destinationFolder)) {
            throw "Uma ou ambas as pastas configuradas não existem mais."
        }
    }
    catch {
        # Se houver erro ao carregar configuração, pedir novamente
        Write-Host "Erro ao carregar configuração: $_" -ForegroundColor Red
        $needNewConfig = $true
    }
}
else {
    $needNewConfig = $true
}

# Se precisar de nova configuração
if ($needNewConfig) {
    # Selecionar pasta de origem
    $sourceFolder = Select-FolderDialog -Description "Selecione a pasta com os vídeos originais" -InitialDirectory "C:\"
    if (-not $sourceFolder) {
        Write-Host "Operação cancelada pelo usuário." -ForegroundColor Yellow
        exit
    }

    # Selecionar pasta de destino
    $destinationFolder = Select-FolderDialog -Description "Selecione a pasta para os vídeos convertidos" -InitialDirectory "C:\"
    if (-not $destinationFolder) {
        Write-Host "Operação cancelada pelo usuário." -ForegroundColor Yellow
        exit
    }
    
    # Salvar configuração
    $config = @{
        SourceFolder = $sourceFolder
        DestinationFolder = $destinationFolder
    }
    $config | ConvertTo-Json | Set-Content -Path $configFile
    
    Write-Host "Configuração salva em $configFile" -ForegroundColor Green
}

$ffmpegPath = ".\bin\ffmpeg.exe"  # deve estar na mesma pasta do script
$videoExtensions = @(".mp4", ".avi", ".mov", ".wmv", ".mkv", ".dav")

# CRIAR PASTA DE DESTINO SE NÃO EXISTIR
if (!(Test-Path $destinationFolder)) {
    New-Item -ItemType Directory -Path $destinationFolder -Force
}

# PERCORRER ARQUIVOS
try {
    $totalFiles = 0
    $convertedFiles = 0
    $skippedFiles = 0
    
    Write-Host "Buscando arquivos de video em $sourceFolder..." -ForegroundColor Cyan
    
    $videoFiles = Get-ChildItem -Path $sourceFolder -Recurse -File -ErrorAction SilentlyContinue -ErrorVariable accessErrors | 
    Where-Object {
        $videoExtensions -contains $_.Extension.ToLower()
    }
    
    $totalFiles = $videoFiles.Count
    Write-Host "Encontrados $totalFiles arquivos de video para processar." -ForegroundColor Green
    
    $currentFile = 0
    
    $videoFiles | ForEach-Object {
        $currentFile++
        $sourceFile = $_.FullName
        $relativePath = $sourceFile.Substring($sourceFolder.Length).TrimStart('\')
        $outputPath = Join-Path $destinationFolder $relativePath
        $outputDir = Split-Path $outputPath

        # Nome do arquivo final com sufixo e extensão .mp4
        $outputFile = [System.IO.Path]::ChangeExtension($outputPath, ".mp4")
        $outputFile = $outputFile.Replace(".mp4", "-inmetro.mp4")

        # CRIA SUBPASTAS NO DESTINO SE PRECISAR
        if (!(Test-Path $outputDir)) {
            New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
        }

        # PULA SE JÁ FOI CONVERTIDO
        if (!(Test-Path $outputFile)) {
            Write-Host "[$currentFile/$totalFiles] Convertendo: $($_.Name)" -ForegroundColor Yellow
            Write-Host "  Origem: $sourceFile" -ForegroundColor DarkGray
            Write-Host "  Destino: $outputFile" -ForegroundColor DarkGray

            & $ffmpegPath -i "$sourceFile" `
                -vf "scale=1280:720" `
                -c:v libx265 -b:v 280k -preset slow -pix_fmt yuv420p `
                -map_metadata -1 -an "$outputFile"
                
            if ($LASTEXITCODE -eq 0) {
                $convertedFiles++
                Write-Host "  Conversao concluida com sucesso!" -ForegroundColor Green
            } else {
                Write-Host "  Erro na conversao!" -ForegroundColor Red
            }
        } else {
            Write-Host "[$currentFile/$totalFiles] Ja convertido: $($_.Name)" -ForegroundColor Blue
            $skippedFiles++
        }
    }

    # Exibir resumo
    Write-Host "`nResumo da operacao do conversor Nubium:" -ForegroundColor Cyan
    Write-Host "  Total de arquivos: $totalFiles" -ForegroundColor White
    Write-Host "  Arquivos convertidos: $convertedFiles" -ForegroundColor Green
    Write-Host "  Arquivos ja existentes (pulados): $skippedFiles" -ForegroundColor Blue

    # Exibir resumo de erros de acesso, se houver
    if ($accessErrors) {
        Write-Warning "`nAlguns arquivos ou pastas nao puderam ser acessados devido a permissoes insuficientes:"
        $accessErrors | ForEach-Object {
            Write-Warning "- $($_.TargetObject): $($_.Exception.Message)"
        }
    }
}
catch {
    Write-Error "Erro ao processar arquivos: $_"
}
