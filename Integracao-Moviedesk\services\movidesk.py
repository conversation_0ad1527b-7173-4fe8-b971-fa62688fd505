import requests
from datetime import datetime, timedelta
from config import MOVIEDESK_API_TOKEN, MOVIEDESK_BASE_URL

# 🚀 Configurações de período otimizadas
PERIODOS_BUSCA = {
    "1_semana": 7,
    "2_semanas": 14,
    "1_mes": 30,
    "3_meses": 90,
    "6_meses": 180,
    "1_ano": 365
}

def buscar_tickets_por_titulo(titulo_exato, dias_historico=90):
    """
    Busca tickets por título exato com filtro de data otimizado

    Args:
        titulo_exato (str): Título exato do ticket para buscar
        dias_historico (int): Número de dias para buscar no histórico (padrão: 90 dias = 3 meses)

    Returns:
        list: Lista de tickets encontrados
    """
    url = f"{MOVIEDESK_BASE_URL}/tickets"
    todos_tickets = []
    skip = 0
    limite_por_pagina = 1000

    # 🚀 Filtro de data otimizado - busca nos últimos X dias
    data_limite = (datetime.now() - timedelta(days=dias_historico)).strftime("%Y-%m-%dT00:00:00")

    print(f"🔍 Buscando tickets '{titulo_exato}' criados após {data_limite[:10]} ({dias_historico} dias)")

    while True:
        # ✅ NOVO: Headers com Bearer Token
        headers = {
            "Authorization": f"Bearer {MOVIEDESK_API_TOKEN}",
            "Content-Type": "application/json"
        }

        # 🚀 OTIMIZADO: Filtro com ordenação por data (mais recentes primeiro)
        params = {
            "filter": f"subject eq '{titulo_exato}' and baseStatus in ('New','InAttendance','Waiting') and createdDate ge {data_limite}",
            "$select": "id,subject,status,baseStatus,createdDate,lastActionDate",
            "$expand": "actions",
            "$orderby": "createdDate desc",  # Mais recentes primeiro
            "$top": limite_por_pagina,
            "$skip": skip
        }

        # ✅ ATUALIZADO: Adicionado headers e verify=False
        response = requests.get(url, headers=headers, params=params, verify=False)

        if not response.ok:
            print(f"❌ Erro ao buscar tickets: {response.status_code} - {response.text}")
            break

        page_tickets = response.json()
        todos_tickets.extend(page_tickets)

        if len(page_tickets) < limite_por_pagina:
            break

        skip += limite_por_pagina

    return todos_tickets


def buscar_tickets_periodo(titulo_exato, periodo="3_meses"):
    """
    Busca tickets usando períodos pré-definidos para maior facilidade

    Args:
        titulo_exato (str): Título exato do ticket
        periodo (str): Período de busca ('1_semana', '2_semanas', '1_mes', '3_meses', '6_meses', '1_ano')

    Returns:
        list: Lista de tickets encontrados
    """
    if periodo not in PERIODOS_BUSCA:
        print(f"⚠️ Período '{periodo}' não reconhecido. Usando '3_meses' como padrão.")
        periodo = "3_meses"

    dias = PERIODOS_BUSCA[periodo]
    print(f"🔍 Buscando tickets no período: {periodo} ({dias} dias)")

    return buscar_tickets_por_titulo(titulo_exato, dias_historico=dias)


def responder_ticket(ticket_id, resposta):
    # ✅ ATUALIZADO: Removido ?token= da URL
    url = f"{MOVIEDESK_BASE_URL}/tickets/{ticket_id}/actions"

    # ✅ NOVO: Headers com Bearer Token
    headers = {
        "Authorization": f"Bearer {MOVIEDESK_API_TOKEN}",
        "Content-Type": "application/json"
    }

    payload = {
        "type": 1,
        "description": resposta,
        "origin": 2,  # Origem: Web API
        "status": 1   # Mantém como aberto
    }

    # ✅ ATUALIZADO: Adicionado headers e verify=False
    response = requests.post(url, headers=headers, json=payload, verify=False)
    return response.status_code
