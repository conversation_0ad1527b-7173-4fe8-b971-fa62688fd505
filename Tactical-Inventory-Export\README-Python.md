# 🐍 Scripts Python para Exportação de Inventário Tactical RMM

Scripts Python para exportar inventário de estações do Tactical RMM. Estes scripts podem ser executados diretamente via Tactical RMM ou localmente.

## 📁 Scripts Disponíveis

### 1. `tactical_rmm_inventory.py` - Script Simplificado
**Para execução via Tactical RMM**
- ✅ Não requer argumentos
- ✅ Configurações fixas no código
- ✅ Exporta CSV e JSON automaticamente
- ✅ Cria diretório de saída automaticamente
- ✅ Gera resumo estatístico

### 2. `tactical_inventory_export.py` - Script Básico
**Para execução local com argumentos**
- ✅ Suporte a argumentos de linha de comando
- ✅ Filtros por cliente e site
- ✅ Múltiplos formatos de saída
- ✅ Controle de agentes offline

### 3. `tactical_inventory_advanced.py` - Script Avançado
**Para exportação detalhada**
- ✅ Informações de hardware via WMI
- ✅ Lista de software instalado
- ✅ Custom fields
- ✅ Múltiplos arquivos de saída
- ✅ Controle de rate limiting

## 🚀 Execução via Tactical RMM

### Script Recomendado: `tactical_rmm_inventory.py`

1. **No Tactical RMM:**
   - Vá em **Scripts → Add Script**
   - Nome: `Exportar Inventário`
   - Tipo: **Python**
   - Cole o conteúdo do arquivo `tactical_rmm_inventory.py`

2. **Configurar Token:**
   - Edite a linha: `API_TOKEN = "SEU_TOKEN_AQUI"`
   - Ou use o token padrão já configurado

3. **Executar:**
   - Selecione agentes ou grupos
   - Execute o script
   - Arquivos serão salvos em `C:\TacticalInventory\`

### Saída do Script:
```
C:\TacticalInventory\
├── TacticalRMM_Inventario_20250102_143022.csv
├── TacticalRMM_Inventario_20250102_143022.json
└── TacticalRMM_Inventario_20250102_143022_resumo.json
```

## 💻 Execução Local

### Pré-requisitos:
```bash
pip install requests
```

### Script Básico:
```bash
# Exportação completa
python tactical_inventory_export.py

# Cliente específico
python tactical_inventory_export.py --client-id 8

# Site específico  
python tactical_inventory_export.py --site-id 54

# Formato JSON
python tactical_inventory_export.py --format json

# Com token personalizado
python tactical_inventory_export.py --token "SEU_TOKEN_AQUI"

# Excluir agentes offline
python tactical_inventory_export.py --include-offline false
```

### Script Avançado:
```bash
# Exportação com hardware
python tactical_inventory_advanced.py --include-hardware

# Exportação com software
python tactical_inventory_advanced.py --include-software

# Exportação completa
python tactical_inventory_advanced.py --include-hardware --include-software --include-custom-fields

# Cliente específico com hardware
python tactical_inventory_advanced.py --client-id 8 --include-hardware

# Limitar agentes processados
python tactical_inventory_advanced.py --max-agents 20 --include-hardware
```

## 📊 Dados Exportados

### Inventário Básico:
- **Identificação:** ID, Hostname, Cliente, Site
- **Status:** Online/Offline/Recente, Último contato
- **Sistema:** SO, Versão, Arquitetura
- **Hardware:** CPU, RAM, Antivírus
- **Rede:** IP público, Domínio
- **Monitoramento:** Falhas de serviços/checks, Manutenção

### Inventário Avançado (script avançado):
- **Hardware Detalhado:** CPU (cores, threads, velocidade), RAM (slots, total), Discos (tamanho, espaço livre), Placa-mãe, BIOS, Serial
- **Software:** Lista completa com versões, editores, tamanhos
- **Custom Fields:** Campos personalizados configurados

## ⚙️ Configuração

### Token de API:
1. Acesse Tactical RMM → Settings → Global Settings → API Keys
2. Crie nova API Key com permissões de leitura
3. Configure no script:
```python
API_TOKEN = "SEU_TOKEN_AQUI"
```

### Personalização:
Edite as constantes no início dos scripts:
```python
API_URL = "https://api.centralmesh.nvirtual.com.br"
API_TOKEN = "SEU_TOKEN_AQUI"
OUTPUT_DIR = "C:\\TacticalInventory"  # Para script simplificado
```

## 📈 Casos de Uso

### 1. Relatório Diário Automático
**Via Tactical RMM:**
- Configure `tactical_rmm_inventory.py` como script
- Agende execução diária em todos os agentes
- Arquivos salvos automaticamente

### 2. Auditoria de Cliente Específico
**Local:**
```bash
python tactical_inventory_advanced.py --client-id 8 --include-hardware --include-software
```

### 3. Inventário de Hardware para Planejamento
**Local:**
```bash
python tactical_inventory_advanced.py --include-hardware --max-agents 100
```

### 4. Monitoramento de Status
**Via Tactical RMM:**
- Execute `tactical_rmm_inventory.py`
- Analise arquivo `_resumo.json` para estatísticas

## 🔧 Solução de Problemas

### Erro de Módulo:
```
ModuleNotFoundError: No module named 'requests'
```
**Solução:**
```bash
pip install requests
```

### Erro de Token:
```
Erro ao buscar agents: 401 Unauthorized
```
**Solução:** Verificar token de API e permissões

### Erro de Timeout:
```
Erro ao buscar agents: timeout
```
**Solução:** Verificar conectividade de rede

### Erro de Permissão (Windows):
```
PermissionError: [Errno 13] Permission denied
```
**Solução:** Executar como administrador ou alterar `OUTPUT_DIR`

## 📝 Logs e Debugging

Todos os scripts incluem logging detalhado:
```
[2025-01-02 14:30:22] Buscando dados da API...
[2025-01-02 14:30:23] Encontrados: 45 agentes, 3 clientes, 12 sites
[2025-01-02 14:30:24] Processando dados dos agentes...
[2025-01-02 14:30:25] CSV exportado: TacticalRMM_Inventario_20250102_143022.csv
```

## 🔒 Segurança

- **Tokens:** Nunca commite tokens no código em produção
- **Permissões:** Use tokens com escopo mínimo necessário
- **Arquivos:** Proteja arquivos de saída com permissões adequadas
- **Logs:** Evite logar informações sensíveis

## 🚀 Vantagens dos Scripts Python

### vs PowerShell:
- ✅ **Multiplataforma:** Funciona em Windows, Linux, macOS
- ✅ **Sem dependências:** Apenas `requests` (padrão em muitos ambientes)
- ✅ **Tactical RMM nativo:** Execução direta via interface
- ✅ **Parsing JSON:** Manipulação nativa de dados da API
- ✅ **Controle de erro:** Tratamento robusto de exceções

### Para Tactical RMM:
- ✅ **Execução remota:** Roda em qualquer agente
- ✅ **Logs centralizados:** Saída visível na interface
- ✅ **Agendamento:** Fácil automação via políticas
- ✅ **Distribuição:** Não precisa instalar em cada máquina

## 📞 Suporte

Para problemas ou melhorias:
1. Verifique logs de execução
2. Teste conectividade com API
3. Valide permissões do token
4. Consulte documentação da API Tactical RMM

---

**Desenvolvido por NVirtual** 🚀
