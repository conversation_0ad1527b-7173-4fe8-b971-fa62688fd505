from services import chatgpt, movidesk
from utils.logger import logger

def processar_ticket(ticket):
    ticket_id = ticket["id"]
    acoes = ticket.get("actions", [])
    if not acoes:
        logger.info(f"❌ Ticket #{ticket_id} sem ações. Ignorado.")
        return

    ultima_acao = acoes[-1]["description"]
    logger.info(f"📨 Última ação do ticket #{ticket_id}:\n{ultima_acao}")

    resposta = chatgpt.gerar_resposta(ultima_acao)
    logger.info(f"🤖 Resposta gerada para o ticket #{ticket_id}:\n{resposta}")

    status = movidesk.responder_ticket(ticket_id, resposta)
    logger.info(f"✅ Ticket #{ticket_id} respondido. Status HTTP: {status}")
