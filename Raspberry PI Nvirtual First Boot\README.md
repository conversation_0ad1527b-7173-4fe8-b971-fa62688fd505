# Raspberry Pi First Boot - Auto Instalação NVirtual

Este projeto configura um Raspberry Pi para executar automaticamente a instalação do Zabbix Proxy, Zabbix Agent e Tactical RMM apenas no primeiro boot.

## Objetivo

Criar uma imagem única de SD card que pode ser usada em vários Raspberry Pi. No primeiro boot, cada dispositivo:
- Baixa automaticamente o script mais atual do GitHub
- Executa a instalação do Zabbix e Tactical RMM
- Configura-se automaticamente com hostname baseado no serial
- Usa DHCP por padrão para evitar conflitos de IP

## Estrutura do Projeto

- `first-boot.service` - Serviço systemd que executa apenas no primeiro boot
- `first-boot.sh` - Script principal que baixa e executa o script do GitHub
- `raspberry-auto-setup.sh` - Script específico para Raspberry Pi (vai para o GitHub)
- `simple-auto-setup.sh` - Versão simplificada do script de configuração
- `setup.sh` - Script para configurar um Raspberry Pi já funcionando
- `install-to-image.sh` - Script para modificar uma imagem .img

## Scripts Disponíveis

### raspberry-auto-setup.sh
Script completo que:
- Define hostname baseado no serial do dispositivo (rpi-XXXXXX)
- Executa instalação do Zabbix com parâmetros fixos:
  - Hostname: temporario (ou baseado no serial)
  - Cliente ID: 1
  - Filial: 1
  - Rede: DHCP
- Instala Tactical RMM automaticamente
- Aplica otimizações específicas para Raspberry Pi

### simple-auto-setup.sh
Versão simplificada para testes rápidos

## Como Usar

### Opção 1: Modificar uma imagem existente (Recomendado)
```bash
sudo ./install-to-image.sh /caminho/para/raspios-lite.img
```

### Opção 2: Instalar em um Raspberry Pi já funcionando
```bash
sudo ./setup.sh
```

## Configuração Automática

O script está pré-configurado para:
- **Repositório GitHub**: `https://github.com/paulomatheusgrr/nvirtual-projects`
- **Script executado**: `Raspberry PI Nvirtual First Boot/raspberry-auto-setup.sh`
- **Hostname Zabbix**: `temporario` (ou baseado no serial)
- **Cliente ID**: `1`
- **Filial**: `1`
- **Rede**: DHCP (sem IP fixo)

## Vantagens

✅ **Uma imagem para todos**: Mesma imagem funciona em qualquer Raspberry Pi
✅ **Sempre atualizado**: Baixa o script mais recente do GitHub
✅ **Zero configuração**: Não precisa tocar no Raspberry Pi
✅ **Identificação única**: Hostname baseado no serial do dispositivo
✅ **Sem conflitos de IP**: Usa DHCP por padrão
✅ **Logs detalhados**: Tudo registrado em `/var/log/first-boot.log`

## Requisitos

- Raspberry Pi OS (Lite ou Desktop)
- Conexão com internet no primeiro boot
- Acesso ao repositório GitHub público
