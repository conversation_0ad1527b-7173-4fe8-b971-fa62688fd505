#!/bin/bash

# Exemplo de script que será executado no primeiro boot
# Este arquivo deve estar no seu repositório GitHub

set -e

echo "=== Script de Primeiro Boot - Exemplo ==="
echo "Executando em: $(date)"
echo "Usuário: $(whoami)"
echo "Diretório: $(pwd)"

# Exemplo: Atualizar sistema
echo "Atualizando sistema..."
apt-get update
apt-get upgrade -y

# Exemplo: Instalar pacotes
echo "Instalando pacotes necessários..."
apt-get install -y \
    curl \
    wget \
    git \
    python3 \
    python3-pip \
    nodejs \
    npm

# Exemplo: Configurar usuário pi
echo "Configurando usuário pi..."
usermod -aG sudo pi

# Exemplo: Configurar SSH
echo "Habilitando SSH..."
systemctl enable ssh
systemctl start ssh

# Exemplo: Configurar Wi-Fi (se necessário)
# echo "Configurando Wi-Fi..."
# cat > /etc/wpa_supplicant/wpa_supplicant.conf << EOF
# country=BR
# ctrl_interface=DIR=/var/run/wpa_supplicant GROUP=netdev
# update_config=1
# 
# network={
#     ssid="SUA_REDE_WIFI"
#     psk="SUA_SENHA_WIFI"
# }
# EOF

# Exemplo: Instalar aplicação Python
echo "Instalando aplicação Python..."
pip3 install flask requests

# Exemplo: Criar serviço personalizado
echo "Criando serviço personalizado..."
cat > /etc/systemd/system/minha-aplicacao.service << EOF
[Unit]
Description=Minha Aplicação
After=network.target

[Service]
Type=simple
User=pi
WorkingDirectory=/home/<USER>
ExecStart=/usr/bin/python3 /home/<USER>/app.py
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# Habilitar o serviço
systemctl enable minha-aplicacao.service

# Exemplo: Configurar firewall
echo "Configurando firewall..."
ufw --force enable
ufw allow ssh
ufw allow 80
ufw allow 443

# Exemplo: Criar arquivo de configuração
echo "Criando arquivo de configuração..."
cat > /home/<USER>/config.json << EOF
{
    "device_id": "$(cat /proc/cpuinfo | grep Serial | cut -d ' ' -f 2)",
    "setup_date": "$(date -Iseconds)",
    "version": "1.0.0"
}
EOF

chown pi:pi /home/<USER>/config.json

# Exemplo: Log de conclusão
echo "=== Setup concluído com sucesso ==="
echo "Device ID: $(cat /proc/cpuinfo | grep Serial | cut -d ' ' -f 2)"
echo "Data/Hora: $(date)"

# Opcional: Reiniciar após configuração
# echo "Reiniciando em 10 segundos..."
# sleep 10
# reboot
