#!/usr/bin/env python3

"""
Diagnóstico de Detecção - Network Scanner
Ajuda a entender por que alguns dispositivos ficam como "Desconhecido"
Autor: <PERSON>
"""

import subprocess
import socket
import sys
import time
import ipaddress
from datetime import datetime

def print_banner():
    """Banner do diagnóstico"""
    print("=" * 80)
    print("🔍 DIAGNÓSTICO DE DETECÇÃO - NETWORK SCANNER")
    print("=" * 80)
    print("Este script ajuda a entender por que dispositivos ficam 'Desconhecido'")
    print("=" * 80)

def test_ping_methods(ip):
    """Testa diferentes métodos de ping"""
    print(f"\n🏓 Testando métodos de ping para {ip}:")
    
    methods = []
    
    # Ping normal
    try:
        if sys.platform.startswith('win'):
            cmd = ['ping', '-n', '1', '-w', '2000', str(ip)]
        else:
            cmd = ['ping', '-c', '1', '-W', '2', str(ip)]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            methods.append("✅ Ping normal: SUCESSO")
            # Extrair TTL
            if 'TTL=' in result.stdout:
                ttl = result.stdout.split('TTL=')[1].split()[0]
                methods.append(f"   TTL detectado: {ttl}")
            elif 'ttl=' in result.stdout:
                ttl = result.stdout.split('ttl=')[1].split()[0]
                methods.append(f"   TTL detectado: {ttl}")
        else:
            methods.append("❌ Ping normal: FALHOU")
    except Exception as e:
        methods.append(f"❌ Ping normal: ERRO - {e}")
    
    # Ping com tamanhos diferentes
    for size in [64, 1024]:
        try:
            if sys.platform.startswith('win'):
                cmd = ['ping', '-n', '1', '-l', str(size), str(ip)]
            else:
                cmd = ['ping', '-c', '1', '-s', str(size), str(ip)]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                methods.append(f"✅ Ping {size} bytes: SUCESSO")
            else:
                methods.append(f"❌ Ping {size} bytes: FALHOU")
        except:
            methods.append(f"❌ Ping {size} bytes: ERRO")
    
    for method in methods:
        print(f"   {method}")
    
    return len([m for m in methods if "SUCESSO" in m]) > 0

def test_hostname_resolution(ip):
    """Testa resolução de hostname"""
    print(f"\n🏷️  Testando resolução de hostname para {ip}:")
    
    try:
        hostname = socket.gethostbyaddr(str(ip))[0]
        print(f"   ✅ Hostname encontrado: {hostname}")
        return hostname
    except socket.herror:
        print(f"   ❌ Nenhum hostname configurado")
    except Exception as e:
        print(f"   ❌ Erro na resolução: {e}")
    
    return None

def test_port_scanning(ip):
    """Testa scan de portas"""
    print(f"\n🔌 Testando portas comuns em {ip}:")
    
    common_ports = [21, 22, 23, 25, 53, 80, 135, 139, 443, 445, 3389, 5353, 8080, 9100]
    open_ports = []
    
    for port in common_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((str(ip), port))
            if result == 0:
                open_ports.append(port)
                print(f"   ✅ Porta {port}: ABERTA")
            sock.close()
        except:
            pass
    
    if not open_ports:
        print(f"   ❌ Nenhuma porta comum encontrada aberta")
        print(f"   💡 Possíveis causas:")
        print(f"      - Firewall bloqueando conexões")
        print(f"      - Dispositivo com configuração de segurança alta")
        print(f"      - Serviços rodando em portas não-padrão")
    
    return open_ports

def test_http_detection(ip):
    """Testa detecção HTTP"""
    print(f"\n🌐 Testando detecção HTTP em {ip}:")
    
    for port in [80, 443, 8080, 8443]:
        try:
            import urllib.request
            import urllib.error
            
            protocol = "https" if port in [443, 8443] else "http"
            url = f"{protocol}://{ip}:{port}/"
            
            req = urllib.request.Request(url)
            req.add_header('User-Agent', 'NetworkScanner/1.0')
            
            response = urllib.request.urlopen(req, timeout=3)
            headers = dict(response.headers)
            
            print(f"   ✅ HTTP porta {port}: SUCESSO")
            if 'Server' in headers:
                print(f"      Server: {headers['Server']}")
            
            # Ler um pouco do conteúdo
            content = response.read(512).decode('utf-8', errors='ignore')
            if '<title>' in content.lower():
                import re
                title_match = re.search(r'<title[^>]*>([^<]+)</title>', content, re.IGNORECASE)
                if title_match:
                    print(f"      Título: {title_match.group(1).strip()}")
            
            return True
            
        except urllib.error.HTTPError as e:
            print(f"   ⚠️  HTTP porta {port}: Erro {e.code}")
        except urllib.error.URLError:
            pass  # Conexão recusada, normal
        except Exception as e:
            pass  # Outros erros, normal
    
    print(f"   ❌ Nenhum serviço HTTP detectado")
    return False

def analyze_detection_issues(ip, ping_ok, hostname, open_ports, http_ok):
    """Analisa problemas de detecção"""
    print(f"\n📊 ANÁLISE DE DETECÇÃO PARA {ip}:")
    print("=" * 50)
    
    issues = []
    suggestions = []
    
    if not ping_ok:
        issues.append("❌ Não responde a ping")
        suggestions.append("💡 Dispositivo pode ter ping desabilitado ou firewall ativo")
    
    if not hostname:
        issues.append("❌ Sem hostname configurado")
        suggestions.append("💡 Configure DNS reverso ou hostname no dispositivo")
    
    if not open_ports:
        issues.append("❌ Nenhuma porta comum aberta")
        suggestions.append("💡 Dispositivo com alta segurança ou serviços customizados")
    
    if not http_ok:
        issues.append("❌ Sem interface web detectada")
        suggestions.append("💡 Dispositivo pode não ter interface web ou usar portas não-padrão")
    
    if not issues:
        print("✅ Dispositivo bem configurado para detecção!")
        return
    
    print("🔍 Problemas encontrados:")
    for issue in issues:
        print(f"   {issue}")
    
    print("\n💡 Sugestões para melhorar detecção:")
    for suggestion in suggestions:
        print(f"   {suggestion}")
    
    print("\n🛠️  Possíveis soluções:")
    print("   • Aumentar timeout: --timeout 5")
    print("   • Reduzir threads: -t 50")
    print("   • Verificar configurações de firewall")
    print("   • Configurar hostname/DNS no dispositivo")

def diagnose_ip(ip):
    """Diagnóstica um IP específico"""
    print(f"\n🎯 DIAGNOSTICANDO: {ip}")
    print("=" * 80)
    
    # Testes
    ping_ok = test_ping_methods(ip)
    hostname = test_hostname_resolution(ip)
    open_ports = test_port_scanning(ip)
    http_ok = test_http_detection(ip)
    
    # Análise
    analyze_detection_issues(ip, ping_ok, hostname, open_ports, http_ok)

def main():
    """Função principal"""
    print_banner()
    
    if len(sys.argv) < 2:
        print("Uso: python diagnostico_deteccao.py <IP_ou_REDE>")
        print("\nExemplos:")
        print("  python diagnostico_deteccao.py ***********")
        print("  python diagnostico_deteccao.py ***********/24")
        sys.exit(1)
    
    target = sys.argv[1]
    
    try:
        # Verificar se é um IP único ou rede
        if '/' in target:
            network = ipaddress.IPv4Network(target)
            print(f"🌐 Diagnosticando rede: {network}")
            print(f"📊 Total de IPs: {network.num_addresses}")
            
            if network.num_addresses > 10:
                print("⚠️  Rede muito grande! Diagnosticando apenas os primeiros 10 IPs...")
                ips = list(network.hosts())[:10]
            else:
                ips = list(network.hosts())
            
            for ip in ips:
                diagnose_ip(ip)
                time.sleep(1)  # Pausa entre diagnósticos
        else:
            ip = ipaddress.IPv4Address(target)
            diagnose_ip(ip)
    
    except ValueError:
        print(f"❌ Formato inválido: {target}")
        print("Use formato IP (***********) ou CIDR (***********/24)")
        sys.exit(1)
    
    print("\n" + "=" * 80)
    print("✅ DIAGNÓSTICO CONCLUÍDO")
    print("=" * 80)
    print("💡 Use as sugestões acima para melhorar a detecção no network scanner")

if __name__ == "__main__":
    main()
