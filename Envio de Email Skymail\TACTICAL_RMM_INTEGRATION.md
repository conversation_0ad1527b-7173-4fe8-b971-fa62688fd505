# 🎯 Tactical RMM - Network Scanner Integration

Integração do Network Scanner com o Tactical RMM para execução remota de scans de rede.

## 📋 Arquivos para Tactical RMM

### 1. **Script PowerShell**: `TacticalRMM-NetworkScanner.ps1`
- Script principal para execução no Tactical RMM
- Inclui o scanner Python embutido
- Não requer arquivos externos

### 2. **Script Python**: `tactical_network_scanner.py`
- Versão standalone para uso direto
- Saída otimizada para Tactical RMM

## 🚀 Configuração no Tactical RMM

### **Passo 1: Adicionar Script**
1. Acesse **Scripts** no Tactical RMM
2. Clique em **Add Script**
3. Configure:
   - **Name**: `Network Scanner`
   - **Description**: `Scanner de rede avançado - identifica dispositivos, OS e modelos`
   - **Category**: `Network Tools`
   - **Type**: `PowerShell`
   - **Script Body**: Cole o conteúdo de `TacticalRMM-NetworkScanner.ps1`

### **Passo 2: Configurar Argumentos**
Adicione os seguintes argumentos no script:

```powershell
# Argumentos obrigatórios
-NetworkRange "***********/24"

# Argumentos opcionais
-Threads 50
-Timeout 2
-JsonOutput
```

### **Passo 3: Criar Política de Execução**
1. Vá em **Automation > Policies**
2. Crie nova política: `Network Scan Policy`
3. Adicione o script com argumentos padrão

## 📊 Exemplos de Uso

### **Scan Básico de Sub-rede**
```powershell
# Argumentos no Tactical RMM:
-NetworkRange "***********/24"
```

### **Scan Rápido com Mais Threads**
```powershell
# Argumentos no Tactical RMM:
-NetworkRange "***********/24" -Threads 100 -Timeout 1
```

### **Scan de Rede Corporativa**
```powershell
# Argumentos no Tactical RMM:
-NetworkRange "10.0.0.0/24" -Threads 50 -Timeout 2
```

### **Scan com Saída JSON**
```powershell
# Argumentos no Tactical RMM:
-NetworkRange "***********/24" -JsonOutput
```

## 📈 Interpretação dos Resultados

### **Saída Padrão**
```
TACTICAL-INFO: Network Scanner v1.0 - Paulo Matheus
TACTICAL-INFO: Rede: ***********/24 | Threads: 50 | Timeout: 2
TACTICAL-SCAN-START: ***********/24 | 254 IPs
FOUND: *********** | router.local | Network Device | TP-Link Archer C5
FOUND: ***********0 | iPhone-Paulo.local | iOS | iPhone/iPad
FOUND: ************ | windows-pc.local | Windows | Windows Server/Desktop
FOUND: ************ | ubuntu-server.local | Linux | Linux Server
TACTICAL-SCAN-COMPLETE: 4 devices found in 45.2s
TACTICAL-STATS-OS: {"Network Device": 1, "iOS": 1, "Windows": 1, "Linux": 1}
TACTICAL-STATS-DEVICES: {"TP-Link Archer C5": 1, "iPhone/iPad": 1, "Windows Server/Desktop": 1, "Linux Server": 1}
```

### **Formato das Linhas FOUND**
```
FOUND: [IP] | [Hostname] | [OS] | [Dispositivo/Modelo]
```

### **Estatísticas Finais**
- `TACTICAL-STATS-OS`: Contagem por sistema operacional
- `TACTICAL-STATS-DEVICES`: Contagem por tipo de dispositivo

## 🎯 Casos de Uso no Tactical RMM

### **1. Auditoria de Rede Remota**
- Execute em clientes para mapear dispositivos
- Identifique equipamentos não autorizados
- Documente infraestrutura de rede

### **2. Troubleshooting Remoto**
- Localize dispositivos específicos
- Verifique conectividade de rede
- Identifique problemas de configuração

### **3. Inventário Automatizado**
- Execute periodicamente via políticas
- Mantenha inventário atualizado
- Detecte novos dispositivos

### **4. Segurança de Rede**
- Monitore dispositivos desconhecidos
- Identifique possíveis intrusões
- Verifique conformidade de rede

## ⚙️ Configurações Recomendadas

### **Para Sub-redes Pequenas (/24)**
```powershell
-NetworkRange "***********/24" -Threads 50 -Timeout 2
```

### **Para Redes Médias (/22, /23)**
```powershell
-NetworkRange "10.0.0.0/22" -Threads 100 -Timeout 1
```

### **Para Redes Grandes (/16)**
```powershell
-NetworkRange "***********/16" -Threads 200 -Timeout 1
```

## 🔧 Troubleshooting

### **Erro: Python não encontrado**
```
TACTICAL-ERROR: Python 3 não encontrado. Instale Python 3.6+ primeiro.
```
**Solução**: Instale Python 3 no cliente via Tactical RMM

### **Erro: Formato de rede inválido**
```
TACTICAL-ERROR: Formato de rede inválido. Use formato CIDR (ex: ***********/24)
```
**Solução**: Use formato CIDR correto (IP/máscara)

### **Timeout muito baixo**
- Aumente o valor de `-Timeout` para redes lentas
- Reduza `-Threads` se houver muitos erros

### **Muitos dispositivos não detectados**
- Alguns dispositivos podem ter firewall ativo
- Aumente `-Timeout` para dispositivos lentos
- Verifique se a rede está acessível

## 📋 Dispositivos Detectados

### **Roteadores Identificados**
- TP-Link: Archer C5, C7, C9, TL-WR841N, etc.
- D-Link: DIR-615, DIR-842, DWR-921, etc.
- Netgear: R6120, R7000, AX12, etc.
- ASUS: RT-AC68U, RT-AX55, etc.
- Cisco, MikroTik, Ubiquiti

### **Dispositivos Móveis**
- iPhone, iPad (via porta 62078)
- Android (via mDNS e UPnP)
- Samsung, Xiaomi, Huawei

### **Impressoras**
- HP, Canon, Epson, Brother
- Detecção via porta 9100 e hostname

### **Servidores**
- Windows Server (via RDP 3389)
- Linux (Ubuntu, Debian, CentOS)
- NAS (Synology, QNAP)

## 📊 Relatórios e Logs

### **Salvar Resultados**
Use `-JsonOutput` para obter dados estruturados que podem ser processados por outros scripts do Tactical RMM.

### **Integração com Alertas**
Configure alertas no Tactical RMM baseados na saída do script:
- Novos dispositivos detectados
- Dispositivos desconhecidos
- Falhas de conectividade

## 👨‍💻 Suporte

**Desenvolvido por**: Paulo Matheus  
**Versão**: 1.0  
**Compatibilidade**: Tactical RMM, Windows, Linux  
**Requisitos**: Python 3.6+
