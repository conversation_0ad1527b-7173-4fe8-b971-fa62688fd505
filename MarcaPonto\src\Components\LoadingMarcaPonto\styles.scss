@import '../../Styles/responsive';

.full-loading__wrapper {
    height: 100vh;
    width: 100%;
    overflow: hidden;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeIn .2s;

    @keyframes fadeIn {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }

    .loading__logo {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;

        img {
            opacity: .1;
        }
    }

    .loading__content {
        z-index: 2;
        padding: 0 40px;

        h2 {
            font-size: 1.2rem;
            font-weight: 700;
            text-align: center;

            @include sm{
                font-size: 3rem;
            }
        }

        h3 {
            text-align: center;
        }
    }
}