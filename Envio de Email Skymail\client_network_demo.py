#!/usr/bin/env python3

"""
Demo do Network Scanner Organizado por Clientes
Demonstra como usar o scanner para organizar dispositivos por sub-redes /24
Cada sub-rede representa um cliente diferente
Autor: <PERSON>eus
"""

import sys
import os

# Adicionar o diretório do Network Scan ao path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Network Scan'))

from network_scanner import NetworkScanner
import argparse

def demo_client_scan():
    """Demonstração do scan organizado por clientes"""
    
    print("🎯 DEMO: Network Scanner Organizado por Clientes")
    print("=" * 60)
    print("Este demo mostra como o scanner organiza dispositivos por cliente")
    print("Cada sub-rede /24 é tratada como um cliente separado:")
    print("• Cliente 000: 192.168.0.1-254")
    print("• Cliente 001: ***********-254") 
    print("• Cliente 002: ***********-254")
    print("• ... e assim por diante")
    print("=" * 60)
    
    # Solicitar rede para scan
    print("\nOpções de rede para demonstração:")
    print("1. ***********/24 - Um único cliente (rápido)")
    print("2. ***********/22 - 4 clientes (médio)")
    print("3. ***********/20 - 16 clientes (lento)")
    print("4. Personalizada")
    
    choice = input("\nEscolha uma opção (1-4): ").strip()
    
    if choice == "1":
        network = "***********/24"
        threads = 50
    elif choice == "2":
        network = "***********/22"
        threads = 100
    elif choice == "3":
        network = "***********/20"
        threads = 150
    elif choice == "4":
        network = input("Digite a rede (formato CIDR): ").strip()
        threads = int(input("Digite o número de threads (50-200): ") or "100")
    else:
        print("Opção inválida. Usando padrão: ***********/24")
        network = "***********/24"
        threads = 50
    
    # Confirmar execução
    print(f"\n📡 Rede selecionada: {network}")
    print(f"⚡ Threads: {threads}")
    
    confirm = input("\nDeseja continuar? (s/N): ").strip().lower()
    if not confirm.startswith('s'):
        print("Demo cancelado.")
        return
    
    # Executar scan
    try:
        scanner = NetworkScanner(
            network=network,
            max_workers=threads,
            timeout=2
        )
        
        print(f"\n🚀 Iniciando scan da rede {network}...")
        scanner.scan_network()
        
        # Perguntar sobre exportação
        if scanner.results:
            print(f"\n✅ Scan concluído! {len(scanner.results)} dispositivos encontrados.")
            
            export_choice = input("\nDeseja exportar os resultados? (j=JSON, x=Excel, a=Ambos, n=Não): ").strip().lower()
            
            if export_choice in ['j', 'a']:
                filename = f"demo_clientes_{network.replace('/', '_').replace('.', '_')}"
                scanner.save_results(f"{filename}.json")
            
            if export_choice in ['x', 'a']:
                try:
                    filename = f"demo_clientes_{network.replace('/', '_').replace('.', '_')}"
                    scanner.save_to_excel(f"{filename}.xlsx")
                except Exception as e:
                    print(f"❌ Erro ao exportar Excel: {e}")
                    print("💡 Instale openpyxl: pip install openpyxl")
        else:
            print("\n⚠️  Nenhum dispositivo encontrado na rede especificada.")
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Scan interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro durante o scan: {e}")

def show_client_examples():
    """Mostra exemplos de organização por clientes"""
    
    print("\n📋 EXEMPLOS DE ORGANIZAÇÃO POR CLIENTES")
    print("=" * 60)
    
    examples = [
        {
            "network": "***********/16",
            "description": "Rede corporativa completa",
            "clients": "256 clientes (192.168.0.x até 192.168.255.x)",
            "use_case": "Grande empresa com filiais"
        },
        {
            "network": "10.0.0.0/16", 
            "description": "Rede interna empresa",
            "clients": "256 clientes (10.0.0.x até 10.0.255.x)",
            "use_case": "Datacenter com VLANs por departamento"
        },
        {
            "network": "**********/20",
            "description": "Rede de filiais",
            "clients": "16 clientes (172.16.0.x até 172.16.15.x)",
            "use_case": "Empresa com 16 filiais"
        },
        {
            "network": "***********/24",
            "description": "Rede local única",
            "clients": "1 cliente (192.168.1.x)",
            "use_case": "Escritório pequeno ou residencial"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['description']}")
        print(f"   📡 Rede: {example['network']}")
        print(f"   🏢 {example['clients']}")
        print(f"   💼 Caso de uso: {example['use_case']}")
    
    print("\n💡 DICAS:")
    print("• Cada sub-rede /24 vira um 'Cliente XXX' no relatório")
    print("• Excel gera abas separadas para os top 10 clientes")
    print("• Estatísticas são calculadas por cliente e globalmente")
    print("• Ideal para MSPs que gerenciam múltiplos clientes")

def main():
    """Função principal do demo"""
    
    parser = argparse.ArgumentParser(description='Demo do Network Scanner por Clientes')
    parser.add_argument('--examples', action='store_true', help='Mostrar exemplos de uso')
    parser.add_argument('--network', '-n', help='Rede para scan direto')
    parser.add_argument('--threads', '-t', type=int, default=100, help='Número de threads')
    
    args = parser.parse_args()
    
    if args.examples:
        show_client_examples()
        return
    
    if args.network:
        # Scan direto
        try:
            scanner = NetworkScanner(
                network=args.network,
                max_workers=args.threads,
                timeout=2
            )
            
            scanner.scan_network()
            
            if scanner.results:
                # Auto-export
                base_filename = f"scan_clientes_{args.network.replace('/', '_').replace('.', '_')}"
                scanner.save_results(f"{base_filename}.json")
                
                try:
                    scanner.save_to_excel(f"{base_filename}.xlsx")
                except:
                    print("💡 Para Excel: pip install openpyxl")
                    
        except Exception as e:
            print(f"❌ Erro: {e}")
    else:
        # Demo interativo
        demo_client_scan()

if __name__ == "__main__":
    main()
