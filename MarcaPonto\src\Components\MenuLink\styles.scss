@import '../../Styles/variables';
@import '../../Styles/responsive';

.link__wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
    position: relative;
    flex-direction: column;

    @include sm{
        flex-direction: row;
    }

    &:hover{
        .link__icon{
            transform: scale(1.06);
        }
    }

    .link__text {
        p {
            text-decoration: none;
            color: #252F3F;
            font-size: 11px;
        }
    }

    .link__arrow{
        flex: 1;
        justify-content: flex-end;
        display: none;
        align-items: center;

        @include sm{
            display: flex;
        }
    }

    &.link__reduced {
        padding: 10px;
        justify-content: center;
        flex-direction: column;
        align-items: center;

        .link__text {
            p {
                text-decoration: none;
                color: #222;
                font-size: 10px;
                text-align: center;
                // display: none;
            }
        }

        .link__icon{
            margin: 0;
        }

        .link__arrow{
            display: none;
        }

        .link__popover {
            visibility: hidden;
        }
    }

    @include max-xxs {
        &.link__reduced {
            .link__text {
                p {
                    display: block;
                }
            }

            .link__icon {
                margin-right: 10px;
            }
        }
    }

    @include sm {
        justify-content: flex-start;

        .link__text {
            p {
                display: block;
                font-size: 16px;
            }
        }

        .link__icon {
            margin-right: 10px;
        }
    }
}