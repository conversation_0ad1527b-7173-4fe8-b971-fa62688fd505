#!/usr/bin/env python3

"""
Exemplo de Uso - Geração de CSV para Clientes e Máquinas
Demonstra como usar a funcionalidade CSV do Network Scanner
Autor: <PERSON>
"""

import os
import sys
from datetime import datetime

# Adicionar o diretório atual ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def exemplo_basico():
    """Exemplo básico de uso"""
    print("=" * 80)
    print("📊 EXEMPLO 1: USO BÁSICO")
    print("=" * 80)

    print("Para gerar CSV com dados de clientes e máquinas:")
    print()
    print("# ✨ AUTOMÁTICO: Scanner principal gera CSVs automaticamente")
    print("python network_scanner.py -n ***********/24")
    print("# Resultado: network_scan_YYYYMMDD_HHMMSS_detalhado.csv + _resumo.csv")
    print()
    print("# Com nome personalizado")
    print("python network_scanner.py -n ***********/24 -o 'empresa_2025'")
    print("# Resultado: empresa_2025_detalhado.csv + empresa_2025_resumo.csv")
    print()
    print("# Script dedicado para controle específico:")
    print("python generate_client_csv.py -n ***********/24 -f detailed  # Só detalhado")
    print("python generate_client_csv.py -n ***********/24 -f summary   # Só resumo")
    print()

def exemplo_empresarial():
    """Exemplo para uso empresarial"""
    print("=" * 80)
    print("🏢 EXEMPLO 2: USO EMPRESARIAL")
    print("=" * 80)
    
    print("Para auditoria de TI em múltiplos clientes:")
    print()
    print("# Scan completo de toda a rede corporativa")
    print("python generate_client_csv.py -n ***********/16 -o 'auditoria_ti_2025'")
    print()
    print("# Resultado:")
    print("# - auditoria_ti_2025_detalhado.csv (todas as máquinas)")
    print("# - auditoria_ti_2025_resumo.csv (estatísticas por cliente)")
    print()
    print("# Para redes grandes, use menos threads:")
    print("python generate_client_csv.py -n 10.0.0.0/8 -t 50 --timeout 3 -o 'rede_corporativa'")
    print()

def exemplo_tactical_rmm():
    """Exemplo para Tactical RMM"""
    print("=" * 80)
    print("🔧 EXEMPLO 3: TACTICAL RMM")
    print("=" * 80)
    
    print("Para integração com Tactical RMM:")
    print()
    print("# CSV simples para Tactical RMM")
    print("python tactical_network_scanner.py ***********/24 --csv")
    print()
    print("# CSV + JSON para análise completa")
    print("python tactical_network_scanner.py ***********/24 --csv --json")
    print()
    print("# Com arquivo personalizado")
    print("python tactical_network_scanner.py ***********/24 --csv --output 'tactical_scan_cliente_001'")
    print()

def exemplo_analise_dados():
    """Exemplo de análise de dados"""
    print("=" * 80)
    print("📈 EXEMPLO 4: ANÁLISE DE DADOS")
    print("=" * 80)
    
    print("Como analisar os dados CSV gerados:")
    print()
    print("1. **Excel/LibreOffice:**")
    print("   - Abra o arquivo CSV detalhado")
    print("   - Aplique filtros automáticos")
    print("   - Filtre por 'Unidade_Cliente' para ver máquinas específicas")
    print("   - Use tabelas dinâmicas para estatísticas")
    print()
    print("2. **Python/Pandas:**")
    print("   import pandas as pd")
    print("   df = pd.read_csv('network_scan_clientes_detalhado.csv')")
    print("   print(df.groupby('Unidade_Cliente').size())")
    print()
    print("3. **Análise por Cliente:**")
    print("   - CSV detalhado: Análise técnica completa")
    print("   - CSV resumo: Relatórios executivos")
    print()

def exemplo_estrutura_dados():
    """Mostra a estrutura dos dados CSV"""
    print("=" * 80)
    print("📋 EXEMPLO 5: ESTRUTURA DOS DADOS")
    print("=" * 80)
    
    print("**CSV Detalhado (uma linha por máquina):**")
    print()
    print("Unidade_Cliente | Rede_Cliente | Total_Maquinas | IP_Maquina | Nome_Maquina")
    print("Cliente_001     | ***********/24 | 5           | ************ | PC-ADMIN")
    print("Cliente_001     | ***********/24 | 5           | ************ | PRINTER-HP")
    print("Cliente_002     | ***********/24 | 3           | ************ | SERVER-WEB")
    print()
    print("**CSV Resumo (uma linha por cliente):**")
    print()
    print("Unidade_Cliente | Rede_Cliente | Total_Maquinas | OS_Principal | Lista_IPs")
    print("Cliente_001     | ***********/24 | 5           | Windows      | ************; ************")
    print("Cliente_002     | ***********/24 | 3           | Linux        | ************; ************")
    print()

def exemplo_casos_uso():
    """Casos de uso práticos"""
    print("=" * 80)
    print("💼 EXEMPLO 6: CASOS DE USO PRÁTICOS")
    print("=" * 80)
    
    print("**1. Auditoria de Segurança:**")
    print("python generate_client_csv.py -n ***********/16 -f detailed --timeout 5")
    print("→ Análise detalhada de portas abertas e serviços")
    print()
    
    print("**2. Inventário de Ativos:**")
    print("python generate_client_csv.py -n ***********/16 -f summary -o 'inventario_2025'")
    print("→ Lista de clientes e quantidade de equipamentos")
    print()
    
    print("**3. Monitoramento de Rede:**")
    print("python generate_client_csv.py -n ***********/24 -t 200 --timeout 1")
    print("→ Scan rápido para monitoramento contínuo")
    print()
    
    print("**4. Relatório Executivo:**")
    print("python generate_client_csv.py -n ***********/16 -f summary")
    print("→ Resumo por cliente para apresentação")
    print()

def main():
    """Função principal"""
    print("🌐 NETWORK SCANNER - EXEMPLOS DE USO CSV")
    print(f"📅 Data: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("👨‍💻 Desenvolvido por: Paulo Matheus")
    print()
    
    # Executar todos os exemplos
    exemplo_basico()
    exemplo_empresarial()
    exemplo_tactical_rmm()
    exemplo_analise_dados()
    exemplo_estrutura_dados()
    exemplo_casos_uso()
    
    print("=" * 80)
    print("✅ RESUMO DOS COMANDOS PRINCIPAIS")
    print("=" * 80)
    print()
    print("# ✨ AUTOMÁTICO: Scanner principal (recomendado)")
    print("python network_scanner.py -n ***********/24")
    print("# Gera automaticamente: _detalhado.csv + _resumo.csv")
    print()
    print("# Com nome personalizado")
    print("python network_scanner.py -n ***********/16 -o 'empresa_2025'")
    print("# Gera: empresa_2025_detalhado.csv + empresa_2025_resumo.csv")
    print()
    print("# Script dedicado (para controle específico)")
    print("python generate_client_csv.py -n ***********/24")
    print()
    print("# Uso com Tactical RMM")
    print("python tactical_network_scanner.py ***********/24 --csv")
    print()
    print("📖 Para mais detalhes, consulte: CSV_EXPORT_GUIDE.md")
    print()

if __name__ == "__main__":
    main()
