[Unit]
Description=First Boot Script
After=network-online.target
Wants=network-online.target
ConditionPathExists=!/var/lib/first-boot-done

[Service]
Type=oneshot
ExecStart=/usr/local/bin/first-boot.sh
ExecStartPost=/bin/touch /var/lib/first-boot-done
ExecStartPost=/bin/systemctl disable first-boot.service
RemainAfterExit=yes
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
