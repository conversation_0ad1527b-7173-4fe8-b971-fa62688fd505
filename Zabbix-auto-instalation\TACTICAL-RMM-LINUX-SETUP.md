# Configuração no Tactical RMM - Linux

## 🐧 Guia Passo a Passo para Script Linux

### 1. Upload do Script

1. **Acesse o Tactical RMM**
2. **Vá para Scripts > Add Script**
3. **Configure os campos:**
   - **Name**: `Instalar Zabbix Agent Linux`
   - **Description**: `Instalação automatizada do Zabbix Agent para Linux`
   - **Category**: `Monitoring`
   - **Script Type**: `Shell`
   - **Shell**: `bash`

4. **Cole o conteúdo do arquivo** `Install-ZabbixAgent-Linux-Simple.sh`

### 2. Configuração dos Argumentos

#### ✅ **Configuração Básica (Recomendada)**
```bash
--server SEU_IP_AQUI
```

#### ✅ **Configuração Completa**
```bash
--server SEU_IP_AQUI --hostname NOME_PERSONALIZADO
```

### 3. Exemplos por Cenário

#### 🏢 **Cenário 1: Servidor interno**
```bash
--server *************
```
- Usa o hostname do sistema como identificação
- Ideal para redes internas

#### 🏪 **Cenário 2: Servidores com nomes padronizados**
```bash
--server ********* --hostname PROD-WEB-01
```
- Define nome específico para identificação
- Útil para organização por função/localização

#### 🌐 **Cenário 3: Servidor Zabbix externo**
```bash
--server zabbix.empresa.com.br --hostname CLIENTE-APP-01
```
- Usa FQDN em vez de IP
- Hostname personalizado para identificação

#### 🔄 **Cenário 4: Reinstalação/Atualização**
```bash
--server ************* --hostname BACKUP-SRV --force
```
- Remove instalação anterior
- Instala versão limpa

#### 🔍 **Cenário 5: Diagnóstico apenas**
```bash
--server ************* --diagnose-only
```
- Apenas verifica problemas
- Não faz alterações no sistema

### 4. Execução do Script

#### **Opção A: Execução Individual**
1. Selecione o servidor Linux
2. Vá para **Scripts**
3. Escolha **"Instalar Zabbix Agent Linux"**
4. **Modifique os argumentos** se necessário
5. Clique em **Run**

#### **Opção B: Execução em Massa**
1. Selecione múltiplos agentes Linux
2. **Bulk Actions > Run Script**
3. Escolha **"Instalar Zabbix Agent Linux"**
4. **Configure argumentos únicos** ou use padrão
5. Execute

#### **Opção C: Automação via Policy**
1. Crie uma **Policy** para Linux
2. Adicione **Script Check**
3. Configure para executar em novos agentes
4. Define argumentos padrão

### 5. Monitoramento da Execução

#### **Durante a execução:**
- ✅ **Status**: Running
- 📊 **Logs**: Acompanhe em tempo real
- ⏱️ **Tempo**: ~1-3 minutos (dependendo da conexão)

#### **Códigos de retorno:**
- **0**: ✅ Sucesso - Instalação concluída
- **1**: ❌ Erro - Verificar logs para detalhes

#### **Logs típicos de sucesso:**
```
[2025-01-12 10:30:15] [SUCCESS] === INSTALACAO DO ZABBIX AGENT PARA LINUX ===
[2025-01-12 10:30:16] [INFO] Arquitetura detectada: amd64
[2025-01-12 10:30:17] [SUCCESS] Download concluido com wget
[2025-01-12 10:30:25] [SUCCESS] Zabbix Agent instalado com sucesso!
[2025-01-12 10:30:26] [SUCCESS] Configuracao aplicada com sucesso!
[2025-01-12 10:30:27] [SUCCESS] Servico iniciado com sucesso!
[2025-01-12 10:30:28] [SUCCESS] === INSTALACAO CONCLUIDA COM SUCESSO! ===
```

### 6. Verificação Pós-Instalação

#### **Via Tactical RMM:**
1. Execute comando de verificação:
```bash
systemctl status zabbix-agent
```

2. Teste de conectividade:
```bash
nc -z SEU_IP_ZABBIX 10051
```

#### **No servidor Zabbix:**
1. Verifique se o host aparece em **Configuration > Hosts**
2. Confirme status **"Available"**
3. Teste itens de monitoramento

### 7. Configurações Avançadas

#### **Argumentos opcionais:**

| Parâmetro | Uso | Exemplo |
|-----------|-----|---------|
| `--version` | Versão específica | `--version 7.0.9` |
| `--install-path` | Caminho personalizado | `--install-path /usr/local/zabbix` |
| `--config-path` | Config personalizado | `--config-path /etc/zabbix-custom` |
| `--force` | Forçar reinstalação | `--force` |
| `--local-tar` | Arquivo local | `--local-tar /tmp/zabbix.tar.gz` |

#### **Exemplo completo:**
```bash
--server ************* --hostname PROD-DB-01 --version 7.0.10 --force
```

### 8. Troubleshooting Comum

#### **❌ Erro: "Permission denied"**
- **Causa**: Script não executado como root
- **Solução**: Tactical RMM executa como root automaticamente

#### **❌ Erro: "Package not found"**
- **Causa**: Dependências não instaladas
- **Solução**: Script instala automaticamente

#### **❌ Erro: "Service failed to start"**
- **Causa**: Configuração de rede
- **Solução**: Verificar IP do servidor

#### **❌ Erro: "Download failed"**
- **Causa**: Sem internet ou proxy
- **Solução**: Usar `--local-tar` com arquivo baixado

### 9. Distribuições Suportadas

#### **✅ Testadas e funcionais:**
- **Ubuntu**: 18.04, 20.04, 22.04, 24.04
- **Debian**: 10, 11, 12
- **CentOS**: 7, 8
- **RHEL**: 8, 9
- **Rocky Linux**: 8, 9
- **AlmaLinux**: 8, 9
- **Fedora**: 35+

#### **⚠️ Limitações:**
- Requer **systemd** (maioria das distribuições modernas)
- Precisa de **root/sudo** (Tactical RMM fornece automaticamente)

### 10. Scripts Auxiliares

#### **Verificação rápida:**
```bash
#!/bin/bash
# Adicione como script separado no Tactical RMM
if systemctl is-active --quiet zabbix-agent; then
    echo "Status: $(systemctl is-active zabbix-agent)"
    echo "Enabled: $(systemctl is-enabled zabbix-agent)"
    if [ -f /etc/zabbix/zabbix_agentd.conf ]; then
        server=$(grep "^Server=" /etc/zabbix/zabbix_agentd.conf | cut -d= -f2)
        hostname=$(grep "^Hostname=" /etc/zabbix/zabbix_agentd.conf | cut -d= -f2)
        echo "Servidor: $server"
        echo "Hostname: $hostname"
    fi
else
    echo "Zabbix Agent nao esta rodando"
fi
```

#### **Desinstalação:**
```bash
#!/bin/bash
# Script para remover Zabbix Agent
systemctl stop zabbix-agent 2>/dev/null
systemctl disable zabbix-agent 2>/dev/null
rm -f /etc/systemd/system/zabbix-agent.service
systemctl daemon-reload
rm -rf /opt/zabbix
rm -rf /etc/zabbix
rm -rf /var/log/zabbix
rm -rf /var/run/zabbix
userdel zabbix 2>/dev/null
echo "Zabbix Agent removido"
```

### 11. Boas Práticas

#### **✅ Nomenclatura de Hosts:**
- Use padrões consistentes: `AMBIENTE-FUNCAO-NUMERO`
- Exemplos: `PROD-WEB-01`, `DEV-DB-02`, `BACKUP-SRV-01`

#### **✅ Organização:**
- Crie grupos no Zabbix por função/localização
- Use templates específicos por tipo de servidor

#### **✅ Monitoramento:**
- Configure alertas para agentes offline
- Monitore logs de instalação

#### **✅ Manutenção:**
- Execute verificações periódicas
- Mantenha versões atualizadas

### 12. Exemplo de Política Automatizada

```bash
# Para novos servidores Linux automaticamente
--server ************* --hostname $(hostname)-auto --force
```

---

**💡 Dica**: Teste primeiro em um servidor de desenvolvimento antes de aplicar em produção.

**📞 Suporte**: Para dúvidas, consulte os logs detalhados do script ou execute comandos de diagnóstico.
