﻿# Caminho do compartilhamento de arquivos
$folder = "D:\Compartilhamento"

# Regex para detectar arquivos duplicados baseados em sufixo
$pattern = "^(?<baseName>.+)-[^\\]+(?<ext>\.\w+)$"

# Agrupamento de arquivos por base
$arquivosAgrupados = @{}

# Busca todos os arquivos da pasta e subpastas
Get-ChildItem -Path $folder -Recurse -File | ForEach-Object {
    $file = $_
    $name = $file.Name

    if ($name -match $pattern) {
        $baseName = "$($matches['baseName'])$($matches['ext'])"
        if (-not $arquivosAgrupados.ContainsKey($baseName)) {
            $arquivosAgrupados[$baseName] = @()
        }
        $arquivosAgrupados[$baseName] += $file
    }
}

# Processamento dos arquivos
foreach ($baseName in $arquivosAgrupados.Keys) {
    $arquivos = $arquivosAgrupados[$baseName]

    # Incluir o arquivo original (sem sufixo) se existir
    $original = Get-ChildItem -Path $folder -Recurse -File -Filter $baseName -ErrorAction SilentlyContinue
    if ($original) {
        $arquivos += $original
    }

    # Ordenar por data de modificação (mais recente primeiro)
    $maisRecente = $arquivos | Sort-Object LastWriteTime -Descending | Select-Object -First 1

    Write-Host "`n🔐 Mantendo: $($maisRecente.FullName)" -ForegroundColor Green

    # Excluir os demais
    $paraExcluir = $arquivos | Where-Object { $_.FullName -ne $maisRecente.FullName }

    foreach ($arquivo in $paraExcluir) {
        try {
            Remove-Item -Path $arquivo.FullName -Force
            Write-Host "❌ Removido: $($arquivo.FullName)" -ForegroundColor Red
        } catch {
            Write-Warning "Erro ao remover $($arquivo.FullName): $($_.Exception.Message)"
        }
    }

    # Se o nome do mais recente ainda estiver com sufixo, renomear
    $nomeAtual = $maisRecente.Name
    if ($nomeAtual -match $pattern) {
        $novoNome = "$($matches['baseName'])$($matches['ext'])"
        $novoCaminho = Join-Path -Path $maisRecente.DirectoryName -ChildPath $novoNome

        # Evitar sobrescrever se já existir um com o nome destino
        if (-not (Test-Path $novoCaminho)) {
            try {
                Rename-Item -Path $maisRecente.FullName -NewName $novoNome
                Write-Host "✏️ Renomeado para: $novoNome" -ForegroundColor Cyan
            } catch {
                Write-Warning "Erro ao renomear $($maisRecente.FullName): $($_.Exception.Message)"
            }
        } else {
            Write-Warning "⚠️ Arquivo com nome final já existe: $novoCaminho. Não renomeado."
        }
    }
}