# Script Otimizado para Tactical RMM - Linux

## 🎯 Solução para Problemas de Argumentos

Este é o **script otimizado** criado especificamente para resolver problemas de processamento de argumentos no Tactical RMM Linux.

### ❌ Problema Original
```
[ERROR] Opcao desconhecida: --server **********
```

### ✅ Solução Implementada
- **Processamento flexível de argumentos** (múltiplos métodos)
- **Suporte a variáveis de ambiente** como fallback
- **Parsing inteligente** de strings de argumentos
- **Debug integrado** para troubleshooting
- **Compatibilidade total** com Tactical RMM

## 📁 Arquivo Principal
**`Install-ZabbixAgent-Linux-TacticalRMM.sh`**

## 🚀 Como usar no Tactical RMM

### **Método 1: Argumentos tradicionais**
```bash
--server **********
```

### **Método 2: Com hostname personalizado**
```bash
--server ********** --hostname SERVIDOR-LINUX-01
```

### **Método 3: Com força (reinstalação)**
```bash
--server ********** --hostname SERVIDOR-LINUX-01 --force
```

### **Método 4: Variáveis de ambiente (alternativa)**
Se os argumentos não funcionarem, configure como variáveis de ambiente no Tactical RMM:
```bash
ZABBIX_SERVER_IP=**********
ZABBIX_HOSTNAME=SERVIDOR-01
```

## 🔧 Funcionalidades do Script Otimizado

### **1. Processamento Inteligente de Argumentos**
- ✅ **Argumentos tradicionais**: `--server IP --hostname NOME`
- ✅ **String única**: Processa argumentos em uma string
- ✅ **Variáveis de ambiente**: Fallback automático
- ✅ **Debug integrado**: Mostra como argumentos foram recebidos

### **2. Instalação Simplificada**
- ✅ **Detecção automática** de arquitetura (AMD64/ARM64)
- ✅ **Instalação de dependências** automática
- ✅ **Limpeza de instalações anteriores** com `--force`
- ✅ **Configuração completa** em uma execução

### **3. Compatibilidade Total**
- ✅ **Ubuntu/Debian** (apt-get)
- ✅ **CentOS/RHEL/Rocky/AlmaLinux** (yum/dnf)
- ✅ **Fedora** (dnf)
- ✅ **Qualquer distribuição** com systemd

### **4. Configuração Automática**
- ✅ **Usuário zabbix** criado automaticamente
- ✅ **Serviço systemd** configurado
- ✅ **Firewall** configurado (UFW/firewalld/iptables)
- ✅ **Permissões** adequadas definidas

## 📊 Parâmetros Suportados

| Parâmetro | Descrição | Exemplo |
|-----------|-----------|---------|
| `--server` | IP do servidor Zabbix | `--server **********` |
| `--hostname` | Nome do host | `--hostname SERVIDOR-01` |
| `--force` | Força reinstalação | `--force` |
| `--diagnose-only` | Apenas diagnóstico | `--diagnose-only` |

## 🔍 Variáveis de Ambiente (Alternativa)

Se os argumentos não funcionarem no Tactical RMM, use variáveis de ambiente:

| Variável | Descrição | Exemplo |
|----------|-----------|---------|
| `ZABBIX_SERVER_IP` | IP do servidor Zabbix | `**********` |
| `ZABBIX_HOSTNAME` | Nome do host | `SERVIDOR-01` |

## 🎮 Modos de Operação

### **1. Instalação Básica**
```bash
--server **********
```
- Usa hostname do sistema
- Instalação padrão

### **2. Instalação com Nome Personalizado**
```bash
--server ********** --hostname PROD-WEB-01
```
- Define nome específico
- Ideal para organização

### **3. Reinstalação Forçada**
```bash
--server ********** --hostname PROD-WEB-01 --force
```
- Remove instalação anterior
- Instalação limpa

### **4. Diagnóstico Apenas**
```bash
--server ********** --diagnose-only
```
- Verifica sistema
- Não instala nada

## 📁 Arquivos Criados

- **Binário**: `/opt/zabbix/zabbix_agentd`
- **Configuração**: `/etc/zabbix/zabbix_agentd.conf`
- **Logs**: `/var/log/zabbix/zabbix_agentd.log`
- **Serviço**: `/etc/systemd/system/zabbix-agent.service`
- **PID**: `/var/run/zabbix/zabbix_agentd.pid`

## 🔍 Debug e Troubleshooting

### **O script mostra debug automático:**
```
[INFO] Debug: Numero de argumentos recebidos: 2
[INFO] Debug: Argumento 1: '--server'
[INFO] Debug: Argumento 2: '**********'
```

### **Comandos de verificação:**
```bash
# Verificar status
systemctl status zabbix-agent

# Ver logs
tail -f /var/log/zabbix/zabbix_agentd.log

# Verificar configuração
cat /etc/zabbix/zabbix_agentd.conf

# Testar conectividade
nc -z ********** 10051
```

## 🚨 Resolução de Problemas

### **Problema: Argumentos não reconhecidos**
**Solução 1**: Use variáveis de ambiente no Tactical RMM
```bash
# No campo Environment Variables do Tactical RMM:
ZABBIX_SERVER_IP=**********
ZABBIX_HOSTNAME=SERVIDOR-01
```

**Solução 2**: Verifique os logs de debug do script

### **Problema: Erro de download/extração**
**Sintomas**:
```
tar: Cannot open: No such file or directory
tar: Child returned status 2
```

**Soluções**:
1. **Teste conectividade**: Execute `Test-Download-URL.sh` para verificar URLs
2. **Verifique proxy/firewall**: Pode estar bloqueando download
3. **Use instalação offline**: Baixe manualmente e use `--local-tar`

**Comandos de diagnóstico**:
```bash
# Testar conectividade
curl -I https://cdn.zabbix.com/zabbix/binaries/stable/7.0/7.0.10/zabbix_agent-7.0.10-linux-3.0-amd64-static.tar.gz

# Testar download manual
wget https://cdn.zabbix.com/zabbix/binaries/stable/7.0/7.0.10/zabbix_agent-7.0.10-linux-3.0-amd64-static.tar.gz
```

### **Problema: Serviço não inicia**
**Solução**: Use `--force` para limpeza completa
```bash
--server ********** --force
```

### **Problema: Sem conectividade**
**Verificações**:
1. Testar ping: `ping **********`
2. Testar porta: `nc -z ********** 10051`
3. Verificar firewall local

## ✅ Vantagens do Script Otimizado

1. **✅ Compatibilidade total** com Tactical RMM
2. **✅ Múltiplos métodos** de passagem de argumentos
3. **✅ Debug integrado** para troubleshooting
4. **✅ Fallback automático** para variáveis de ambiente
5. **✅ Instalação robusta** e confiável
6. **✅ Configuração completa** automática

## 🎯 Recomendação para Tactical RMM

### **Use este comando:**
```bash
--server ********** --hostname $(hostname)-zabbix --force
```

### **Ou configure variáveis de ambiente:**
```
ZABBIX_SERVER_IP=**********
ZABBIX_HOSTNAME=SERVIDOR-PERSONALIZADO
```

## 📋 Checklist de Instalação

- [ ] ✅ **Script executado como root** (Tactical RMM faz automaticamente)
- [ ] ✅ **Servidor Zabbix especificado** (obrigatório)
- [ ] ✅ **Conectividade de rede** verificada
- [ ] ✅ **Firewall configurado** (script faz automaticamente)
- [ ] ✅ **Serviço iniciado** (script faz automaticamente)

## 🔄 Próximos Passos

1. **Substitua** o script anterior por `Install-ZabbixAgent-Linux-TacticalRMM.sh`
2. **Execute** com o comando:
   ```bash
   --server ********** --hostname NOME-DO-SERVIDOR
   ```
3. **Monitore** os logs para verificar o sucesso da instalação
4. **Verifique** no servidor Zabbix se o host apareceu

## 📦 URLs Atualizadas (Repositório Oficial Zabbix)

### **URLs Principais (Pacotes Nativos):**
- **Ubuntu AMD64**: `https://repo.zabbix.com/zabbix/7.0/ubuntu/pool/main/z/zabbix/zabbix-agent_7.0.10-1+ubuntu22.04_amd64.deb`
- **Ubuntu ARM64**: `https://repo.zabbix.com/zabbix/7.0/ubuntu/pool/main/z/zabbix/zabbix-agent_7.0.10-1+ubuntu22.04_arm64.deb`

### **URLs Alternativas:**
- **Debian DEB**: Para sistemas Debian 11/12
- **RHEL/CentOS RPM**: Para sistemas Red Hat/CentOS 8/9
- **Fallback automático**: Script tenta múltiplas URLs automaticamente

### **Tipos de Pacote Suportados:**
- ✅ **DEB** (Ubuntu/Debian) - Instalação nativa com dpkg
- ✅ **RPM** (RHEL/CentOS/Fedora) - Instalação nativa com rpm/yum/dnf
- ✅ **TAR.GZ** (Binários estáticos) - Compatibilidade com versão anterior

### **Vantagens das novas URLs:**
- ✅ **Repositório oficial** Zabbix
- ✅ **Pacotes nativos** (melhor integração com sistema)
- ✅ **Múltiplas alternativas** para alta disponibilidade
- ✅ **Suporte ARM64** completo
- ✅ **Detecção automática** do tipo de pacote

---

**💡 Este script resolve definitivamente os problemas de argumentos no Tactical RMM Linux!**
