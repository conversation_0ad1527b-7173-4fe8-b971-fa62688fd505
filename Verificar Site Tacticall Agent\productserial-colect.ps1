# Script para extrair chave do Windows e salvar em Custom Field - Tactical RMM
# Versao: 2.0 - FUNCIONAL
# Data: 2025-07-01

function Get-WindowsKey {
    try {
        $hklm = 2147483650
        $key = "SOFTWARE\Microsoft\Windows NT\CurrentVersion"
        $value = "DigitalProductId"

        $reg = [Microsoft.Win32.RegistryKey]::OpenRemoteBaseKey($hklm, $env:COMPUTERNAME)
        $regKey = $reg.OpenSubKey($key)

        if (-not $regKey) {
            return "ERRO: Não foi possível acessar o registro"
        }

        $digitalProductId = $regKey.GetValue($value)

        if (-not $digitalProductId) {
            return "ERRO: DigitalProductId não encontrado"
        }

        $keyChars = "BCDFGHJKMPQRTVWXY2346789"
        $decodedKey = ""

        $keyOffset = 52
        $i = 24
        do {
            $current = 0
            $j = 14
            do {
                $current = $current * 256 -bxor $digitalProductId[$j + $keyOffset]
                $digitalProductId[$j + $keyOffset] = [math]::Floor($current / 24)
                $current = $current % 24
                $j--
            } while ($j -ge 0)
            $decodedKey = $keyChars[$current] + $decodedKey
            $i--
            if (($i + 1) % 6 -eq 0 -and $i -ne -1) {
                $decodedKey = "-" + $decodedKey
            }
        } while ($i -ge 0)

        return $decodedKey

    } catch {
        return "ERRO: $($_.Exception.Message)"
    } finally {
        if ($regKey) { $regKey.Close() }
        if ($reg) { $reg.Close() }
    }
}

# Configurações da API do Tactical RMM
$apiUrl = "https://api.centralmesh.nvirtual.com.br"
$apiToken = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"

# Obter a chave do Windows
$windowsKey = Get-WindowsKey

# Saída para logs do Tactical RMM
Write-Output "Windows Product Key: $windowsKey"
Write-Host "Computador: $env:COMPUTERNAME"
Write-Host "Chave extraída em: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

# Investigar a estrutura atual do agente e Custom Fields
if ($apiToken -and $windowsKey -and $windowsKey -notlike "ERRO:*") {
    try {
        # Obter o Agent ID atual
        $hostname = $env:COMPUTERNAME

        # Buscar o agente atual
        $agents = Invoke-RestMethod -Uri "$apiUrl/agents/" -Headers @{
            "X-API-KEY" = $apiToken
        } -TimeoutSec 30

        $currentAgent = $agents | Where-Object { $_.hostname -eq $hostname } | Select-Object -First 1

        if ($currentAgent) {
            $agentId = $currentAgent.agent_id

            Write-Host "🔍 Agente encontrado: $agentId"
            Write-Host "� Custom Fields atuais:"

            # Mostrar custom fields existentes
            if ($currentAgent.custom_fields) {
                $currentAgent.custom_fields | ForEach-Object {
                    Write-Host "   - Campo ID: $($_.id), Field: $($_.field), Valor: $($_.value)"
                }
            } else {
                Write-Host "   - Nenhum Custom Field encontrado"
            }

            # Tentar buscar informações sobre Custom Fields disponíveis
            try {
                Write-Host "🔍 Buscando Custom Fields disponíveis..."
                $customFields = Invoke-RestMethod -Uri "$apiUrl/core/customfields/" -Headers @{
                    "X-API-KEY" = $apiToken
                } -TimeoutSec 30

                Write-Host "📝 Custom Fields disponíveis no sistema:"
                $customFields | ForEach-Object {
                    Write-Host "   - ID: $($_.id), Nome: $($_.name), Modelo: $($_.model)"
                }

                # Procurar o campo "Serial Windows"
                $serialWindowsField = $customFields | Where-Object { $_.name -eq "Serial Windows" }

                if ($serialWindowsField) {
                    Write-Host "✅ Campo 'Serial Windows' encontrado! ID: $($serialWindowsField.id)"

                    # Tentar atualizar usando o ID correto do campo
                    $customFieldUpdate = @{
                        "custom_fields" = @(
                            @{
                                "field" = $serialWindowsField.id
                                "value" = $windowsKey
                            }
                        )
                    } | ConvertTo-Json -Depth 3

                    Write-Host "🔄 Atualizando Custom Field com ID correto..."
                    $null = Invoke-RestMethod -Method PUT -Uri "$apiUrl/agents/$agentId/" -Headers @{
                        "X-API-KEY" = $apiToken
                        "Content-Type" = "application/json"
                    } -Body $customFieldUpdate -TimeoutSec 30

                    Write-Host "✅ Custom Field 'Serial Windows' atualizado com sucesso!"

                } else {
                    Write-Host "❌ Campo 'Serial Windows' NÃO encontrado!"
                    Write-Host "   Você precisa criar o Custom Field no Tactical RMM primeiro:"
                    Write-Host "   1. Vá em Settings → Global Settings → Custom Fields"
                    Write-Host "   2. Clique em 'Add Custom Field'"
                    Write-Host "   3. Nome: 'Serial Windows', Tipo: 'Text', Modelo: 'Agent'"
                }

            } catch {
                Write-Host "⚠️ Não foi possível buscar Custom Fields: $($_.Exception.Message)"
            }

        } else {
            Write-Host "⚠️ Agente não encontrado na API"
        }

    } catch {
        Write-Host "⚠️ Erro geral: $($_.Exception.Message)"
    }
} else {
    Write-Host "⚠️ Token da API não configurado ou erro na extração da chave"
}
