# Script de Instalação do Zabbix Agent 2 para Windows
# Compatível com Tactical RMM
# Autor: <PERSON> - NVirtual
# Data: 2025-01-12
# Versão: 1.2

<#
.SYNOPSIS
    Instala e configura o Zabbix Agent 2 no Windows

.DESCRIPTION
    Este script automatiza a instalação do Zabbix Agent 2 no Windows,
    compatível com execução via Tactical RMM.

.PARAMETER ZabbixServerIP
    [OBRIGATÓRIO] IP ou hostname do servidor Zabbix
    Exemplo: "*************" ou "zabbix.empresa.com"

.PARAMETER AgentHostname
    [OPCIONAL] Nome do host para identificação no Zabbix
    Se não informado, usa o nome do computador atual
    Exemplo: "SERVIDOR-01" ou "WORKSTATION-VENDAS"

.PARAMETER ZabbixVersion
    [OPCIONAL] Versão do Zabbix Agent 2 para instalar
    Padrão: "7.0.6"

.PARAMETER InstallPath
    [OPCIONAL] Caminho de instalação
    Padrão: "C:\Program Files\Zabbix Agent 2"

.PARAMETER Force
    [OPCIONAL] Força a remoção de instalações existentes
    Use quando houver conflitos ou para reinstalar

.PARAMETER LocalMSIPath
    [OPCIONAL] Caminho para arquivo MSI local para instalação offline
    Use quando houver problemas de conectividade ou proxy
    Exemplo: "C:\temp\zabbix_agent2-7.0.6-windows-amd64-openssl.msi"

.PARAMETER DiagnoseOnly
    [OPCIONAL] Apenas executa diagnóstico sem instalar
    Útil para verificar problemas antes da instalação

.EXAMPLE
    # Uso básico (apenas IP do servidor)
    .\Install-ZabbixAgent2-Windows.ps1 -ZabbixServerIP "*************"

.EXAMPLE
    # Definindo IP do servidor e nome do host
    .\Install-ZabbixAgent2-Windows.ps1 -ZabbixServerIP "*************" -AgentHostname "SERVIDOR-VENDAS"

.EXAMPLE
    # Instalação com correção automática de problemas
    .\Install-ZabbixAgent2-Windows.ps1 -ZabbixServerIP "*************" -AgentHostname "SERVIDOR-VENDAS" -Force

.EXAMPLE
    # Apenas diagnóstico (sem instalar)
    .\Install-ZabbixAgent2-Windows.ps1 -ZabbixServerIP "*************" -DiagnoseOnly

.EXAMPLE
    # Instalação offline com MSI local
    .\Install-ZabbixAgent2-Windows.ps1 -ZabbixServerIP "*************" -LocalMSIPath "C:\temp\zabbix_agent2.msi"

.EXAMPLE
    # Para uso no Tactical RMM (Script Arguments):
    -ZabbixServerIP "*************" -AgentHostname "NOME-PERSONALIZADO"

.EXAMPLE
    # Para uso no Tactical RMM com correção de problemas:
    -ZabbixServerIP "*************" -AgentHostname "NOME-PERSONALIZADO" -Force

.EXAMPLE
    # Para uso no Tactical RMM apenas diagnóstico:
    -ZabbixServerIP "*************" -DiagnoseOnly

.NOTES
    Para usar no Tactical RMM:
    1. Faça upload deste script
    2. Em "Script Arguments" digite:
       -ZabbixServerIP "SEU_IP_ZABBIX" -AgentHostname "NOME_DO_HOST"
    3. Execute o script

    DIAGNÓSTICO INTEGRADO:
    - Use -DiagnoseOnly para apenas verificar problemas
    - Use -Force para correção automática de problemas (erro 1603, etc.)
    - O script detecta e corrige automaticamente:
      * Instalações anteriores corrompidas
      * Serviços e processos do Zabbix rodando
      * Problemas de SSL/TLS
      * Conflitos de arquivos e diretórios

    RESOLUÇÃO DE PROBLEMAS:
    - Erro SSL/TLS: Use -LocalMSIPath com MSI baixado manualmente
    - Erro 1603: Use -Force para limpeza automática
    - Problemas de conectividade: Use -DiagnoseOnly primeiro
#>

param(
    [Parameter(Mandatory=$true, HelpMessage="IP ou hostname do servidor Zabbix")]
    [ValidateNotNullOrEmpty()]
    [string]$ZabbixServerIP,

    [Parameter(Mandatory=$false, HelpMessage="Nome do host para identificação no Zabbix")]
    [ValidateNotNullOrEmpty()]
    [string]$AgentHostname = $env:COMPUTERNAME,

    [Parameter(Mandatory=$false)]
    [string]$ZabbixVersion = "7.0.6",

    [Parameter(Mandatory=$false)]
    [string]$InstallPath = "C:\Program Files\Zabbix Agent 2",

    [Parameter(Mandatory=$false)]
    [switch]$Force,

    [Parameter(Mandatory=$false, HelpMessage="Caminho para arquivo MSI local (para instalação offline)")]
    [string]$LocalMSIPath,

    [Parameter(Mandatory=$false, HelpMessage="Apenas diagnosticar problemas sem instalar")]
    [switch]$DiagnoseOnly
)

# Configurações
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# URLs de download (atualizadas para versão 7.0.6)
$DownloadURL64 = "https://cdn.zabbix.com/zabbix/binaries/stable/7.0/7.0.6/zabbix_agent2-7.0.6-windows-amd64-openssl.msi"
$DownloadURL32 = "https://cdn.zabbix.com/zabbix/binaries/stable/7.0/7.0.6/zabbix_agent2-7.0.6-windows-i386-openssl.msi"

# URLs alternativas para fallback
$AlternativeURL64 = @(
    "https://repo.zabbix.com/zabbix/7.0/windows/zabbix_agent2-7.0.6-windows-amd64-openssl.msi",
    "https://sourceforge.net/projects/zabbix/files/ZABBIX%20Latest%20Stable/7.0.6/zabbix_agent2-7.0.6-windows-amd64-openssl.msi/download"
)
$AlternativeURL32 = @(
    "https://repo.zabbix.com/zabbix/7.0/windows/zabbix_agent2-7.0.6-windows-i386-openssl.msi",
    "https://sourceforge.net/projects/zabbix/files/ZABBIX%20Latest%20Stable/7.0.6/zabbix_agent2-7.0.6-windows-i386-openssl.msi/download"
)

# Função para log com timestamp
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch($Level) {
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

# Função para verificar se é administrador
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Função para configurar protocolos SSL/TLS
function Initialize-SecurityProtocols {
    Write-Log "Configurando protocolos de segurança..."

    try {
        # Verificar versão do PowerShell
        $psVersion = $PSVersionTable.PSVersion.Major
        Write-Log "PowerShell versão $psVersion"

        if ($psVersion -lt 3) {
            Write-Log "PowerShell versão antiga detectada. Algumas funcionalidades podem ser limitadas." -Level "WARNING"
        }

        # Verificar protocolos disponíveis
        $availableProtocols = [Enum]::GetNames([Net.SecurityProtocolType])
        Write-Log "Protocolos SSL/TLS disponíveis - $($availableProtocols -join ', ')"

        # Configurar protocolos baseado na versão do .NET Framework
        if ([Net.SecurityProtocolType].GetEnumNames() -contains "Tls12") {
            [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12 -bor [Net.SecurityProtocolType]::Tls11 -bor [Net.SecurityProtocolType]::Tls
            Write-Log "TLS 1.2, 1.1 e 1.0 habilitados" -Level "SUCCESS"
        } else {
            [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls11 -bor [Net.SecurityProtocolType]::Tls
            Write-Log "TLS 1.1 e 1.0 habilitados (TLS 1.2 não disponível)" -Level "WARNING"
        }

        # Verificar configuração atual
        Write-Log "Protocolo atual - $([Net.ServicePointManager]::SecurityProtocol)"

        Write-Log "Protocolos SSL/TLS configurados com sucesso" -Level "SUCCESS"
    }
    catch {
        Write-Log "Erro ao configurar protocolos SSL/TLS: $($_.Exception.Message)" -Level "WARNING"
    }
}

# Função para diagnóstico completo do sistema
function Invoke-SystemDiagnosis {
    param([bool]$FixIssues = $false)

    Write-Log "=== DIAGNÓSTICO DO SISTEMA ===" -Level "SUCCESS"
    $issuesFound = 0

    # 1. Verificar privilégios de administrador
    Write-Log "1. Verificando privilégios de administrador..."
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    $isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

    if ($isAdmin) {
        Write-Log "[OK] Executando como Administrador" -Level "SUCCESS"
    } else {
        Write-Log "[ERRO] NÃO está executando como Administrador" -Level "ERROR"
        Write-Log "SOLUÇÃO: Execute o PowerShell como Administrador" -Level "WARNING"
        $issuesFound++
    }

    # 2. Verificar serviços do Zabbix
    Write-Log "2. Verificando serviços do Zabbix existentes..."
    $zabbixServices = Get-Service | Where-Object { $_.Name -like "*Zabbix*" }
    if ($zabbixServices) {
        Write-Log "[AVISO] Encontrados serviços do Zabbix:" -Level "WARNING"
        foreach ($service in $zabbixServices) {
            Write-Log "  - $($service.Name) - Status $($service.Status)" -Level "WARNING"
        }
        $issuesFound++

        if ($FixIssues) {
            Write-Log "Corrigindo: Parando e removendo serviços..." -Level "INFO"
            foreach ($service in $zabbixServices) {
                try {
                    if ($service.Status -eq "Running") {
                        Stop-Service -Name $service.Name -Force -TimeoutSec 30 -ErrorAction SilentlyContinue
                    }
                    & sc.exe delete $service.Name 2>&1 | Out-Null
                    Write-Log "Serviço $($service.Name) removido" -Level "SUCCESS"
                }
                catch {
                    Write-Log "Erro ao remover $($service.Name): $($_.Exception.Message)" -Level "WARNING"
                }
            }
        }
    } else {
        Write-Log "[OK] Nenhum serviço do Zabbix encontrado" -Level "SUCCESS"
    }

    # 3. Verificar processos do Zabbix
    Write-Log "3. Verificando processos do Zabbix..."
    $zabbixProcesses = Get-Process | Where-Object { $_.ProcessName -like "*zabbix*" }
    if ($zabbixProcesses) {
        Write-Log "[AVISO] Encontrados processos do Zabbix rodando:" -Level "WARNING"
        foreach ($proc in $zabbixProcesses) {
            Write-Log "  - $($proc.ProcessName) - ID $($proc.Id)" -Level "WARNING"
        }
        $issuesFound++

        if ($FixIssues) {
            Write-Log "Corrigindo: Terminando processos..." -Level "INFO"
            foreach ($proc in $zabbixProcesses) {
                try {
                    Stop-Process -Id $proc.Id -Force
                    Write-Log "Processo $($proc.ProcessName) terminado" -Level "SUCCESS"
                }
                catch {
                    Write-Log "Erro ao terminar $($proc.ProcessName): $($_.Exception.Message)" -Level "WARNING"
                }
            }
        }
    } else {
        Write-Log "[OK] Nenhum processo do Zabbix rodando" -Level "SUCCESS"
    }

    # 4. Verificar instalações MSI existentes
    Write-Log "4. Verificando instalações MSI do Zabbix..."
    try {
        $zabbixProducts = Get-WmiObject -Class Win32_Product -Filter "Name LIKE '%Zabbix%'" -ErrorAction SilentlyContinue
        if ($zabbixProducts) {
            Write-Log "[AVISO] Encontradas instalações MSI do Zabbix:" -Level "WARNING"
            foreach ($product in $zabbixProducts) {
                Write-Log "  - $($product.Name) - Versão $($product.Version)" -Level "WARNING"
            }
            $issuesFound++

            if ($FixIssues) {
                Write-Log "Corrigindo: Removendo instalações MSI..." -Level "INFO"
                foreach ($product in $zabbixProducts) {
                    try {
                        $result = $product.Uninstall()
                        if ($result.ReturnValue -eq 0) {
                            Write-Log "Produto $($product.Name) removido" -Level "SUCCESS"
                        } else {
                            Write-Log "Erro ao remover $($product.Name): Código $($result.ReturnValue)" -Level "WARNING"
                        }
                    }
                    catch {
                        Write-Log "Erro ao remover $($product.Name): $($_.Exception.Message)" -Level "WARNING"
                    }
                }
            }
        } else {
            Write-Log "[OK] Nenhuma instalação MSI do Zabbix encontrada" -Level "SUCCESS"
        }
    }
    catch {
        Write-Log "Erro ao consultar produtos MSI: $($_.Exception.Message)" -Level "WARNING"
    }

    # 5. Verificar diretórios do Zabbix
    Write-Log "5. Verificando diretórios do Zabbix..."
    $zabbixDirs = @(
        "C:\Program Files\Zabbix Agent",
        "C:\Program Files\Zabbix Agent 2",
        "C:\Program Files (x86)\Zabbix Agent",
        "C:\Program Files (x86)\Zabbix Agent 2"
    )

    $foundDirs = @()
    foreach ($dir in $zabbixDirs) {
        if (Test-Path $dir) {
            $foundDirs += $dir
            Write-Log "[AVISO] Diretório encontrado: $dir" -Level "WARNING"
        }
    }

    if ($foundDirs.Count -eq 0) {
        Write-Log "[OK] Nenhum diretório do Zabbix encontrado" -Level "SUCCESS"
    } else {
        $issuesFound++
        if ($FixIssues) {
            Write-Log "Corrigindo: Removendo diretórios..." -Level "INFO"
            foreach ($dir in $foundDirs) {
                try {
                    Remove-Item -Path $dir -Recurse -Force -ErrorAction Stop
                    Write-Log "Diretório $dir removido" -Level "SUCCESS"
                }
                catch {
                    Write-Log "Erro ao remover $dir : $($_.Exception.Message)" -Level "WARNING"
                }
            }
        }
    }

    # 6. Verificar Windows Installer Service
    Write-Log "6. Verificando Windows Installer Service..."
    $msiService = Get-Service -Name "msiserver" -ErrorAction SilentlyContinue
    if ($msiService) {
        if ($msiService.Status -ne "Running") {
            Write-Log "[AVISO] Windows Installer não está rodando" -Level "WARNING"
            $issuesFound++
            if ($FixIssues) {
                try {
                    Start-Service -Name "msiserver"
                    Write-Log "Windows Installer iniciado" -Level "SUCCESS"
                }
                catch {
                    Write-Log "Erro ao iniciar Windows Installer: $($_.Exception.Message)" -Level "ERROR"
                }
            }
        } else {
            Write-Log "[OK] Windows Installer está rodando" -Level "SUCCESS"
        }
    } else {
        Write-Log "[ERRO] Windows Installer Service não encontrado" -Level "ERROR"
        $issuesFound++
    }

    # 7. Verificar espaço em disco
    Write-Log "7. Verificando espaço em disco..."
    $drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }
    foreach ($drive in $drives) {
        $freeGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        if ($freeGB -lt 1) {
            Write-Log "[ERRO] Drive $($drive.DeviceID) com pouco espaço - $freeGB GB livre" -Level "ERROR"
            $issuesFound++
        } elseif ($freeGB -lt 5) {
            Write-Log "[AVISO] Drive $($drive.DeviceID) com espaço limitado - $freeGB GB livre" -Level "WARNING"
        } else {
            Write-Log "[OK] Drive $($drive.DeviceID) - $freeGB GB livre" -Level "SUCCESS"
        }
    }

    # 8. Limpar arquivos temporários
    Write-Log "8. Verificando arquivos temporários..."
    $tempFiles = Get-ChildItem -Path $env:TEMP -Filter "*zabbix*" -ErrorAction SilentlyContinue
    if ($tempFiles) {
        Write-Log "[AVISO] Encontrados arquivos temporários do Zabbix - $($tempFiles.Count) arquivos" -Level "WARNING"
        if ($FixIssues) {
            Write-Log "Corrigindo: Removendo arquivos temporários..." -Level "INFO"
            foreach ($file in $tempFiles) {
                try {
                    Remove-Item -Path $file.FullName -Force
                    Write-Log "Arquivo $($file.Name) removido" -Level "SUCCESS"
                }
                catch {
                    Write-Log "Erro ao remover $($file.Name): $($_.Exception.Message)" -Level "WARNING"
                }
            }
        }
    } else {
        Write-Log "[OK] Nenhum arquivo temporário do Zabbix encontrado" -Level "SUCCESS"
    }

    # Resumo do diagnóstico
    Write-Log "=== RESUMO DO DIAGNÓSTICO ===" -Level "SUCCESS"
    if ($issuesFound -eq 0) {
        Write-Log "[OK] Sistema limpo - pronto para instalação" -Level "SUCCESS"
        return $true
    } else {
        Write-Log "[AVISO] Encontrados $issuesFound problemas que podem causar falhas na instalação" -Level "WARNING"
        if (-not $FixIssues) {
            Write-Log "PARA CORRIGIR: Execute o script novamente com parâmetro -Force" -Level "INFO"
        }
        return $false
    }
}

# Função para detectar arquitetura
function Get-SystemArchitecture {
    if ([Environment]::Is64BitOperatingSystem) {
        return "x64"
    } else {
        return "x86"
    }
}

# Função para parar e remover serviço existente
function Remove-ExistingZabbixAgent {
    Write-Log "Verificando instalação existente do Zabbix Agent..."

    $removalNeeded = $false

    # Parar serviços do Zabbix Agent (v1 e v2)
    $services = @("Zabbix Agent", "Zabbix Agent 2")
    foreach ($serviceName in $services) {
        $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
        if ($service) {
            Write-Log "Encontrado serviço $serviceName - Status $($service.Status)" -Level "WARNING"
            $removalNeeded = $true

            if ($service.Status -eq "Running") {
                Write-Log "Parando serviço: $serviceName" -Level "WARNING"
                try {
                    Stop-Service -Name $serviceName -Force -TimeoutSec 30 -ErrorAction Stop
                    Write-Log "Serviço $serviceName parado com sucesso"
                }
                catch {
                    Write-Log "Erro ao parar serviço $serviceName : $($_.Exception.Message)" -Level "ERROR"
                    # Tentar matar processo diretamente
                    $processes = Get-Process | Where-Object { $_.ProcessName -like "*zabbix*" }
                    foreach ($proc in $processes) {
                        Write-Log "Terminando processo $($proc.ProcessName) - ID $($proc.Id)" -Level "WARNING"
                        Stop-Process -Id $proc.Id -Force -ErrorAction SilentlyContinue
                    }
                }
            }

            # Remover serviço usando sc.exe (mais confiável)
            Write-Log "Removendo serviço $serviceName"
            try {
                $result = & sc.exe delete $serviceName 2>&1
                if ($LASTEXITCODE -eq 0) {
                    Write-Log "Serviço $serviceName removido com sucesso"
                } else {
                    Write-Log "Aviso ao remover serviço $serviceName : $result" -Level "WARNING"
                }
            }
            catch {
                Write-Log "Erro ao remover serviço $serviceName : $($_.Exception.Message)" -Level "WARNING"
            }
        }
    }

    # Verificar processos do Zabbix ainda rodando
    $zabbixProcesses = Get-Process | Where-Object { $_.ProcessName -like "*zabbix*" }
    if ($zabbixProcesses) {
        Write-Log "Encontrados processos do Zabbix ainda rodando:" -Level "WARNING"
        foreach ($proc in $zabbixProcesses) {
            Write-Log "  - $($proc.ProcessName) - ID $($proc.Id)"
            try {
                Stop-Process -Id $proc.Id -Force
                Write-Log "Processo $($proc.ProcessName) terminado"
            }
            catch {
                Write-Log "Erro ao terminar processo $($proc.ProcessName): $($_.Exception.Message)" -Level "WARNING"
            }
        }
    }

    # Remover instalação via MSI se existir (método mais rápido)
    Write-Log "Verificando instalações MSI do Zabbix..."
    try {
        $zabbixProducts = Get-WmiObject -Class Win32_Product -Filter "Name LIKE '%Zabbix Agent%'" -ErrorAction SilentlyContinue
        foreach ($product in $zabbixProducts) {
            Write-Log "Removendo instalação MSI $($product.Name) - Versão $($product.Version)" -Level "WARNING"
            $removalNeeded = $true
            try {
                $result = $product.Uninstall()
                if ($result.ReturnValue -eq 0) {
                    Write-Log "Produto $($product.Name) removido com sucesso"
                } else {
                    Write-Log "Erro ao remover produto $($product.Name): Código $($result.ReturnValue)" -Level "WARNING"
                }
            }
            catch {
                Write-Log "Erro ao remover produto $($product.Name): $($_.Exception.Message)" -Level "WARNING"
            }
        }
    }
    catch {
        Write-Log "Erro ao consultar produtos MSI: $($_.Exception.Message)" -Level "WARNING"
    }

    # Limpar diretórios residuais
    $oldPaths = @(
        "C:\Program Files\Zabbix Agent",
        "C:\Program Files\Zabbix Agent 2",
        "C:\Program Files (x86)\Zabbix Agent",
        "C:\Program Files (x86)\Zabbix Agent 2"
    )

    foreach ($path in $oldPaths) {
        if (Test-Path $path) {
            Write-Log "Removendo diretório residual: $path" -Level "WARNING"
            $removalNeeded = $true
            try {
                # Tentar remover arquivos em uso
                Get-ChildItem -Path $path -Recurse -Force | ForEach-Object {
                    if (-not $_.PSIsContainer) {
                        try {
                            Remove-Item -Path $_.FullName -Force -ErrorAction Stop
                        }
                        catch {
                            Write-Log "Arquivo em uso, tentando novamente: $($_.FullName)" -Level "WARNING"
                            Start-Sleep -Seconds 1
                            Remove-Item -Path $_.FullName -Force -ErrorAction SilentlyContinue
                        }
                    }
                }
                Remove-Item -Path $path -Recurse -Force -ErrorAction Stop
                Write-Log "Diretório $path removido com sucesso"
            }
            catch {
                Write-Log "Erro ao remover diretório $path : $($_.Exception.Message)" -Level "WARNING"
            }
        }
    }

    # Limpar entradas do registro (opcional)
    $regPaths = @(
        "HKLM:\SOFTWARE\Zabbix",
        "HKLM:\SOFTWARE\WOW6432Node\Zabbix"
    )

    foreach ($regPath in $regPaths) {
        if (Test-Path $regPath) {
            Write-Log "Removendo entrada do registro: $regPath" -Level "WARNING"
            try {
                Remove-Item -Path $regPath -Recurse -Force -ErrorAction Stop
                Write-Log "Entrada do registro removida: $regPath"
            }
            catch {
                Write-Log "Erro ao remover entrada do registro $regPath : $($_.Exception.Message)" -Level "WARNING"
            }
        }
    }

    if ($removalNeeded) {
        Write-Log "Aguardando 3 segundos para finalizar limpeza..." -Level "INFO"
        Start-Sleep -Seconds 3
        Write-Log "Limpeza de instalação anterior concluída" -Level "SUCCESS"
    } else {
        Write-Log "Nenhuma instalação anterior encontrada" -Level "INFO"
    }
}

# Função para download do instalador
function Download-ZabbixAgent {
    param([string]$Architecture)

    $downloadUrl = if ($Architecture -eq "x64") { $DownloadURL64 } else { $DownloadURL32 }
    $fileName = Split-Path $downloadUrl -Leaf
    $downloadPath = Join-Path $env:TEMP $fileName

    Write-Log "Baixando Zabbix Agent 2 v$ZabbixVersion - $Architecture"
    Write-Log "URL: $downloadUrl"

    try {
        # Configurar protocolos de segurança para compatibilidade
        Write-Log "Configurando protocolos de segurança SSL/TLS..."
        [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12 -bor [Net.SecurityProtocolType]::Tls11 -bor [Net.SecurityProtocolType]::Tls

        # Tentar primeiro com Invoke-WebRequest (PowerShell 3.0+)
        try {
            Write-Log "Tentando download com Invoke-WebRequest..."
            Invoke-WebRequest -Uri $downloadUrl -OutFile $downloadPath -UseBasicParsing
            Write-Log "Download concluído com Invoke-WebRequest: $downloadPath" -Level "SUCCESS"
            return $downloadPath
        }
        catch {
            Write-Log "Invoke-WebRequest falhou: $($_.Exception.Message)" -Level "WARNING"

            # Fallback para WebClient com configurações SSL aprimoradas
            Write-Log "Tentando download com WebClient (fallback)..."
            $webClient = New-Object System.Net.WebClient

            # Configurações adicionais para SSL/TLS
            $webClient.Headers.Add("User-Agent", "PowerShell-ZabbixInstaller/1.0")

            # Ignorar erros de certificado se necessário (apenas para download)
            [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}

            $webClient.DownloadFile($downloadUrl, $downloadPath)
            Write-Log "Download concluído com WebClient: $downloadPath" -Level "SUCCESS"

            # Restaurar validação de certificado
            [System.Net.ServicePointManager]::ServerCertificateValidationCallback = $null

            return $downloadPath
        }
    }
    catch {
        Write-Log "Erro no download: $($_.Exception.Message)" -Level "ERROR"
        Write-Log "Tentando URLs alternativas..." -Level "WARNING"

        # Selecionar URLs alternativas baseado na arquitetura
        $alternativeUrls = if ($Architecture -eq "x64") { $AlternativeURL64 } else { $AlternativeURL32 }

        $downloadSuccess = $false
        foreach ($altUrl in $alternativeUrls) {
            try {
                Write-Log "Tentando URL alternativa: $altUrl"

                # Tentar com diferentes métodos
                try {
                    Invoke-WebRequest -Uri $altUrl -OutFile $downloadPath -UseBasicParsing -TimeoutSec 300
                    $downloadSuccess = $true
                    Write-Log "Download concluído com URL alternativa: $downloadPath" -Level "SUCCESS"
                    break
                }
                catch {
                    Write-Log "Invoke-WebRequest falhou para $altUrl : $($_.Exception.Message)" -Level "WARNING"

                    # Fallback para WebClient
                    $webClient = New-Object System.Net.WebClient
                    $webClient.Headers.Add("User-Agent", "PowerShell-ZabbixInstaller/1.0")
                    $webClient.DownloadFile($altUrl, $downloadPath)
                    $downloadSuccess = $true
                    Write-Log "Download concluído com WebClient alternativo: $downloadPath" -Level "SUCCESS"
                    break
                }
            }
            catch {
                Write-Log "Falha na URL alternativa $altUrl : $($_.Exception.Message)" -Level "WARNING"
                continue
            }
        }

        if (-not $downloadSuccess) {
            throw "Não foi possível baixar o instalador do Zabbix Agent 2 de nenhuma fonte. Verifique a conectividade com a internet e configurações de proxy/firewall."
        }

        return $downloadPath
    }
}

# Função para instalar o Zabbix Agent 2
function Install-ZabbixAgent {
    param([string]$InstallerPath)

    Write-Log "Instalando Zabbix Agent 2..."

    # Verificar se o arquivo MSI existe e é válido
    if (-not (Test-Path $InstallerPath)) {
        throw "Arquivo MSI não encontrado: $InstallerPath"
    }

    $fileSize = (Get-Item $InstallerPath).Length
    Write-Log "Tamanho do arquivo MSI: $([math]::Round($fileSize/1MB, 2)) MB"

    if ($fileSize -lt 1MB) {
        throw "Arquivo MSI parece estar corrompido (muito pequeno: $fileSize bytes)"
    }

    # Verificar se há espaço suficiente em disco
    $drive = (Get-Item $InstallPath).PSDrive.Name
    $freeSpace = (Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='${drive}:'").FreeSpace
    Write-Log "Espaço livre em disco ${drive} - $([math]::Round($freeSpace/1GB, 2)) GB"

    if ($freeSpace -lt 100MB) {
        Write-Log "Pouco espaço em disco disponível" -Level "WARNING"
    }

    # Criar diretório de instalação se não existir
    if (-not (Test-Path $InstallPath)) {
        Write-Log "Criando diretório de instalação: $InstallPath"
        New-Item -Path $InstallPath -ItemType Directory -Force | Out-Null
    }

    # Parâmetros de instalação silenciosa com log detalhado
    $logPath = Join-Path $env:TEMP "zabbix_install.log"
    $msiArgs = @(
        "/i", "`"$InstallerPath`"",
        "/quiet",
        "/norestart",
        "/l*v", "`"$logPath`"",
        "INSTALLDIR=`"$InstallPath`"",
        "SERVER=$ZabbixServerIP",
        "SERVERACTIVE=$ZabbixServerIP",
        "HOSTNAME=$AgentHostname",
        "ENABLEREMOTECOMMANDS=1",
        "LOGTYPE=file"
    )

    Write-Log "Executando: msiexec $($msiArgs -join ' ')"
    Write-Log "Log de instalação será salvo em: $logPath"

    try {
        $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $msiArgs -Wait -PassThru -NoNewWindow

        # Analisar código de saída do MSI
        switch ($process.ExitCode) {
            0 {
                Write-Log "Zabbix Agent 2 instalado com sucesso!" -Level "SUCCESS"
                return
            }
            1603 {
                $errorMsg = "Erro fatal durante instalação (1603). Possíveis causas:`n"
                $errorMsg += "- Instalação anterior corrompida`n"
                $errorMsg += "- Permissões insuficientes`n"
                $errorMsg += "- Conflito com software existente`n"
                $errorMsg += "- Parâmetros de instalação inválidos"
                Write-Log $errorMsg -Level "ERROR"
            }
            1618 {
                Write-Log "Outra instalação está em andamento - Erro 1618" -Level "ERROR"
            }
            1619 {
                Write-Log "Pacote de instalação não encontrado ou corrompido - Erro 1619" -Level "ERROR"
            }
            1620 {
                Write-Log "Pacote de instalação não pode ser aberto - Erro 1620" -Level "ERROR"
            }
            1633 {
                Write-Log "Plataforma não suportada - Erro 1633" -Level "ERROR"
            }
            default {
                Write-Log "MSI retornou código de erro desconhecido: $($process.ExitCode)" -Level "ERROR"
            }
        }

        # Tentar ler o log de instalação para mais detalhes
        if (Test-Path $logPath) {
            Write-Log "Analisando log de instalação..." -Level "INFO"
            $logContent = Get-Content $logPath -Tail 50
            $errorLines = $logContent | Where-Object { $_ -match "error|failed|exception" -and $_ -notmatch "MSI \(s\)" }

            if ($errorLines) {
                Write-Log "Erros encontrados no log:" -Level "ERROR"
                foreach ($line in $errorLines | Select-Object -First 5) {
                    Write-Log "  $line" -Level "ERROR"
                }
            }

            Write-Log "Log completo disponível em: $logPath" -Level "INFO"
        }

        throw "Instalação falhou com código: $($process.ExitCode)"
    }
    catch {
        Write-Log "Erro na instalação: $($_.Exception.Message)" -Level "ERROR"

        # Sugestões de resolução baseadas no erro
        Write-Log "SUGESTÕES PARA RESOLUÇÃO:" -Level "WARNING"
        Write-Log "1. Execute o script com parâmetro -Force para limpar instalações anteriores" -Level "INFO"
        Write-Log "2. Verifique se não há outro Zabbix Agent rodando" -Level "INFO"
        Write-Log "3. Tente executar como Administrador" -Level "INFO"
        Write-Log "4. Verifique o log detalhado em: $logPath" -Level "INFO"

        throw
    }
}

# Função para configurar o arquivo de configuração
function Configure-ZabbixAgent {
    $configPath = Join-Path $InstallPath "zabbix_agent2.conf"
    
    if (-not (Test-Path $configPath)) {
        Write-Log "Arquivo de configuração não encontrado: $configPath" -Level "ERROR"
        return
    }
    
    Write-Log "Configurando arquivo: $configPath"
    
    # Backup da configuração original
    $backupPath = "$configPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item -Path $configPath -Destination $backupPath
    Write-Log "Backup criado: $backupPath"
    
    # Configurações personalizadas
    $config = @"
# Configuração do Zabbix Agent 2
# Gerado automaticamente em $(Get-Date)

# Servidor Zabbix
Server=$ZabbixServerIP
ServerActive=$ZabbixServerIP

# Identificação do agente
Hostname=$AgentHostname

# Configurações de log
LogType=file
LogFile=$InstallPath\zabbix_agent2.log
LogFileSize=10

# Configurações de segurança
EnableRemoteCommands=1
UnsafeUserParameters=0

# Configurações de performance
Timeout=30
Include=$InstallPath\zabbix_agent2.d\*.conf

# Configurações de rede
ListenPort=10050
StartAgents=3

# Buffer de dados
BufferSend=5
BufferSize=100
"@
    
    try {
        $config | Out-File -FilePath $configPath -Encoding UTF8 -Force
        Write-Log "Configuração aplicada com sucesso!" -Level "SUCCESS"
    }
    catch {
        Write-Log "Erro ao configurar: $($_.Exception.Message)" -Level "ERROR"
        throw
    }
}

# Função para configurar o serviço
function Configure-ZabbixService {
    Write-Log "Configurando serviço Zabbix Agent 2..."
    
    try {
        # Verificar se o serviço existe
        $service = Get-Service -Name "Zabbix Agent 2" -ErrorAction SilentlyContinue
        
        if ($service) {
            # Configurar para inicialização automática
            Set-Service -Name "Zabbix Agent 2" -StartupType Automatic
            
            # Iniciar o serviço
            Start-Service -Name "Zabbix Agent 2"
            
            # Verificar status
            $service = Get-Service -Name "Zabbix Agent 2"
            if ($service.Status -eq "Running") {
                Write-Log "Serviço iniciado com sucesso!" -Level "SUCCESS"
            } else {
                Write-Log "Serviço não está rodando. Status: $($service.Status)" -Level "WARNING"
            }
        } else {
            Write-Log "Serviço 'Zabbix Agent 2' não encontrado" -Level "ERROR"
        }
    }
    catch {
        Write-Log "Erro ao configurar serviço: $($_.Exception.Message)" -Level "ERROR"
        throw
    }
}

# Função para configurar firewall
function Configure-Firewall {
    Write-Log "Configurando regras de firewall..."
    
    try {
        # Remover regras existentes
        Get-NetFirewallRule -DisplayName "*Zabbix*" -ErrorAction SilentlyContinue | Remove-NetFirewallRule -ErrorAction SilentlyContinue
        
        # Criar nova regra para porta 10050
        New-NetFirewallRule -DisplayName "Zabbix Agent 2" -Direction Inbound -Protocol TCP -LocalPort 10050 -Action Allow -Profile Any
        Write-Log "Regra de firewall criada para porta 10050" -Level "SUCCESS"
    }
    catch {
        Write-Log "Erro ao configurar firewall: $($_.Exception.Message)" -Level "WARNING"
    }
}

# Função para testar conectividade
function Test-ZabbixConnectivity {
    Write-Log "Testando conectividade com servidor Zabbix..."
    
    try {
        # Testar ping
        $pingResult = Test-Connection -ComputerName $ZabbixServerIP -Count 2 -Quiet
        if ($pingResult) {
            Write-Log "Ping para ${ZabbixServerIP}: OK" -Level "SUCCESS"
        } else {
            Write-Log "Ping para ${ZabbixServerIP}: FALHOU" -Level "WARNING"
        }

        # Testar porta 10051 (Zabbix Server)
        $tcpTest = Test-NetConnection -ComputerName $ZabbixServerIP -Port 10051 -WarningAction SilentlyContinue
        if ($tcpTest.TcpTestSucceeded) {
            Write-Log "Conexão TCP para ${ZabbixServerIP}:10051: OK" -Level "SUCCESS"
        } else {
            Write-Log "Conexão TCP para ${ZabbixServerIP}:10051: FALHOU" -Level "WARNING"
        }
    }
    catch {
        Write-Log "Erro no teste de conectividade: $($_.Exception.Message)" -Level "WARNING"
    }
}

# Função principal
function Main {
    Write-Log "=== INSTALAÇÃO DO ZABBIX AGENT 2 PARA WINDOWS ===" -Level "SUCCESS"
    Write-Log "Versão $ZabbixVersion"
    Write-Log "Servidor Zabbix $ZabbixServerIP"
    Write-Log "Hostname $AgentHostname"
    Write-Log "Caminho de instalação $InstallPath"

    # Verificar privilégios de administrador
    if (-not (Test-Administrator)) {
        Write-Log "Este script deve ser executado como Administrador!" -Level "ERROR"
        exit 1
    }

    # Configurar protocolos SSL/TLS
    Initialize-SecurityProtocols

    # Detectar arquitetura
    $architecture = Get-SystemArchitecture
    Write-Log "Arquitetura detectada $architecture"

    # Modo diagnóstico apenas
    if ($DiagnoseOnly) {
        Write-Log "=== MODO DIAGNÓSTICO ATIVADO ===" -Level "INFO"
        $systemClean = Invoke-SystemDiagnosis -FixIssues $false
        if ($systemClean) {
            Write-Log "Sistema pronto para instalação do Zabbix Agent 2" -Level "SUCCESS"
            exit 0
        } else {
            Write-Log "Problemas encontrados. Execute novamente com -Force para corrigir automaticamente" -Level "WARNING"
            exit 1
        }
    }

    # Diagnóstico e correção automática se Force estiver habilitado
    if ($Force) {
        Write-Log "=== EXECUTANDO DIAGNÓSTICO E CORREÇÃO ===" -Level "INFO"
        $systemClean = Invoke-SystemDiagnosis -FixIssues $true
        if (-not $systemClean) {
            Write-Log "Alguns problemas não puderam ser corrigidos automaticamente" -Level "WARNING"
        }
    } else {
        # Diagnóstico básico sem correção
        $systemClean = Invoke-SystemDiagnosis -FixIssues $false
        if (-not $systemClean) {
            Write-Log "Problemas detectados. Recomenda-se usar -Force para correção automática" -Level "WARNING"
        }
    }
    
    try {
        # Remover instalação existente se Force estiver habilitado ou se houver conflito
        if ($Force -or (Get-Service -Name "Zabbix Agent*" -ErrorAction SilentlyContinue)) {
            Remove-ExistingZabbixAgent
        }

        # Determinar caminho do instalador
        if ($LocalMSIPath -and (Test-Path $LocalMSIPath)) {
            Write-Log "Usando arquivo MSI local: $LocalMSIPath" -Level "SUCCESS"
            $installerPath = $LocalMSIPath
        } else {
            if ($LocalMSIPath) {
                Write-Log "Arquivo MSI local não encontrado: $LocalMSIPath" -Level "WARNING"
                Write-Log "Tentando download online..." -Level "INFO"
            }
            # Download do instalador
            $installerPath = Download-ZabbixAgent -Architecture $architecture
        }
        
        # Instalar Zabbix Agent 2
        Install-ZabbixAgent -InstallerPath $installerPath
        
        # Configurar arquivo de configuração
        Configure-ZabbixAgent
        
        # Configurar serviço
        Configure-ZabbixService
        
        # Configurar firewall
        Configure-Firewall
        
        # Testar conectividade
        Test-ZabbixConnectivity
        
        # Limpeza (apenas se foi baixado, não se foi fornecido localmente)
        if (-not $LocalMSIPath -or -not (Test-Path $LocalMSIPath)) {
            Write-Log "Removendo arquivo temporário: $installerPath"
            Remove-Item -Path $installerPath -Force -ErrorAction SilentlyContinue
        } else {
            Write-Log "Mantendo arquivo MSI local: $installerPath"
        }
        
        Write-Log "=== INSTALAÇÃO CONCLUÍDA COM SUCESSO! ===" -Level "SUCCESS"
        Write-Log "O Zabbix Agent 2 está instalado e rodando"
        Write-Log "Configuração: $InstallPath\zabbix_agent2.conf"
        Write-Log "Logs: $InstallPath\zabbix_agent2.log"
        
    }
    catch {
        Write-Log "ERRO DURANTE A INSTALAÇÃO: $($_.Exception.Message)" -Level "ERROR"
        exit 1
    }
}

# Executar script principal
Main
