@echo off
title Instalacao TacticalRMM - NVirtual
color 0A

echo ========================================
echo    INSTALACAO TACTICALRMM - NVIRTUAL
echo ========================================
echo.

REM Solicita o ID do cliente
:input_client
set /p CLIENT_ID="Digite o ID do Cliente: "
if "%CLIENT_ID%"=="" (
    echo Erro: ID do Cliente nao pode estar vazio!
    echo.
    goto input_client
)

REM Solicita o ID do site
:input_site
set /p SITE_ID="Digite o ID do Site: "
if "%SITE_ID%"=="" (
    echo Erro: ID do Site nao pode estar vazio!
    echo.
    goto input_site
)

echo.
echo ========================================
echo Configuracoes:
echo Cliente ID: %CLIENT_ID%
echo Site ID: %SITE_ID%
echo Tipo: Workstation
echo ========================================
echo.

REM Confirma a instalacao
set /p CONFIRM="Confirma a instalacao? (S/N): "
if /i not "%CONFIRM%"=="S" (
    echo Instalacao cancelada pelo usuario.
    pause
    exit /b
)

echo.
echo Iniciando instalacao do TacticalRMM...
echo.

REM Verifica se o executavel existe
if not exist "tacticalagent-v2.9.1-windows-amd64.exe" (
    echo ERRO: Arquivo tacticalagent-v2.9.1-windows-amd64.exe nao encontrado!
    echo Certifique-se de que o arquivo esta na mesma pasta do script.
    pause
    exit /b 1
)

REM Executa a instalacao
echo Instalando o agente...
tacticalagent-v2.9.1-windows-amd64.exe /VERYSILENT /SUPPRESSMSGBOXES

REM Aguarda 5 segundos
echo Aguardando conclusao da instalacao...
ping 127.0.0.1 -n 5 >nul

REM Configura o agente
echo Configurando o agente...
"C:\Program Files\TacticalAgent\tacticalrmm.exe" -m install --api https://api.centralmesh.nvirtual.com.br --client-id %CLIENT_ID% --site-id %SITE_ID% --agent-type workstation --auth b21afb9812ea2676e30e8b9a40f32a4beb5b8033a782ca3612fc379d4d76787a --ping -silent

REM Verifica se a instalacao foi bem-sucedida
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo   INSTALACAO CONCLUIDA COM SUCESSO!
    echo ========================================
    echo Cliente ID: %CLIENT_ID%
    echo Site ID: %SITE_ID%
    echo Tipo: Workstation
    echo ========================================
) else (
    echo.
    echo ========================================
    echo      ERRO NA INSTALACAO!
    echo ========================================
    echo Verifique os logs para mais detalhes.
)

echo.
echo Pressione qualquer tecla para sair...
pause >nul
