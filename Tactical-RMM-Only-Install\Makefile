# Makefile para Tactical RMM Installation Scripts
# Autor: <PERSON> - NVirtual
# Data: 2025-01-21

.PHONY: help install uninstall status quick-install setup permissions clean

# Cores para output
GREEN = \033[0;32m
YELLOW = \033[1;33m
RED = \033[0;31m
NC = \033[0m # No Color

# Variáveis
SCRIPTS = install-tactical-only.sh uninstall-tactical.sh check-tactical-status.sh quick-install.sh
CONFIG_FILE = config
CONFIG_EXAMPLE = config.example

help: ## Mostrar esta ajuda
	@echo "$(GREEN)Tactical RMM Installation Scripts$(NC)"
	@echo "$(YELLOW)Desenvolvido por Paulo Matheus - NVirtual$(NC)"
	@echo ""
	@echo "Comandos disponíveis:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

setup: ## Configurar permissões e arquivos iniciais
	@echo "$(GREEN)Configurando scripts...$(NC)"
	@chmod +x $(SCRIPTS)
	@if [ ! -f $(CONFIG_FILE) ]; then \
		echo "$(YELLOW)Criando arquivo de configuração...$(NC)"; \
		cp $(CONFIG_EXAMPLE) $(CONFIG_FILE); \
		echo "$(YELLOW)⚠️  Edite o arquivo 'config' antes de usar quick-install$(NC)"; \
	fi
	@echo "$(GREEN)✅ Setup concluído!$(NC)"

permissions: ## Dar permissões de execução aos scripts
	@echo "$(GREEN)Configurando permissões...$(NC)"
	@chmod +x $(SCRIPTS)
	@echo "$(GREEN)✅ Permissões configuradas!$(NC)"

install: permissions ## Instalar Tactical RMM (modo interativo)
	@echo "$(GREEN)Iniciando instalação interativa...$(NC)"
	@./install-tactical-only.sh

quick-install: setup ## Instalação rápida usando arquivo de configuração
	@echo "$(GREEN)Iniciando instalação rápida...$(NC)"
	@if [ ! -f $(CONFIG_FILE) ]; then \
		echo "$(RED)❌ Arquivo 'config' não encontrado!$(NC)"; \
		echo "$(YELLOW)Execute 'make setup' primeiro e edite o arquivo 'config'$(NC)"; \
		exit 1; \
	fi
	@./quick-install.sh

status: permissions ## Verificar status do Tactical RMM
	@echo "$(GREEN)Verificando status...$(NC)"
	@./check-tactical-status.sh

status-services: permissions ## Verificar apenas status dos serviços
	@echo "$(GREEN)Verificando serviços...$(NC)"
	@./check-tactical-status.sh 2

status-processes: permissions ## Verificar apenas processos em execução
	@echo "$(GREEN)Verificando processos...$(NC)"
	@./check-tactical-status.sh 3

status-files: permissions ## Verificar apenas arquivos de instalação
	@echo "$(GREEN)Verificando arquivos...$(NC)"
	@./check-tactical-status.sh 4

status-connectivity: permissions ## Verificar apenas conectividade
	@echo "$(GREEN)Verificando conectividade...$(NC)"
	@./check-tactical-status.sh 5

logs: permissions ## Mostrar logs recentes
	@echo "$(GREEN)Mostrando logs recentes...$(NC)"
	@./check-tactical-status.sh 6

monitor: permissions ## Monitoramento contínuo (logs em tempo real)
	@echo "$(GREEN)Iniciando monitoramento contínuo...$(NC)"
	@echo "$(YELLOW)Pressione Ctrl+C para parar$(NC)"
	@./check-tactical-status.sh 8

uninstall: permissions ## Desinstalar Tactical RMM
	@echo "$(RED)Iniciando desinstalação...$(NC)"
	@./uninstall-tactical.sh

clean: ## Limpar arquivos temporários
	@echo "$(GREEN)Limpando arquivos temporários...$(NC)"
	@rm -f /tmp/tactical_install_auto.sh
	@rm -f /tmp/rmmagent-linux.sh
	@rm -f /tmp/rmmagent.tar.gz
	@rm -rf /tmp/rmmagent-master
	@echo "$(GREEN)✅ Limpeza concluída!$(NC)"

config-edit: ## Editar arquivo de configuração
	@if [ ! -f $(CONFIG_FILE) ]; then \
		echo "$(YELLOW)Arquivo 'config' não existe. Criando...$(NC)"; \
		cp $(CONFIG_EXAMPLE) $(CONFIG_FILE); \
	fi
	@echo "$(GREEN)Abrindo editor para o arquivo 'config'...$(NC)"
	@${EDITOR:-nano} $(CONFIG_FILE)

config-show: ## Mostrar configuração atual
	@if [ -f $(CONFIG_FILE) ]; then \
		echo "$(GREEN)Configuração atual:$(NC)"; \
		echo "$(YELLOW)==================$(NC)"; \
		grep -v "^#" $(CONFIG_FILE) | grep -v "^$$"; \
	else \
		echo "$(RED)❌ Arquivo 'config' não encontrado!$(NC)"; \
	fi

config-validate: ## Validar arquivo de configuração
	@if [ -f $(CONFIG_FILE) ]; then \
		echo "$(GREEN)Validando configuração...$(NC)"; \
		source $(CONFIG_FILE); \
		if [ -z "$$TACTICAL_CLIENT_ID" ]; then \
			echo "$(RED)❌ TACTICAL_CLIENT_ID não está definido$(NC)"; \
			exit 1; \
		fi; \
		if [ -z "$$TACTICAL_CLIENT_FILIAL" ]; then \
			echo "$(RED)❌ TACTICAL_CLIENT_FILIAL não está definido$(NC)"; \
			exit 1; \
		fi; \
		echo "$(GREEN)✅ Configuração válida!$(NC)"; \
		echo "Cliente ID: $$TACTICAL_CLIENT_ID"; \
		echo "Filial: $$TACTICAL_CLIENT_FILIAL"; \
	else \
		echo "$(RED)❌ Arquivo 'config' não encontrado!$(NC)"; \
	fi

info: ## Mostrar informações do sistema
	@echo "$(GREEN)Informações do sistema:$(NC)"
	@echo "$(YELLOW)======================$(NC)"
	@echo "OS: $$(lsb_release -d 2>/dev/null | cut -f2 || cat /etc/os-release | grep PRETTY_NAME | cut -d'\"' -f2)"
	@echo "Kernel: $$(uname -r)"
	@echo "Arquitetura: $$(uname -m)"
	@echo "Usuário: $$(whoami)"
	@echo "Diretório: $$(pwd)"

test-connectivity: ## Testar conectividade com servidores NVirtual
	@echo "$(GREEN)Testando conectividade...$(NC)"
	@echo "$(YELLOW)========================$(NC)"
	@echo -n "Mesh Server: "
	@if curl -I https://mesh.centralmesh.nvirtual.com.br --connect-timeout 10 >/dev/null 2>&1; then \
		echo "$(GREEN)OK$(NC)"; \
	else \
		echo "$(RED)FALHA$(NC)"; \
	fi
	@echo -n "API Server: "
	@if curl -I https://api.centralmesh.nvirtual.com.br --connect-timeout 10 >/dev/null 2>&1; then \
		echo "$(GREEN)OK$(NC)"; \
	else \
		echo "$(RED)FALHA$(NC)"; \
	fi

# Comandos de desenvolvimento
dev-setup: ## Setup para desenvolvimento
	@echo "$(GREEN)Configurando ambiente de desenvolvimento...$(NC)"
	@chmod +x $(SCRIPTS)
	@cp $(CONFIG_EXAMPLE) $(CONFIG_FILE)
	@echo "$(GREEN)✅ Ambiente de desenvolvimento configurado!$(NC)"

dev-test: ## Executar testes básicos
	@echo "$(GREEN)Executando testes básicos...$(NC)"
	@bash -n install-tactical-only.sh && echo "✅ install-tactical-only.sh: sintaxe OK"
	@bash -n uninstall-tactical.sh && echo "✅ uninstall-tactical.sh: sintaxe OK"
	@bash -n check-tactical-status.sh && echo "✅ check-tactical-status.sh: sintaxe OK"
	@bash -n quick-install.sh && echo "✅ quick-install.sh: sintaxe OK"
	@echo "$(GREEN)✅ Todos os testes passaram!$(NC)"
