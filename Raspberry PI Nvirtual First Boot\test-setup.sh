#!/bin/bash

# Script para testar a configuração do first boot
# Use este script para verificar se tudo está funcionando

set -e

GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[TEST] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

log "=== Teste de Configuração First Boot ==="

# Verificar se estamos executando como root
if [ "$EUID" -ne 0 ]; then
    error "Execute como root: sudo $0"
    exit 1
fi

# Verificar arquivos necessários
log "Verificando arquivos..."

FILES=(
    "first-boot.service"
    "first-boot.sh"
    "raspberry-auto-setup.sh"
    "setup.sh"
    "install-to-image.sh"
)

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        log "✅ $file encontrado"
    else
        error "❌ $file não encontrado"
        exit 1
    fi
done

# Verificar se o script principal é executável
if [ -x "first-boot.sh" ]; then
    log "✅ first-boot.sh é executável"
else
    warning "⚠️  first-boot.sh não é executável, corrigindo..."
    chmod +x first-boot.sh
fi

# Verificar conectividade com GitHub
log "Testando conectividade com GitHub..."
if curl -s --head https://github.com/paulomatheusgrr/nvirtual-projects > /dev/null; then
    log "✅ Conectividade com GitHub OK"
else
    warning "⚠️  Problema de conectividade com GitHub"
fi

# Verificar se o script existe no GitHub
log "Verificando se o script existe no GitHub..."
GITHUB_URL="https://raw.githubusercontent.com/paulomatheusgrr/nvirtual-projects/main/Raspberry%20PI%20Nvirtual%20First%20Boot/raspberry-auto-setup.sh"
if curl -s --head "$GITHUB_URL" | grep -q "200 OK"; then
    log "✅ Script encontrado no GitHub"
else
    warning "⚠️  Script não encontrado no GitHub em: $GITHUB_URL"
    warning "Certifique-se de fazer commit e push do arquivo raspberry-auto-setup.sh"
fi

# Simular instalação (sem executar)
log "Simulando instalação..."

# Copiar arquivos para locais temporários
TEMP_DIR="/tmp/first-boot-test"
mkdir -p "$TEMP_DIR"

cp first-boot.sh "$TEMP_DIR/"
cp first-boot.service "$TEMP_DIR/"

log "✅ Arquivos copiados para $TEMP_DIR"

# Verificar sintaxe do serviço systemd
log "Verificando sintaxe do serviço systemd..."
if systemd-analyze verify "$TEMP_DIR/first-boot.service" 2>/dev/null; then
    log "✅ Serviço systemd válido"
else
    warning "⚠️  Possível problema na sintaxe do serviço systemd"
fi

# Verificar sintaxe do script bash
log "Verificando sintaxe do script bash..."
if bash -n "$TEMP_DIR/first-boot.sh"; then
    log "✅ Script bash válido"
else
    error "❌ Erro de sintaxe no script bash"
    exit 1
fi

# Limpar arquivos temporários
rm -rf "$TEMP_DIR"

log "=== Teste concluído com sucesso ==="
echo
log "📋 Próximos passos:"
echo "1. Faça commit e push do arquivo raspberry-auto-setup.sh para o GitHub"
echo "2. Use './setup.sh' para instalar em um Raspberry Pi funcionando"
echo "3. Ou use './install-to-image.sh imagem.img' para modificar uma imagem"
echo
log "🔗 URL do script no GitHub:"
echo "https://github.com/paulomatheusgrr/nvirtual-projects/blob/main/Raspberry%20PI%20Nvirtual%20First%20Boot/raspberry-auto-setup.sh"
