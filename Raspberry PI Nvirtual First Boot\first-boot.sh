#!/bin/bash

# First Boot Script for Raspberry Pi
# Este script executa apenas no primeiro boot

set -e

# Configurações - MODIFIQUE ESTAS VARIÁVEIS
GITHUB_REPO="https://github.com/paulomatheusgrr/nvirtual-projects"
SCRIPT_PATH="Raspberry PI Nvirtual First Boot/raspberry-auto-setup.sh"
GITHUB_TOKEN=""  # Deixe vazio para repositórios públicos

# Diretório de trabalho
WORK_DIR="/tmp/first-boot"
LOG_FILE="/var/log/first-boot.log"

# Função para log
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Função para cleanup
cleanup() {
    log "Limpando arquivos temporários..."
    rm -rf "$WORK_DIR"
}

# Trap para cleanup em caso de erro
trap cleanup EXIT

log "=== Iniciando First Boot Script ==="

# Criar diretório de trabalho
mkdir -p "$WORK_DIR"
cd "$WORK_DIR"

# Aguardar conectividade de rede
log "Aguardando conectividade de rede..."
for i in {1..30}; do
    if ping -c 1 ******* >/dev/null 2>&1; then
        log "Conectividade de rede estabelecida"
        break
    fi
    if [ $i -eq 30 ]; then
        log "ERRO: Não foi possível estabelecer conectividade de rede"
        exit 1
    fi
    sleep 2
done

# Instalar dependências se necessário
log "Verificando dependências..."
if ! command -v git >/dev/null 2>&1; then
    log "Instalando git..."
    apt-get update
    apt-get install -y git
fi

# Clonar repositório ou baixar arquivo específico
log "Baixando script do GitHub..."
if [ -n "$GITHUB_TOKEN" ]; then
    # Para repositórios privados
    git clone "https://$GITHUB_TOKEN@${GITHUB_REPO#https://}" repo
else
    # Para repositórios públicos
    git clone "$GITHUB_REPO" repo
fi

# Verificar se o script existe
SCRIPT_FULL_PATH="$WORK_DIR/repo/$SCRIPT_PATH"
if [ ! -f "$SCRIPT_FULL_PATH" ]; then
    log "ERRO: Script não encontrado em $SCRIPT_PATH"
    exit 1
fi

# Tornar o script executável
chmod +x "$SCRIPT_FULL_PATH"

# Executar o script automatizado
log "Executando script: $SCRIPT_PATH"
cd "$WORK_DIR/repo"

# Tornar o script executável
chmod +x "$SCRIPT_FULL_PATH"

# Executar o script diretamente (já é automatizado)
log "Executando configuração automática do Raspberry Pi..."
bash "$SCRIPT_FULL_PATH" 2>&1 | tee -a "$LOG_FILE"

log "=== First Boot Script concluído com sucesso ==="

# O cleanup será executado automaticamente pelo trap
