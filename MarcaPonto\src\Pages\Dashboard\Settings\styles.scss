.aoo__curioso{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba($color: #000000, $alpha: 0.7);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;

    video{
        width: 100%;
        height: 150%;
        border-radius: 40px;
    }
}

.settings__opt{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    & > div{
        flex: 1;
        margin: 20px 10px 0;

        &:first-child{
            margin-left: 0;
        }

        p{
            text-align: center;
        }
    }

    .opt__1{
        .form__inputs{
            width: 100%;

            .form__group{
                width: 80%;
            }
        }

        button{
            &.hasError{
                pointer-events: none;
            }
        }
    }

    .opt__2{
        .opt2__lang{
            margin-top: 20px;

            .mo__wrapper{
                margin: 4px 0;
                display: flex;
                flex-direction: row;
                align-items: center;
                padding: 0 10px;
                cursor: pointer;

                p{
                    margin-left: 5px;
                }

                &:hover{
                    background-color: #f2f2f2;
                    border-radius: 20px;
                }
            }
        }
    }

    .opt__3{
        position: relative;

        .sobre__animation{
            position: absolute;
            bottom: 0;
            right: 0;
            opacity: .2;
        }

        .nao__clica{
            position: absolute;
            bottom: 20px;
            font-size: 9px;
            cursor: pointer;

            &:hover{
                transform: scale(1.09);
            }
        }

        .sobre__membros{
            margin-top: 20px;

            ul{
                li{
                    text-align: center;
                    margin: 10px 0;

                    strong{
                        font-weight: 700;
                        font-size: 16px;
                    }
                }
            }
        }
    }
}