import logging
from datetime import datetime
import os

# Criar pasta de logs (se não existir)
if not os.path.exists("logs"):
    os.makedirs("logs")

# Nome do arquivo baseado na data
log_filename = datetime.now().strftime("logs/%Y-%m-%d.log")

# Configuração do logger
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler(log_filename, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("MovideskBot")
