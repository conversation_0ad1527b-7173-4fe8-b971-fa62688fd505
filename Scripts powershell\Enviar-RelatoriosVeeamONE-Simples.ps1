<#
.SYNOPSIS
    Script simples para envio manual de relatórios do Veeam ONE

.DESCRIPTION
    Versão simplificada do script para disparar relatórios do Veeam ONE manualmente.
    Este script foca em métodos diretos e práticos para execução imediata.

.PARAMETER ReportName
    Nome do relatório para executar (opcional - se não especificado, mostra menu)

.PARAMETER All
    Executa todos os relatórios encontrados

.EXAMPLE
    .\Enviar-RelatoriosVeeamONE-Simples.ps1
    Mostra menu interativo

.EXAMPLE
    .\Enviar-RelatoriosVeeamONE-Simples.ps1 -ReportName "Backup Status"
    Executa relatório específico

.EXAMPLE
    .\Enviar-RelatoriosVeeamONE-Simples.ps1 -All
    Executa todos os relatórios

.NOTES
    Autor: Paulo Matheus - NVirtual
    Versão: 1.0 - Versão Simplificada
#>

param(
    [string]$ReportName,
    [switch]$All
)

# Função para log colorido
function Write-ColorLog {
    param([string]$Message, [string]$Color = "White")
    $timestamp = Get-Date -Format "HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

# Função para executar relatório via linha de comando
function Execute-VeeamReport {
    param([string]$Name)
    
    Write-ColorLog "Executando relatório: $Name" "Yellow"
    
    # Método 1: Tentar via executável do Veeam ONE
    $veeamOnePath = "${env:ProgramFiles}\Veeam\Veeam ONE"
    $reporterExe = "$veeamOnePath\Veeam.ONE.Reporter.exe"
    
    if (Test-Path $reporterExe) {
        try {
            $arguments = "/report:`"$Name`" /execute /silent"
            Start-Process -FilePath $reporterExe -ArgumentList $arguments -Wait -NoNewWindow
            Write-ColorLog "✓ Relatório '$Name' executado com sucesso!" "Green"
            return $true
        } catch {
            Write-ColorLog "✗ Erro ao executar via Reporter.exe: $($_.Exception.Message)" "Red"
        }
    }
    
    # Método 2: Tentar via PowerShell do Veeam
    try {
        # Carregar snap-in se disponível
        if (-not (Get-PSSnapin -Name VeeamPSSnapin -ErrorAction SilentlyContinue)) {
            Add-PSSnapin VeeamPSSnapin -ErrorAction SilentlyContinue
        }
        
        # Tentar cmdlets específicos
        if (Get-Command "Start-VBRReportJob" -ErrorAction SilentlyContinue) {
            $reportJob = Get-VBRReportJob | Where-Object { $_.Name -like "*$Name*" } | Select-Object -First 1
            if ($reportJob) {
                Start-VBRReportJob -ReportJob $reportJob
                Write-ColorLog "✓ Relatório '$Name' iniciado via PowerShell!" "Green"
                return $true
            }
        }
    } catch {
        Write-ColorLog "Método PowerShell não disponível: $($_.Exception.Message)" "Yellow"
    }
    
    # Método 3: Tentar via WMI/CIM (se disponível)
    try {
        $veeamService = Get-Service -Name "*Veeam*ONE*" -ErrorAction SilentlyContinue | Select-Object -First 1
        if ($veeamService -and $veeamService.Status -eq "Running") {
            Write-ColorLog "Serviço Veeam ONE detectado: $($veeamService.Name)" "Cyan"
            # Aqui você pode adicionar lógica específica para seu ambiente
        }
    } catch {
        Write-ColorLog "Não foi possível detectar serviços Veeam ONE" "Yellow"
    }
    
    Write-ColorLog "✗ Não foi possível executar o relatório '$Name'" "Red"
    return $false
}

# Função para listar relatórios disponíveis
function Get-AvailableReports {
    $reports = @()
    
    # Buscar em locais comuns de configuração
    $configPaths = @(
        "${env:ProgramData}\Veeam\Veeam ONE\Reports",
        "${env:ProgramFiles}\Veeam\Veeam ONE\Reports",
        "${env:ALLUSERSPROFILE}\Veeam\Veeam ONE\Configuration"
    )
    
    foreach ($path in $configPaths) {
        if (Test-Path $path) {
            $files = Get-ChildItem -Path $path -Filter "*.xml" -ErrorAction SilentlyContinue
            foreach ($file in $files) {
                $reports += $file.BaseName
            }
        }
    }
    
    # Se não encontrou arquivos, usar lista padrão
    if ($reports.Count -eq 0) {
        $reports = @(
            "Backup Status Report",
            "Infrastructure Overview",
            "VM Performance Report", 
            "Storage Report",
            "Capacity Planning Report",
            "Backup Job Statistics",
            "Virtual Machine Report",
            "Datastore Report",
            "Host Performance Report"
        )
        Write-ColorLog "Usando lista padrão de relatórios (arquivos de configuração não encontrados)" "Yellow"
    } else {
        Write-ColorLog "Encontrados $($reports.Count) relatórios configurados" "Green"
    }
    
    return $reports | Sort-Object | Get-Unique
}

# Função principal
function Main {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║              VEEAM ONE - ENVIO MANUAL DE RELATÓRIOS          ║" -ForegroundColor Cyan  
    Write-Host "║                    Versão Simplificada                      ║" -ForegroundColor Cyan
    Write-Host "║                 Paulo Matheus - NVirtual                     ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
    Write-Host ""
    
    # Verificar se Veeam ONE está instalado
    $veeamOnePath = "${env:ProgramFiles}\Veeam\Veeam ONE"
    if (-not (Test-Path $veeamOnePath)) {
        Write-ColorLog "⚠️  Veeam ONE não encontrado em: $veeamOnePath" "Red"
        Write-ColorLog "Verifique se o Veeam ONE está instalado neste servidor" "Red"
        Read-Host "Pressione Enter para sair"
        exit 1
    }
    
    Write-ColorLog "✓ Veeam ONE detectado em: $veeamOnePath" "Green"
    
    # Obter lista de relatórios
    $availableReports = Get-AvailableReports
    
    # Executar relatório específico
    if ($ReportName) {
        $matchingReport = $availableReports | Where-Object { $_ -like "*$ReportName*" } | Select-Object -First 1
        
        if ($matchingReport) {
            Execute-VeeamReport -Name $matchingReport
        } else {
            Write-ColorLog "Relatório '$ReportName' não encontrado!" "Red"
            Write-ColorLog "Relatórios disponíveis:" "Yellow"
            $availableReports | ForEach-Object { Write-Host "  - $_" -ForegroundColor Gray }
        }
        return
    }
    
    # Executar todos os relatórios
    if ($All) {
        Write-ColorLog "Executando todos os relatórios disponíveis..." "Cyan"
        $successCount = 0
        $totalCount = $availableReports.Count
        
        foreach ($report in $availableReports) {
            if (Execute-VeeamReport -Name $report) {
                $successCount++
            }
            Start-Sleep -Seconds 3  # Pausa entre execuções
        }
        
        Write-Host ""
        Write-ColorLog "Execução concluída: $successCount/$totalCount relatórios executados com sucesso" "Cyan"
        return
    }
    
    # Menu interativo
    while ($true) {
        Write-Host ""
        Write-ColorLog "=== MENU DE OPÇÕES ===" "Cyan"
        Write-Host "1. Listar relatórios disponíveis" -ForegroundColor White
        Write-Host "2. Executar relatório específico" -ForegroundColor White
        Write-Host "3. Executar todos os relatórios" -ForegroundColor White
        Write-Host "4. Sair" -ForegroundColor White
        Write-Host ""
        
        $choice = Read-Host "Escolha uma opção (1-4)"
        
        switch ($choice) {
            "1" {
                Write-Host ""
                Write-ColorLog "=== RELATÓRIOS DISPONÍVEIS ===" "Cyan"
                for ($i = 0; $i -lt $availableReports.Count; $i++) {
                    Write-Host "$($i + 1). $($availableReports[$i])" -ForegroundColor Yellow
                }
            }
            
            "2" {
                Write-Host ""
                Write-ColorLog "=== EXECUTAR RELATÓRIO ESPECÍFICO ===" "Cyan"
                for ($i = 0; $i -lt $availableReports.Count; $i++) {
                    Write-Host "$($i + 1). $($availableReports[$i])" -ForegroundColor Yellow
                }
                Write-Host ""
                
                $reportChoice = Read-Host "Digite o número do relatório (1-$($availableReports.Count))"
                $reportIndex = [int]$reportChoice - 1
                
                if ($reportIndex -ge 0 -and $reportIndex -lt $availableReports.Count) {
                    $selectedReport = $availableReports[$reportIndex]
                    Execute-VeeamReport -Name $selectedReport
                } else {
                    Write-ColorLog "Opção inválida!" "Red"
                }
            }
            
            "3" {
                Write-Host ""
                Write-ColorLog "Executando todos os relatórios..." "Cyan"
                $confirm = Read-Host "Tem certeza? Isso pode demorar alguns minutos (s/N)"
                
                if ($confirm -eq "s" -or $confirm -eq "S") {
                    $successCount = 0
                    $totalCount = $availableReports.Count
                    
                    foreach ($report in $availableReports) {
                        if (Execute-VeeamReport -Name $report) {
                            $successCount++
                        }
                        Start-Sleep -Seconds 3
                    }
                    
                    Write-Host ""
                    Write-ColorLog "Execução concluída: $successCount/$totalCount relatórios executados" "Cyan"
                } else {
                    Write-ColorLog "Operação cancelada" "Yellow"
                }
            }
            
            "4" {
                Write-ColorLog "Saindo..." "Green"
                exit 0
            }
            
            default {
                Write-ColorLog "Opção inválida! Digite um número de 1 a 4." "Red"
            }
        }
    }
}

# Executar script
try {
    Main
} catch {
    Write-ColorLog "Erro inesperado: $($_.Exception.Message)" "Red"
    Read-Host "Pressione Enter para sair"
    exit 1
}
