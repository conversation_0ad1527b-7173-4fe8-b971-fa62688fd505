#!/bin/bash

# Script de instalação dedicado para Tactical RMM Agent
# Autor: <PERSON> - NVirtual
# Data: 2025-01-21
# Versão: 1.0.0
#
# Este script instala APENAS o Tactical RMM Agent
# Separado do projeto Zabbix para uso independente

set -e  # Para o script em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Detectar versão do Ubuntu e arquitetura
UBUNTU_VERSION=""
ARCHITECTURE=$(uname -m)
IS_ARM=false

if grep -q "Ubuntu 24.04" /etc/os-release; then
    UBUNTU_VERSION="24.04"
    log "Ubuntu 24.04 detectado"
elif grep -q "Ubuntu 22.04" /etc/os-release; then
    UBUNTU_VERSION="22.04"
    log "Ubuntu 22.04 detectado"
elif grep -q "Ubuntu 20.04" /etc/os-release; then
    UBUNTU_VERSION="20.04"
    log "Ubuntu 20.04 detectado"
elif grep -q "Debian" /etc/os-release; then
    UBUNTU_VERSION="debian"
    log "Debian detectado"
else
    warning "Sistema não testado. Tentando instalação genérica..."
    UBUNTU_VERSION="generic"
fi

# Detectar arquitetura ARM
if [[ "$ARCHITECTURE" == "aarch64" ]] || [[ "$ARCHITECTURE" == "armv7l" ]] || [[ "$ARCHITECTURE" == "arm64" ]]; then
    IS_ARM=true
    log "Arquitetura ARM detectada: $ARCHITECTURE"
else
    log "Arquitetura x86_64 detectada: $ARCHITECTURE"
fi

# Banner
echo "
                                                                                                           
                      ####                                                                                  
    ################# ####                                                                                  
   #                  ####                                                                                   
   ##  ####       ###  ## ####         #### ### ############ ############        ###     ####       ###         
   ##  ######     ###  ##  ####       ####  ### ###     #####   ###   ###        ###    ######      ###         
   ##  ########   ###  ##   ####     ###    ### ###       ###   ###   ###        ###   ### ####     ###         
   ##  ###  ##### ###  ##    ####   ###     ### ###    ######   ###   ###        ###  ###   ####    ###         
   ##  ###    #######  ##      #######      ### ###    #####    ###   ###       #### ###     ####   ###         
   ##  ###      #####  ##       #####       ### ###     #####   ###    ############ ###        ###  ########### 
   ##  ###        ###  ##        ###        ### ###       ####  ###     ########## ###          ### ########### 
  ####                 #                                                                       
  #### ################                                                                              
  ####                                                                                             
                                                                                                           
"
log "Instalador Tactical RMM Agent - NVirtual"
log "Desenvolvido por Paulo Matheus"
log "Versão: 1.0.0"
echo

# Verificar se está rodando como root
if [[ $EUID -eq 0 ]]; then
    error "Este script não deve ser executado como root. Execute como usuário normal com sudo."
fi

# Verificar se sudo está disponível
if ! command -v sudo &> /dev/null; then
    error "sudo não está instalado. Instale sudo primeiro: apt install sudo"
fi

# Verificar se está em modo automático
AUTO_MODE=false
if [[ "$1" == "--auto" ]] || [[ "${SILENT_MODE:-false}" == "true" ]]; then
    AUTO_MODE=true
fi

# Solicitar informações do usuário (apenas se não estiver em modo automático)
if [[ "$AUTO_MODE" == "false" ]]; then
    log "Configuração do Tactical RMM..."
    echo

    read -p "Digite o ID do Cliente (ID_CLIENT): " TACTICAL_CLIENT_ID
    if [[ -z "$TACTICAL_CLIENT_ID" ]]; then
        error "ID do Cliente é obrigatório"
    fi

    read -p "Digite a Filial do Cliente (FILIAL_CLIENT): " TACTICAL_CLIENT_FILIAL
    if [[ -z "$TACTICAL_CLIENT_FILIAL" ]]; then
        error "Filial do Cliente é obrigatória"
    fi

    echo
    info "Configurações fornecidas:"
    info "• Cliente ID: $TACTICAL_CLIENT_ID"
    info "• Filial: $TACTICAL_CLIENT_FILIAL"
    info "• Arquitetura: $ARCHITECTURE"
    info "• Sistema: $UBUNTU_VERSION"
    echo

    read -p "Confirma as configurações acima? (s/N): " CONFIRM_CONFIG
    if [[ ! "$CONFIRM_CONFIG" =~ ^[Ss]$ ]]; then
        error "Instalação cancelada pelo usuário."
    fi
else
    # Modo automático - usar variáveis de ambiente
    if [[ -z "$TACTICAL_CLIENT_ID" ]]; then
        error "TACTICAL_CLIENT_ID não está definido (modo automático)"
    fi

    if [[ -z "$TACTICAL_CLIENT_FILIAL" ]]; then
        error "TACTICAL_CLIENT_FILIAL não está definido (modo automático)"
    fi

    log "Modo automático ativado"
    info "• Cliente ID: $TACTICAL_CLIENT_ID"
    info "• Filial: $TACTICAL_CLIENT_FILIAL"
    info "• Arquitetura: $ARCHITECTURE"
    info "• Sistema: $UBUNTU_VERSION"
fi

# Atualizar sistema
log "Atualizando sistema..."
sudo apt-get update -y

# Instalar dependências básicas
log "Instalando dependências básicas..."
sudo apt-get install -y curl wget unzip build-essential

# Função para download com retry e URLs alternativas
download_tactical_script() {
    local script_name="rmmagent-linux.sh"
    local max_attempts=3
    local wait_time=5

    # URLs alternativas para o script
    local urls=(
        "https://raw.githubusercontent.com/netvolt/LinuxRMM-Script/main/rmmagent-linux.sh"
        "https://github.com/netvolt/LinuxRMM-Script/raw/main/rmmagent-linux.sh"
        "https://raw.githubusercontent.com/netvolt/LinuxRMM-Script/master/rmmagent-linux.sh"
    )

    cd /tmp

    # Remover arquivo anterior se existir
    rm -f "$script_name"

    for url in "${urls[@]}"; do
        log "Tentando baixar de: $url"

        for attempt in $(seq 1 $max_attempts); do
            info "Tentativa $attempt de $max_attempts..."

            if wget --timeout=30 --tries=1 "$url" -O "$script_name" 2>/dev/null; then
                if [[ -f "$script_name" ]] && [[ -s "$script_name" ]]; then
                    log "✅ Script baixado com sucesso!"
                    chmod +x "$script_name"
                    return 0
                else
                    warning "Arquivo baixado está vazio ou corrompido"
                    rm -f "$script_name"
                fi
            else
                warning "Falha no download (tentativa $attempt)"
            fi

            if [[ $attempt -lt $max_attempts ]]; then
                info "Aguardando ${wait_time}s antes da próxima tentativa..."
                sleep $wait_time
                wait_time=$((wait_time + 5))  # Aumentar tempo de espera
            fi
        done

        warning "Falha em todas as tentativas para URL: $url"
    done

    error "Não foi possível baixar o script do Tactical RMM. Verifique sua conectividade com a internet."
}

# Instalar Tactical RMM
log "Instalando Tactical RMM Agent..."

# Baixar script do Tactical RMM com retry
download_tactical_script

info "Instalando Tactical RMM Agent com as configurações fornecidas..."
info "Cliente ID: $TACTICAL_CLIENT_ID"
info "Filial: $TACTICAL_CLIENT_FILIAL"

# Executar instalação do Tactical RMM com os parâmetros corretos baseado na arquitetura
if [[ "$IS_ARM" == "true" ]]; then
    # URL para ARM/Raspberry Pi
    TACTICAL_MESH_URL='https://mesh.centralmesh.nvirtual.com.br/meshagents?id=7Nss2LHe67mTwByGHQ3H3lOI4x8Awfk6kwbQgxSMMq%40qIJKjK6OOSBMWfXBYgPlb&installflags=0&meshinstall=26'
    log "Instalando Tactical RMM para arquitetura ARM..."
else
    # URL para x86_64
    TACTICAL_MESH_URL='https://mesh.centralmesh.nvirtual.com.br/meshagents?id=7Nss2LHe67mTwByGHQ3H3lOI4x8Awfk6kwbQgxSMMq%40qIJKjK6OOSBMWfXBYgPlb&installflags=2&meshinstall=6'
    log "Instalando Tactical RMM para arquitetura x86_64..."
fi

# Executar instalação
./rmmagent-linux.sh install "$TACTICAL_MESH_URL" 'https://api.centralmesh.nvirtual.com.br' "$TACTICAL_CLIENT_ID" "$TACTICAL_CLIENT_FILIAL" 'ecd275ac5baa7e615674a38f2de333f00dd2635e179f9a08e4026db2e5856ae3' 'server'

if [[ $? -eq 0 ]]; then
    log "✅ Tactical RMM Agent instalado com sucesso!"
else
    warning "⚠️ Houve um problema na instalação do Tactical RMM Agent. Verifique os logs."
    info "Para verificar logs: journalctl -u tacticalagent"
fi

# Verificar se o serviço está rodando
log "Verificando status do Tactical RMM Agent..."
sleep 5

if systemctl is-active --quiet tacticalagent 2>/dev/null; then
    log "✅ Tactical RMM Agent está rodando corretamente"
elif systemctl is-active --quiet rmmagent 2>/dev/null; then
    log "✅ RMM Agent está rodando corretamente"
else
    warning "⚠️ Serviço do Tactical RMM pode não estar rodando"
    info "Para verificar status: sudo systemctl status tacticalagent"
    info "Para verificar logs: sudo journalctl -u tacticalagent -f"
fi

# Resumo final
echo
log "Instalação concluída!"
echo "=========================================="
echo "RESUMO DA INSTALAÇÃO"
echo "=========================================="
echo "Tactical RMM Agent:"
echo "• Cliente ID: $TACTICAL_CLIENT_ID"
echo "• Filial: $TACTICAL_CLIENT_FILIAL"
echo "• Mesh Server: mesh.centralmesh.nvirtual.com.br"
echo "• API Server: api.centralmesh.nvirtual.com.br"
echo "• Arquitetura: $ARCHITECTURE"
echo "• Sistema: $UBUNTU_VERSION"
echo
echo "Comandos úteis:"
echo "• Verificar status: sudo systemctl status tacticalagent"
echo "• Ver logs: sudo journalctl -u tacticalagent -f"
echo "• Reiniciar serviço: sudo systemctl restart tacticalagent"
echo
echo "Desenvolvido por: Paulo Matheus - NVirtual"
echo "=========================================="

log "Script finalizado!"
