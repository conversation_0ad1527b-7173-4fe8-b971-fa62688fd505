#!/bin/bash

# Script de demonstração do Tactical RMM Installation Scripts
# Autor: <PERSON>eus - NVirtual
# Data: 2025-01-21
# Versão: 1.0.0

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Função para log
log() {
    echo -e "${GREEN}[DEMO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
}

title() {
    echo -e "${CYAN}$1${NC}"
}

# Banner principal
clear
echo "
████████╗ █████╗  ██████╗████████╗██╗ ██████╗ █████╗ ██╗         ██████╗ ███╗   ███╗███╗   ███╗
╚══██╔══╝██╔══██╗██╔════╝╚══██╔══╝██║██╔════╝██╔══██╗██║         ██╔══██╗████╗ ████║████╗ ████║
   ██║   ███████║██║        ██║   ██║██║     ███████║██║         ██████╔╝██╔████╔██║██╔████╔██║
   ██║   ██╔══██║██║        ██║   ██║██║     ██╔══██║██║         ██╔══██╗██║╚██╔╝██║██║╚██╔╝██║
   ██║   ██║  ██║╚██████╗   ██║   ██║╚██████╗██║  ██║███████╗    ██║  ██║██║ ╚═╝ ██║██║ ╚═╝ ██║
   ╚═╝   ╚═╝  ╚═╝ ╚═════╝   ╚═╝   ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝    ╚═╝  ╚═╝╚═╝     ╚═╝╚═╝     ╚═╝
"
title "                           DEMONSTRAÇÃO - TACTICAL RMM INSTALLER"
title "                              Desenvolvido por Paulo Matheus - NVirtual"
echo

log "Bem-vindo à demonstração do Tactical RMM Installation Scripts!"
echo

# Verificar se os arquivos necessários existem
required_files=(
    "install-tactical-only.sh"
    "uninstall-tactical.sh"
    "check-tactical-status.sh"
    "quick-install.sh"
    "config.example"
    "Makefile"
    "README.md"
)

log "Verificando arquivos do projeto..."
missing_files=()

for file in "${required_files[@]}"; do
    if [[ -f "$file" ]]; then
        info "✅ $file"
    else
        error "❌ $file (AUSENTE)"
        missing_files+=("$file")
    fi
done

if [[ ${#missing_files[@]} -gt 0 ]]; then
    echo
    error "Alguns arquivos estão ausentes. Esta demonstração pode não funcionar corretamente."
    warning "Arquivos ausentes: ${missing_files[*]}"
    echo
    read -p "Deseja continuar mesmo assim? (s/N): " continue_demo
    if [[ ! "$continue_demo" =~ ^[Ss]$ ]]; then
        log "Demonstração cancelada."
        exit 1
    fi
fi

echo
log "Todos os arquivos necessários estão presentes!"

# Menu de demonstração
show_demo_menu() {
    echo
    title "=========================================="
    title "           MENU DE DEMONSTRAÇÃO"
    title "=========================================="
    echo "1. 📋 Mostrar informações do projeto"
    echo "2. 🔧 Demonstrar configuração inicial"
    echo "3. 📝 Mostrar exemplo de arquivo de configuração"
    echo "4. 🚀 Simular instalação (modo dry-run)"
    echo "5. 📊 Demonstrar verificação de status"
    echo "6. 🌐 Testar conectividade com servidores"
    echo "7. 📖 Mostrar comandos do Makefile"
    echo "8. 🧪 Executar testes de sintaxe"
    echo "9. 📄 Mostrar documentação"
    echo "0. 🚪 Sair"
    echo
}

# Função 1: Informações do projeto
show_project_info() {
    title "📋 INFORMAÇÕES DO PROJETO"
    echo "=========================================="
    echo
    info "Nome: Tactical RMM Installation Scripts"
    info "Autor: Paulo Matheus - NVirtual"
    info "Versão: 1.0.0"
    info "Data: 2025-01-21"
    echo
    info "Descrição:"
    echo "  Este projeto fornece scripts dedicados para instalação,"
    echo "  desinstalação e monitoramento do Tactical RMM Agent"
    echo "  em sistemas Linux, separado do projeto Zabbix."
    echo
    info "Características principais:"
    echo "  ✅ Instalação dedicada apenas do Tactical RMM"
    echo "  ✅ Suporte multi-arquitetura (x86_64 e ARM)"
    echo "  ✅ Suporte multi-sistema (Ubuntu, Debian)"
    echo "  ✅ Interface interativa e modo automático"
    echo "  ✅ Scripts de verificação e monitoramento"
    echo "  ✅ Makefile para facilitar o uso"
    echo
}

# Função 2: Demonstrar configuração inicial
demo_setup() {
    title "🔧 DEMONSTRAÇÃO - CONFIGURAÇÃO INICIAL"
    echo "=========================================="
    echo
    
    log "Simulando configuração inicial..."
    
    info "1. Verificando permissões dos scripts..."
    for script in install-tactical-only.sh uninstall-tactical.sh check-tactical-status.sh quick-install.sh; do
        if [[ -f "$script" ]]; then
            if [[ -x "$script" ]]; then
                info "   ✅ $script (executável)"
            else
                warning "   ⚠️  $script (sem permissão de execução)"
                info "   Comando para corrigir: chmod +x $script"
            fi
        fi
    done
    
    echo
    info "2. Verificando arquivo de configuração..."
    if [[ -f "config" ]]; then
        info "   ✅ Arquivo 'config' existe"
    else
        warning "   ⚠️  Arquivo 'config' não existe"
        if [[ -f "config.example" ]]; then
            info "   ✅ Arquivo 'config.example' disponível"
            info "   Comando para criar: cp config.example config"
        fi
    fi
    
    echo
    info "3. Comandos de configuração recomendados:"
    echo "   make setup           # Configurar tudo automaticamente"
    echo "   make permissions     # Apenas permissões"
    echo "   make config-edit     # Editar configuração"
    echo
}

# Função 3: Mostrar exemplo de configuração
show_config_example() {
    title "📝 EXEMPLO DE ARQUIVO DE CONFIGURAÇÃO"
    echo "=========================================="
    echo
    
    if [[ -f "config.example" ]]; then
        info "Conteúdo do arquivo 'config.example':"
        echo
        echo "$(cat config.example | head -30)"
        echo "..."
        echo
        info "Para usar:"
        echo "  1. cp config.example config"
        echo "  2. Editar o arquivo 'config'"
        echo "  3. Definir TACTICAL_CLIENT_ID e TACTICAL_CLIENT_FILIAL"
        echo "  4. make quick-install"
    else
        error "Arquivo 'config.example' não encontrado!"
    fi
    echo
}

# Função 4: Simular instalação
simulate_installation() {
    title "🚀 SIMULAÇÃO DE INSTALAÇÃO"
    echo "=========================================="
    echo
    
    warning "MODO DEMONSTRAÇÃO - Nenhuma instalação real será feita"
    echo
    
    info "Passos que seriam executados:"
    echo "  1. ✅ Detectar sistema operacional e arquitetura"
    echo "  2. ✅ Verificar privilégios sudo"
    echo "  3. ✅ Solicitar ID do Cliente e Filial"
    echo "  4. ✅ Atualizar sistema (apt update/upgrade)"
    echo "  5. ✅ Instalar dependências básicas"
    echo "  6. ✅ Baixar script do Tactical RMM"
    echo "  7. ✅ Executar instalação do agent"
    echo "  8. ✅ Verificar status dos serviços"
    echo "  9. ✅ Mostrar resumo da instalação"
    echo
    
    info "Exemplo de uso real:"
    echo "  # Modo interativo"
    echo "  ./install-tactical-only.sh"
    echo
    echo "  # Modo automático"
    echo "  export TACTICAL_CLIENT_ID='123'"
    echo "  export TACTICAL_CLIENT_FILIAL='Matriz'"
    echo "  ./install-tactical-only.sh --auto"
    echo
    echo "  # Usando Makefile"
    echo "  make install        # Interativo"
    echo "  make quick-install  # Automático com config"
    echo
}

# Função 5: Demonstrar verificação de status
demo_status_check() {
    title "📊 DEMONSTRAÇÃO - VERIFICAÇÃO DE STATUS"
    echo "=========================================="
    echo
    
    info "O script check-tactical-status.sh oferece várias opções:"
    echo
    echo "  1. Verificação completa"
    echo "  2. Status de serviços"
    echo "  3. Processos em execução"
    echo "  4. Arquivos de instalação"
    echo "  5. Conectividade com servidores"
    echo "  6. Logs recentes"
    echo "  7. Informações do sistema"
    echo "  8. Monitoramento contínuo"
    echo
    
    info "Comandos disponíveis:"
    echo "  ./check-tactical-status.sh           # Menu interativo"
    echo "  ./check-tactical-status.sh 1         # Verificação completa"
    echo "  ./check-tactical-status.sh 2         # Apenas serviços"
    echo "  make status                          # Via Makefile"
    echo "  make status-services                 # Apenas serviços"
    echo "  make logs                            # Logs recentes"
    echo "  make monitor                         # Tempo real"
    echo
    
    if [[ -f "check-tactical-status.sh" ]]; then
        info "Executando verificação de conectividade como exemplo..."
        echo
        chmod +x check-tactical-status.sh 2>/dev/null
        # Simular verificação de conectividade
        echo "🌐 Testando conectividade..."
        echo "  mesh.centralmesh.nvirtual.com.br: $(curl -I https://mesh.centralmesh.nvirtual.com.br --connect-timeout 5 >/dev/null 2>&1 && echo "✅ OK" || echo "❌ FALHA")"
        echo "  api.centralmesh.nvirtual.com.br:  $(curl -I https://api.centralmesh.nvirtual.com.br --connect-timeout 5 >/dev/null 2>&1 && echo "✅ OK" || echo "❌ FALHA")"
    fi
    echo
}

# Função 6: Testar conectividade
test_connectivity() {
    title "🌐 TESTE DE CONECTIVIDADE"
    echo "=========================================="
    echo
    
    log "Testando conectividade com servidores da NVirtual..."
    echo
    
    servers=(
        "mesh.centralmesh.nvirtual.com.br"
        "api.centralmesh.nvirtual.com.br"
    )
    
    for server in "${servers[@]}"; do
        echo -n "🔍 $server: "
        
        if curl -I "https://$server" --connect-timeout 10 >/dev/null 2>&1; then
            echo -e "${GREEN}✅ OK${NC}"
        else
            echo -e "${RED}❌ FALHA${NC}"
        fi
    done
    
    echo
    info "Teste de DNS:"
    for server in "${servers[@]}"; do
        echo -n "🔍 DNS $server: "
        if nslookup "$server" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ OK${NC}"
        else
            echo -e "${RED}❌ FALHA${NC}"
        fi
    done
    echo
}

# Função 7: Mostrar comandos do Makefile
show_makefile_commands() {
    title "📖 COMANDOS DO MAKEFILE"
    echo "=========================================="
    echo
    
    if [[ -f "Makefile" ]]; then
        info "Executando 'make help'..."
        echo
        make help 2>/dev/null || {
            warning "Não foi possível executar 'make help'"
            info "Comandos principais disponíveis:"
            echo "  make setup             # Configurar ambiente"
            echo "  make install           # Instalação interativa"
            echo "  make quick-install     # Instalação rápida"
            echo "  make status            # Verificar status"
            echo "  make uninstall         # Desinstalar"
            echo "  make config-edit       # Editar configuração"
            echo "  make test-connectivity # Testar conectividade"
            echo "  make clean             # Limpar temporários"
        }
    else
        error "Arquivo 'Makefile' não encontrado!"
    fi
    echo
}

# Função 8: Testes de sintaxe
run_syntax_tests() {
    title "🧪 TESTES DE SINTAXE"
    echo "=========================================="
    echo
    
    log "Executando testes de sintaxe nos scripts..."
    echo
    
    scripts=(
        "install-tactical-only.sh"
        "uninstall-tactical.sh"
        "check-tactical-status.sh"
        "quick-install.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [[ -f "$script" ]]; then
            echo -n "🔍 $script: "
            if bash -n "$script" 2>/dev/null; then
                echo -e "${GREEN}✅ OK${NC}"
            else
                echo -e "${RED}❌ ERRO DE SINTAXE${NC}"
            fi
        else
            echo -e "🔍 $script: ${YELLOW}⚠️  ARQUIVO NÃO ENCONTRADO${NC}"
        fi
    done
    
    echo
    info "Todos os scripts com sintaxe OK estão prontos para uso!"
    echo
}

# Função 9: Mostrar documentação
show_documentation() {
    title "📄 DOCUMENTAÇÃO"
    echo "=========================================="
    echo
    
    if [[ -f "README.md" ]]; then
        info "Primeiras linhas do README.md:"
        echo
        head -20 README.md
        echo "..."
        echo
        info "Para ver a documentação completa:"
        echo "  cat README.md"
        echo "  less README.md"
        echo "  ou abra o arquivo em um editor de texto"
    else
        error "Arquivo 'README.md' não encontrado!"
    fi
    echo
}

# Loop principal do menu
while true; do
    show_demo_menu
    read -p "Escolha uma opção (0-9): " choice
    echo
    
    case $choice in
        1) show_project_info ;;
        2) demo_setup ;;
        3) show_config_example ;;
        4) simulate_installation ;;
        5) demo_status_check ;;
        6) test_connectivity ;;
        7) show_makefile_commands ;;
        8) run_syntax_tests ;;
        9) show_documentation ;;
        0) 
            log "Obrigado por usar a demonstração!"
            info "Para usar os scripts reais:"
            echo "  make setup      # Configurar ambiente"
            echo "  make install    # Instalar Tactical RMM"
            echo
            exit 0
            ;;
        *)
            error "Opção inválida! Escolha um número de 0 a 9."
            ;;
    esac
    
    echo
    read -p "Pressione Enter para continuar..."
    clear
done
