#!/bin/bash
# Script para testar URLs do repositorio oficial Zabbix
# Autor: <PERSON> - NVirtual

# URLs para testar
URLS=(
    # Ubuntu DEB packages
    "https://repo.zabbix.com/zabbix/7.0/ubuntu/pool/main/z/zabbix/zabbix-agent_7.0.10-1+ubuntu22.04_amd64.deb"
    "https://repo.zabbix.com/zabbix/7.0/ubuntu/pool/main/z/zabbix/zabbix-agent_7.0.10-1+ubuntu22.04_arm64.deb"
    "https://repo.zabbix.com/zabbix/7.0/ubuntu/pool/main/z/zabbix/zabbix-agent_7.0.10-1+ubuntu20.04_amd64.deb"
    
    # Debian DEB packages
    "https://repo.zabbix.com/zabbix/7.0/debian/pool/main/z/zabbix/zabbix-agent_7.0.10-1+debian12_amd64.deb"
    "https://repo.zabbix.com/zabbix/7.0/debian/pool/main/z/zabbix/zabbix-agent_7.0.10-1+debian12_arm64.deb"
    "https://repo.zabbix.com/zabbix/7.0/debian/pool/main/z/zabbix/zabbix-agent_7.0.10-1+debian11_amd64.deb"
    
    # RHEL/CentOS RPM packages
    "https://repo.zabbix.com/zabbix/7.0/rhel/9/x86_64/zabbix-agent-7.0.10-release1.el9.x86_64.rpm"
    "https://repo.zabbix.com/zabbix/7.0/rhel/9/aarch64/zabbix-agent-7.0.10-release1.el9.aarch64.rpm"
    "https://repo.zabbix.com/zabbix/7.0/rhel/8/x86_64/zabbix-agent-7.0.10-release1.el8.x86_64.rpm"
    "https://repo.zabbix.com/zabbix/7.0/centos/8/x86_64/zabbix-agent-7.0.10-release1.el8.x86_64.rpm"
)

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "SUCCESS")
            echo -e "[$timestamp] [${GREEN}SUCCESS${NC}] $message"
            ;;
        "WARNING")
            echo -e "[$timestamp] [${YELLOW}WARNING${NC}] $message"
            ;;
        "ERROR")
            echo -e "[$timestamp] [${RED}ERROR${NC}] $message"
            ;;
        *)
            echo -e "[$timestamp] [INFO] $message"
            ;;
    esac
}

test_url() {
    local url="$1"
    
    # Testar com curl primeiro
    if command -v curl > /dev/null 2>&1; then
        local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 --max-time 30 "$url")
        if [ "$response" = "200" ]; then
            return 0
        fi
    fi
    
    # Testar com wget
    if command -v wget > /dev/null 2>&1; then
        if wget --timeout=10 --tries=1 --spider "$url" 2>/dev/null; then
            return 0
        fi
    fi
    
    return 1
}

get_file_info() {
    local url="$1"
    
    if command -v curl > /dev/null 2>&1; then
        local size=$(curl -s -I "$url" | grep -i content-length | awk '{print $2}' | tr -d '\r')
        if [ ! -z "$size" ]; then
            local size_mb=$((size / 1024 / 1024))
            echo "${size_mb}MB"
        else
            echo "Tamanho desconhecido"
        fi
    else
        echo "N/A"
    fi
}

main() {
    log_message "SUCCESS" "=== TESTE DE URLs DO REPOSITORIO OFICIAL ZABBIX ==="
    
    # Verificar ferramentas disponíveis
    if command -v curl > /dev/null 2>&1; then
        log_message "SUCCESS" "curl disponivel"
    else
        log_message "WARNING" "curl nao disponivel"
    fi
    
    if command -v wget > /dev/null 2>&1; then
        log_message "SUCCESS" "wget disponivel"
    else
        log_message "WARNING" "wget nao disponivel"
    fi
    
    echo ""
    
    # Testar cada URL
    local working_urls=0
    local total_urls=${#URLS[@]}
    
    for url in "${URLS[@]}"; do
        local filename=$(basename "$url")
        log_message "INFO" "Testando: $filename"
        log_message "INFO" "URL: $url"
        
        if test_url "$url"; then
            local file_size=$(get_file_info "$url")
            log_message "SUCCESS" "  Status: OK - Tamanho: $file_size"
            ((working_urls++))
        else
            log_message "ERROR" "  Status: FALHOU (404 ou timeout)"
        fi
        
        echo ""
    done
    
    # Resumo
    log_message "SUCCESS" "=== RESUMO ==="
    log_message "INFO" "URLs testadas: $total_urls"
    log_message "INFO" "URLs funcionando: $working_urls"
    log_message "INFO" "Taxa de sucesso: $(( working_urls * 100 / total_urls ))%"
    
    if [ $working_urls -gt 0 ]; then
        log_message "SUCCESS" "Encontradas URLs funcionais do repositorio Zabbix"
        
        echo ""
        log_message "INFO" "URLs FUNCIONAIS ENCONTRADAS:"
        for url in "${URLS[@]}"; do
            if test_url "$url"; then
                log_message "SUCCESS" "  $(basename "$url")"
                log_message "INFO" "    $url"
            fi
        done
        
        exit 0
    else
        log_message "ERROR" "Nenhuma URL do repositorio Zabbix esta funcionando"
        log_message "ERROR" "Verifique conectividade com repo.zabbix.com"
        exit 1
    fi
}

main
