#!/usr/bin/env python3

"""
Gerador de CSV para Clientes e Máquinas
Script específico para gerar CSV mostrando unidades de clientes e suas máquinas
Autor: Paulo Matheus
Data: 2025-01-08
"""

import argparse
import sys
import os
from datetime import datetime

# Adicionar o diretório atual ao path para importar o scanner
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from network_scanner import NetworkScanner
except ImportError:
    print("❌ Erro: Não foi possível importar NetworkScanner")
    print("💡 Certifique-se de que o arquivo network_scanner.py está no mesmo diretório")
    sys.exit(1)

def print_banner():
    """Exibe banner do gerador CSV"""
    print("=" * 80)
    print("📊 GERADOR DE CSV - CLIENTES E MÁQUINAS")
    print("=" * 80)
    print("🏢 Gera CSV com unidades de clientes e suas máquinas")
    print("📋 Formatos disponíveis:")
    print("   • CSV Detalhado: Uma linha por máquina")
    print("   • CSV Resumo: Uma linha por cliente")
    print("=" * 80)

def generate_client_csv(network, output_format="both", output_name=None, threads=100, timeout=2):
    """
    Gera CSV com dados de clientes e máquinas
    
    Args:
        network: Rede para escanear (ex: ***********/16)
        output_format: "detailed", "summary" ou "both"
        output_name: Nome base dos arquivos (opcional)
        threads: Número de threads para o scan
        timeout: Timeout em segundos
    """
    
    print(f"🌐 Iniciando scan da rede: {network}")
    print(f"⚡ Threads: {threads} | Timeout: {timeout}s")
    print(f"📊 Formato de saída: {output_format}")
    
    try:
        # Criar scanner
        scanner = NetworkScanner(
            network=network,
            max_workers=threads,
            timeout=timeout
        )
        
        # Executar scan
        scanner.scan_network()
        
        if not scanner.results:
            print("\n⚠️  Nenhum dispositivo encontrado na rede especificada.")
            return False
        
        print(f"\n✅ Scan concluído! {len(scanner.results)} dispositivos encontrados.")
        
        # Gerar arquivos CSV conforme solicitado
        success = True
        
        if output_format in ["detailed", "both"]:
            print("\n📊 Gerando CSV detalhado (uma linha por máquina)...")
            if output_name:
                filename = f"{output_name}_detalhado.csv"
            else:
                filename = None
            
            if scanner.save_to_csv(filename):
                print("✅ CSV detalhado gerado com sucesso!")
            else:
                success = False
        
        if output_format in ["summary", "both"]:
            print("\n📊 Gerando CSV resumo (uma linha por cliente)...")
            if output_name:
                filename = f"{output_name}_resumo.csv"
            else:
                filename = None
            
            if scanner.save_client_summary_csv(filename):
                print("✅ CSV resumo gerado com sucesso!")
            else:
                success = False
        
        return success
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Operação interrompida pelo usuário")
        return False
    except Exception as e:
        print(f"\n❌ Erro durante o scan: {e}")
        return False

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(
        description='Gerador de CSV para Clientes e Máquinas',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos de uso:

  # Scan básico com ambos os formatos CSV
  python generate_client_csv.py -n ***********/24

  # Apenas CSV detalhado
  python generate_client_csv.py -n ***********/16 -f detailed

  # Apenas CSV resumo
  python generate_client_csv.py -n ***********/24 -f summary

  # Com nome personalizado
  python generate_client_csv.py -n ***********/24 -o "empresa_2025"

  # Scan rápido com menos threads
  python generate_client_csv.py -n ***********/24 -t 50 --timeout 1

Formatos de CSV:
  • detailed: Uma linha por máquina encontrada
  • summary: Uma linha por cliente (unidade)
  • both: Gera ambos os formatos (padrão)
        """
    )
    
    parser.add_argument('--network', '-n', required=True,
                       help='Rede para escanear (ex: ***********/24 ou ***********/16)')
    parser.add_argument('--format', '-f', choices=['detailed', 'summary', 'both'], 
                       default='both', help='Formato do CSV (padrão: both)')
    parser.add_argument('--output', '-o', type=str,
                       help='Nome base dos arquivos CSV (sem extensão)')
    parser.add_argument('--threads', '-t', type=int, default=100,
                       help='Número de threads (padrão: 100)')
    parser.add_argument('--timeout', type=int, default=2,
                       help='Timeout em segundos (padrão: 2)')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Modo silencioso (menos output)')
    
    args = parser.parse_args()
    
    if not args.quiet:
        print_banner()
    
    # Validar rede
    try:
        import ipaddress
        ipaddress.IPv4Network(args.network)
    except ValueError:
        print(f"❌ Erro: Formato de rede inválido: {args.network}")
        print("💡 Use formato CIDR, ex: ***********/24 ou ***********/16")
        sys.exit(1)
    
    # Gerar CSV
    success = generate_client_csv(
        network=args.network,
        output_format=args.format,
        output_name=args.output,
        threads=args.threads,
        timeout=args.timeout
    )
    
    if success:
        print(f"\n🎉 Processo concluído com sucesso!")
        print(f"📁 Arquivos CSV gerados no diretório atual")
        
        if not args.quiet:
            print(f"\n💡 Dicas:")
            print(f"   • Abra os arquivos CSV no Excel ou LibreOffice")
            print(f"   • Use filtros para analisar dados por cliente")
            print(f"   • O CSV detalhado mostra todas as máquinas")
            print(f"   • O CSV resumo mostra estatísticas por cliente")
    else:
        print(f"\n❌ Processo finalizado com erros")
        sys.exit(1)

if __name__ == "__main__":
    main()
