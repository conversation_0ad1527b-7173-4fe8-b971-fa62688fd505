#!/usr/bin/env python3
"""
Tactical RMM Inventory Export Script
====================================

Script Python para exportar inventário de estações do Tactical RMM.
Pode ser executado diretamente via Tactical RMM como script Python.

Autor: NVirtual
Data: 2025-01-02
Versão: 1.0
"""

import requests
import json
import csv
import sys
import os
from datetime import datetime, timedelta
import argparse

# Configurações da API
API_URL = "https://api.centralmesh.nvirtual.com.br"
API_TOKEN = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"  # Token padrão - pode ser sobrescrito

def log_message(message, level="INFO"):
    """Função para logging com timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    levels = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "ERROR": "❌",
        "WARN": "⚠️"
    }
    icon = levels.get(level, "📝")
    print(f"[{timestamp}] {icon} {message}")

def get_api_data(endpoint, token=None):
    """Busca dados da API do Tactical RMM"""
    if not token:
        token = API_TOKEN
    
    headers = {
        "X-API-KEY": token,
        "Content-Type": "application/json"
    }
    
    try:
        url = f"{API_URL}/{endpoint}/"
        log_message(f"Buscando dados de: {endpoint}")
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        log_message(f"Encontrados {len(data)} registros em {endpoint}", "SUCCESS")
        return data
        
    except requests.exceptions.RequestException as e:
        log_message(f"Erro ao buscar {endpoint}: {str(e)}", "ERROR")
        return []

def calculate_agent_status(last_seen):
    """Calcula o status do agente baseado no último contato"""
    if not last_seen:
        return "Desconhecido"
    
    try:
        # Parse da data (formato ISO)
        last_seen_dt = datetime.fromisoformat(last_seen.replace('Z', '+00:00'))
        now = datetime.now(last_seen_dt.tzinfo)
        diff = now - last_seen_dt
        
        if diff.total_seconds() < 300:  # 5 minutos
            return "Online"
        elif diff.total_seconds() < 86400:  # 24 horas
            return "Recente"
        else:
            return "Offline"
    except:
        return "Desconhecido"

def format_bytes_to_gb(bytes_value):
    """Converte bytes para GB"""
    if not bytes_value:
        return "N/A"
    try:
        return round(int(bytes_value) / (1024**3), 2)
    except:
        return "N/A"

def format_agent_data(agent, clients_dict, sites_dict):
    """Formata dados do agente para exportação"""
    
    # Buscar informações do cliente e site
    client_name = clients_dict.get(agent.get('client'), 'N/A')
    site_name = sites_dict.get(agent.get('site'), 'N/A')
    
    # Calcular status
    status = calculate_agent_status(agent.get('last_seen'))
    
    # Formatar datas
    last_seen = "N/A"
    if agent.get('last_seen'):
        try:
            dt = datetime.fromisoformat(agent.get('last_seen').replace('Z', '+00:00'))
            last_seen = dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            last_seen = agent.get('last_seen', 'N/A')
    
    boot_time = "N/A"
    if agent.get('boot_time'):
        try:
            dt = datetime.fromisoformat(agent.get('boot_time').replace('Z', '+00:00'))
            boot_time = dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            boot_time = agent.get('boot_time', 'N/A')
    
    install_time = "N/A"
    if agent.get('install_time'):
        try:
            dt = datetime.fromisoformat(agent.get('install_time').replace('Z', '+00:00'))
            install_time = dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            install_time = agent.get('install_time', 'N/A')
    
    return {
        'ID_Agente': agent.get('agent_id', 'N/A'),
        'Hostname': agent.get('hostname', 'N/A'),
        'Cliente': client_name,
        'Site': site_name,
        'Status': status,
        'Sistema_Operacional': agent.get('operating_system', 'N/A'),
        'Versao_OS': agent.get('plat', 'N/A'),
        'Arquitetura': agent.get('arch', 'N/A'),
        'IP_Publico': agent.get('public_ip', 'N/A'),
        'Agente_Versao': agent.get('version', 'N/A'),
        'Ultimo_Contato': last_seen,
        'Tempo_Boot': boot_time,
        'CPU_Modelo': agent.get('cpu_model', 'N/A'),
        'RAM_Total_GB': format_bytes_to_gb(agent.get('total_ram')),
        'RAM_Usado_GB': format_bytes_to_gb(agent.get('used_ram')),
        'Antivirus': agent.get('antivirus', 'N/A'),
        'Dominio': agent.get('domain', 'N/A'),
        'Usuario_Logado': agent.get('logged_in_username', 'N/A'),
        'Servicos_Falhas': agent.get('services_failing', 0),
        'Checks_Falhas': agent.get('checks_failing', 0),
        'Manutencao': "Sim" if agent.get('maintenance_mode') else "Não",
        'Monitoramento': agent.get('monitoring_type', 'N/A'),
        'Data_Instalacao': install_time,
        'Observacoes': agent.get('description', '')
    }

def export_to_csv(data, filename):
    """Exporta dados para arquivo CSV"""
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            if not data:
                log_message("Nenhum dado para exportar", "WARN")
                return False
            
            fieldnames = data[0].keys()
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for row in data:
                writer.writerow(row)
        
        log_message(f"Dados exportados para: {filename}", "SUCCESS")
        return True
        
    except Exception as e:
        log_message(f"Erro ao exportar CSV: {str(e)}", "ERROR")
        return False

def export_to_json(data, filename):
    """Exporta dados para arquivo JSON"""
    try:
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, indent=2, ensure_ascii=False)
        
        log_message(f"Dados exportados para: {filename}", "SUCCESS")
        return True
        
    except Exception as e:
        log_message(f"Erro ao exportar JSON: {str(e)}", "ERROR")
        return False

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description='Exportar inventário do Tactical RMM')
    parser.add_argument('--token', help='Token de API do Tactical RMM')
    parser.add_argument('--client-id', type=int, help='ID do cliente específico')
    parser.add_argument('--site-id', type=int, help='ID do site específico')
    parser.add_argument('--format', choices=['csv', 'json', 'both'], default='csv', 
                       help='Formato de saída (padrão: csv)')
    parser.add_argument('--output', help='Caminho do arquivo de saída (sem extensão)')
    parser.add_argument('--include-offline', action='store_true', default=True,
                       help='Incluir agentes offline')
    parser.add_argument('--verbose', action='store_true', help='Saída detalhada')
    
    args = parser.parse_args()
    
    # Usar token fornecido ou padrão
    token = args.token or API_TOKEN
    
    log_message("=== INICIANDO EXPORTAÇÃO DE INVENTÁRIO TACTICAL RMM ===", "SUCCESS")
    
    try:
        # Buscar dados da API
        log_message("Buscando dados da API...")
        agents = get_api_data("agents", token)
        clients = get_api_data("clients", token)
        sites = get_api_data("clients/sites", token)
        
        if not agents:
            log_message("Nenhum agente encontrado", "ERROR")
            return 1
        
        # Criar dicionários para lookup rápido
        clients_dict = {client['id']: client['name'] for client in clients}
        sites_dict = {site['id']: site['name'] for site in sites}
        
        # Filtrar por cliente se especificado
        if args.client_id:
            agents = [agent for agent in agents if agent.get('client') == args.client_id]
            log_message(f"Filtrado para cliente ID: {args.client_id} ({len(agents)} agentes)")
        
        # Filtrar por site se especificado
        if args.site_id:
            agents = [agent for agent in agents if agent.get('site') == args.site_id]
            log_message(f"Filtrado para site ID: {args.site_id} ({len(agents)} agentes)")
        
        # Filtrar agentes offline se necessário
        if not args.include_offline:
            online_agents = []
            for agent in agents:
                status = calculate_agent_status(agent.get('last_seen'))
                if status in ['Online', 'Recente']:
                    online_agents.append(agent)
            agents = online_agents
            log_message(f"Filtrado agentes offline ({len(agents)} agentes restantes)")
        
        if not agents:
            log_message("Nenhum agente encontrado com os filtros especificados", "WARN")
            return 0
        
        # Processar dados dos agentes
        log_message(f"Processando dados de {len(agents)} agentes...")
        inventory_data = []
        
        for agent in agents:
            formatted_agent = format_agent_data(agent, clients_dict, sites_dict)
            inventory_data.append(formatted_agent)
        
        # Gerar nome do arquivo
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = args.output or f"TacticalRMM_Inventario_{timestamp}"
        
        if args.client_id:
            base_filename += f"_Cliente{args.client_id}"
        if args.site_id:
            base_filename += f"_Site{args.site_id}"
        
        # Exportar dados
        success = True
        if args.format in ['csv', 'both']:
            csv_filename = f"{base_filename}.csv"
            success &= export_to_csv(inventory_data, csv_filename)
        
        if args.format in ['json', 'both']:
            json_filename = f"{base_filename}.json"
            success &= export_to_json(inventory_data, json_filename)
        
        if success:
            log_message("Exportação concluída com sucesso!", "SUCCESS")
            log_message(f"Total de registros: {len(inventory_data)}", "SUCCESS")
            
            # Estatísticas resumidas
            online_count = sum(1 for item in inventory_data if item['Status'] == 'Online')
            offline_count = sum(1 for item in inventory_data if item['Status'] == 'Offline')
            recent_count = sum(1 for item in inventory_data if item['Status'] == 'Recente')
            
            log_message("=== ESTATÍSTICAS ===", "SUCCESS")
            log_message(f"Online: {online_count} | Recente: {recent_count} | Offline: {offline_count}")
            
            return 0
        else:
            log_message("Erro durante a exportação", "ERROR")
            return 1
            
    except Exception as e:
        log_message(f"Erro durante a exportação: {str(e)}", "ERROR")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
