#!/usr/bin/env python3

"""
Quick Network Scanner - Scan rápido de sub-redes
Para scans mais rápidos em redes menores
Autor: <PERSON>
"""

import subprocess
import ipaddress
import socket
import sys
import time
from concurrent.futures import ThreadPoolExecutor
import arg<PERSON><PERSON>

def ping_host(ip, timeout=1):
    """Ping rápido para verificar se host está ativo"""
    try:
        if sys.platform.startswith('win'):
            cmd = ['ping', '-n', '1', '-w', str(timeout * 1000), str(ip)]
        else:
            cmd = ['ping', '-c', '1', '-W', str(timeout), str(ip)]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout + 1)
        return result.returncode == 0
    except:
        return False

def get_hostname(ip):
    """Resolve hostname"""
    try:
        return socket.gethostbyaddr(str(ip))[0]
    except:
        return None

def quick_port_scan(ip, ports=[22, 80, 135, 139, 443, 445, 3389]):
    """Scan rápido de portas principais"""
    open_ports = []
    for port in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(0.3)
            if sock.connect_ex((str(ip), port)) == 0:
                open_ports.append(port)
            sock.close()
        except:
            pass
    return open_ports

def identify_device(ip, hostname, open_ports):
    """Identificação rápida do dispositivo"""
    device_info = {
        'ip': str(ip),
        'hostname': hostname or 'N/A',
        'type': 'Unknown',
        'os': 'Unknown',
        'ports': open_ports
    }
    
    # Identificação baseada em portas
    if 3389 in open_ports:
        device_info['type'] = 'Windows Server/Desktop'
        device_info['os'] = 'Windows'
    elif 445 in open_ports or 135 in open_ports:
        device_info['type'] = 'Windows Machine'
        device_info['os'] = 'Windows'
    elif 22 in open_ports:
        device_info['type'] = 'Linux/Unix Server'
        device_info['os'] = 'Linux/Unix'
    elif 80 in open_ports or 443 in open_ports:
        device_info['type'] = 'Web Server'
        device_info['os'] = 'Linux/Windows'
    
    # Refinamento por hostname
    if hostname:
        hostname_lower = hostname.lower()
        if any(x in hostname_lower for x in ['router', 'switch', 'gateway']):
            device_info['type'] = 'Network Device'
            device_info['os'] = 'Embedded/Custom'
        elif 'printer' in hostname_lower:
            device_info['type'] = 'Printer'
            device_info['os'] = 'Embedded'
    
    return device_info

def scan_host(ip, timeout=1):
    """Scan completo de um host"""
    if not ping_host(ip, timeout):
        return None
    
    hostname = get_hostname(ip)
    open_ports = quick_port_scan(ip)
    device_info = identify_device(ip, hostname, open_ports)
    
    return device_info

def scan_subnet(network, max_workers=50, timeout=1):
    """Scan de uma sub-rede"""
    print(f"🔍 Escaneando rede: {network}")
    print(f"⚡ Threads: {max_workers}, Timeout: {timeout}s")
    print("-" * 80)
    print(f"{'IP':<15} | {'Hostname':<25} | {'Tipo':<20} | {'OS':<15} | {'Portas'}")
    print("-" * 80)
    
    network_obj = ipaddress.IPv4Network(network)
    results = []
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {executor.submit(scan_host, ip, timeout): ip for ip in network_obj.hosts()}
        
        for future in futures:
            try:
                result = future.result()
                if result:
                    results.append(result)
                    ports_str = ','.join(map(str, result['ports'])) if result['ports'] else 'None'
                    print(f"{result['ip']:<15} | {result['hostname']:<25} | {result['type']:<20} | {result['os']:<15} | {ports_str}")
            except Exception as e:
                pass
    
    end_time = time.time()
    
    # Resumo
    print("\n" + "=" * 80)
    print(f"📊 Scan concluído em {end_time - start_time:.2f} segundos")
    print(f"🎯 {len(results)} dispositivos encontrados de {network_obj.num_addresses} IPs")
    
    # Estatísticas
    os_count = {}
    type_count = {}
    
    for result in results:
        os_name = result['os']
        device_type = result['type']
        
        os_count[os_name] = os_count.get(os_name, 0) + 1
        type_count[device_type] = type_count.get(device_type, 0) + 1
    
    print("\n🖥️  Sistemas Operacionais:")
    for os_name, count in sorted(os_count.items()):
        print(f"   {os_name}: {count}")
    
    print("\n📱 Tipos de dispositivos:")
    for device_type, count in sorted(type_count.items()):
        print(f"   {device_type}: {count}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description='Quick Network Scanner')
    parser.add_argument('network', nargs='?', default='***********/24',
                       help='Rede para escanear (ex: ***********/24)')
    parser.add_argument('--threads', '-t', type=int, default=50,
                       help='Número de threads (padrão: 50)')
    parser.add_argument('--timeout', type=int, default=1,
                       help='Timeout em segundos (padrão: 1)')
    
    args = parser.parse_args()
    
    try:
        # Validar rede
        ipaddress.IPv4Network(args.network)
        
        print("🌐 QUICK NETWORK SCANNER")
        print("=" * 80)
        
        results = scan_subnet(args.network, args.threads, args.timeout)
        
        # Salvar resultados básicos
        if results:
            filename = f"quick_scan_{args.network.replace('/', '_')}.txt"
            with open(filename, 'w') as f:
                f.write(f"Quick Scan Results - {args.network}\n")
                f.write(f"Scan Time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("-" * 50 + "\n")
                for result in results:
                    f.write(f"{result['ip']} | {result['hostname']} | {result['type']} | {result['os']}\n")
            
            print(f"\n💾 Resultados salvos em: {filename}")
        
    except ValueError:
        print("❌ Rede inválida. Use formato CIDR (ex: ***********/24)")
    except KeyboardInterrupt:
        print("\n⚠️  Scan interrompido pelo usuário")
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    main()
