@import '../../../Styles/responsive';

// .usuarios__wrapper {

//     .table__wrapper,
//     .animation__wrapper {
//         position: relative;
//         // height: 40vh;
//         // display: flex;
//         // flex-direction: column;
//         // align-items: center;
//         // justify-content: center;

//         // @include sm{
//         //     margin: 40px auto 0;
//         //     height: calc(100vh - 30vh);
//         // }

//         h2 {
//             text-align: center;
//             font-size: 30px;
//         }
//     }

//     .table__wrapper {
//         .rdt_TableHeader {
//             position: relative;
//             padding-left: 0;

//             @include max-xs{
//                 display: flex;
//                 justify-content: center;
//                 align-items: center;

//                 .eihLXW{
//                     width: 100%;
//                     text-align: center;
//                 }
//             }
//         }

//         .usuarios__header {
//             position: relative;
//             z-index: 20;

//             a.bt {
//                 width: 100%;
//                 margin: 0;
//             }

//             @include xs{
//                 position: absolute;
//                 top: 0;
//                 right: 0;
//             }
//         }
//     }
// }