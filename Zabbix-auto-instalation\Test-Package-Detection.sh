#!/bin/bash
# Script para testar detecção de tipos de arquivo
# Autor: <PERSON>eus - NVirtual

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "SUCCESS")
            echo -e "[$timestamp] [${GREEN}SUCCESS${NC}] $message"
            ;;
        "WARNING")
            echo -e "[$timestamp] [${YELLOW}WARNING${NC}] $message"
            ;;
        "ERROR")
            echo -e "[$timestamp] [${RED}ERROR${NC}] $message"
            ;;
        *)
            echo -e "[$timestamp] [INFO] $message"
            ;;
    esac
}

# Funcao para detectar tipo de arquivo (copiada do script principal)
detect_package_type() {
    local file_path="$1"
    local filename=$(basename "$file_path")
    
    if [[ "$filename" == *.rpm ]]; then
        echo "rpm"
    elif [[ "$filename" == *.deb ]]; then
        echo "deb"
    elif [[ "$filename" == *.tar.gz ]]; then
        echo "tar.gz"
    else
        echo "unknown"
    fi
}

# Funcao para validar arquivo baixado (copiada do script principal)
validate_downloaded_file() {
    local download_path="$1"
    local filename=$(basename "$download_path")
    
    if [[ "$filename" == *.deb ]] || [[ "$filename" == *.rpm ]]; then
        # Para pacotes DEB/RPM, verificar se nao e HTML de erro
        if file "$download_path" | grep -q "HTML\|text"; then
            log_message "ERROR" "Arquivo baixado e HTML/texto, nao um pacote valido"
            return 1
        else
            log_message "SUCCESS" "Pacote DEB/RPM valido detectado"
            return 0
        fi
    elif [[ "$filename" == *.tar.gz ]]; then
        # Para tar.gz, verificar compressao
        if file "$download_path" | grep -q "gzip compressed"; then
            log_message "SUCCESS" "Arquivo tar.gz valido detectado"
            return 0
        else
            log_message "ERROR" "Arquivo baixado nao e um tar.gz valido"
            return 1
        fi
    else
        # Arquivo desconhecido, aceitar se nao for HTML
        if ! file "$download_path" | grep -q "HTML\|text"; then
            log_message "SUCCESS" "Arquivo binario valido detectado"
            return 0
        else
            log_message "ERROR" "Arquivo baixado parece ser HTML/texto"
            return 1
        fi
    fi
}

main() {
    log_message "SUCCESS" "=== TESTE DE DETECÇÃO DE TIPOS DE ARQUIVO ==="
    
    # Testar arquivos existentes em /tmp
    local test_files=(
        "/tmp/zabbix-agent_7.0.10-1+ubuntu22.04_amd64.deb"
        "/tmp/zabbix_agent-7.0.10-linux-3.0-amd64-static.tar.gz"
        "/tmp/test.rpm"
        "/tmp/test.txt"
    )
    
    for file_path in "${test_files[@]}"; do
        if [ -f "$file_path" ]; then
            log_message "INFO" "Testando arquivo: $(basename "$file_path")"
            
            # Testar detecção de tipo
            local detected_type=$(detect_package_type "$file_path")
            log_message "INFO" "  Tipo detectado: $detected_type"
            
            # Testar validação
            if validate_downloaded_file "$file_path"; then
                log_message "SUCCESS" "  Validação: OK"
            else
                log_message "ERROR" "  Validação: FALHOU"
            fi
            
            # Mostrar informações do arquivo
            log_message "INFO" "  Tipo real: $(file "$file_path")"
            local file_size=$(stat -c%s "$file_path" 2>/dev/null || stat -f%z "$file_path" 2>/dev/null)
            local file_size_kb=$((file_size / 1024))
            log_message "INFO" "  Tamanho: ${file_size_kb}KB"
            
            echo ""
        else
            log_message "WARNING" "Arquivo não encontrado: $file_path"
        fi
    done
    
    # Testar nomes de arquivo específicos
    log_message "INFO" "=== TESTE DE DETECÇÃO POR NOME ==="
    local test_names=(
        "zabbix-agent_7.0.10-1+ubuntu22.04_amd64.deb"
        "zabbix-agent-7.0.10-release1.el9.x86_64.rpm"
        "zabbix_agent-7.0.10-linux-amd64-static.tar.gz"
        "arquivo_desconhecido.bin"
    )
    
    for name in "${test_names[@]}"; do
        local detected_type=$(detect_package_type "/tmp/$name")
        log_message "INFO" "Nome: $name -> Tipo: $detected_type"
    done
    
    log_message "SUCCESS" "=== TESTE CONCLUÍDO ==="
}

main
