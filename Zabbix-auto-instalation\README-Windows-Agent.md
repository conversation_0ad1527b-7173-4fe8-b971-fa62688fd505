# Instalação do Zabbix Agent 2 para Windows via Tactical RMM

Este script automatiza a instalação do Zabbix Agent 2 em estações Windows através do Tactical RMM.

## 📋 Pré-requisitos

- Windows 7/8/10/11 ou Windows Server 2012/2016/2019/2022
- PowerShell 5.0 ou superior
- Conexão com a internet para download do instalador
- Privilégios de administrador (o Tactical RMM executa como SYSTEM)

## 🚀 Como usar no Tactical RMM

### 1. Upload do Script
1. Faça upload do arquivo `Install-ZabbixAgent2-Windows.ps1` no Tactical RMM
2. Configure como script PowerShell

### 2. Configuração dos Argumentos

No campo **"Script Arguments"** do Tactical RMM, use uma das opções abaixo:

#### Opção 1: Apenas IP do servidor (usa nome do computador como hostname)
```powershell
-ZabbixServerIP "*************"
```

#### Opção 2: IP do servidor + nome personalizado do host
```powershell
-ZabbixServerIP "*************" -AgentHostname "SERVIDOR-VENDAS"
```

#### Opção 3: Forçar reinstalação (remove instalação existente)
```powershell
-ZabbixServerIP "*************" -AgentHostname "WORKSTATION-01" -Force
```

#### Opção 4: Versão específica do Zabbix
```powershell
-ZabbixServerIP "*************" -AgentHostname "PC-FINANCEIRO" -ZabbixVersion "7.0.5"
```

#### Opção 5: Diagnóstico apenas (verificar problemas)
```powershell
-ZabbixServerIP "*************" -DiagnoseOnly
```

#### Opção 6: Instalação com correção automática (para erro 1603)
```powershell
-ZabbixServerIP "*************" -AgentHostname "SERVIDOR-01" -Force
```

#### Opção 7: Instalação offline (para problemas de SSL/conectividade)
```powershell
-ZabbixServerIP "*************" -LocalMSIPath "C:\temp\zabbix_agent2.msi"
```

### 3. Exemplos Práticos

| Cenário | Script Arguments |
|---------|------------------|
| **Instalação básica** | `-ZabbixServerIP "*********"` |
| **Com hostname personalizado** | `-ZabbixServerIP "*********" -AgentHostname "CAIXA-01"` |
| **Servidor por nome** | `-ZabbixServerIP "zabbix.empresa.local" -AgentHostname "VENDAS-PC"` |
| **Reinstalação forçada** | `-ZabbixServerIP "*********" -AgentHostname "SERVIDOR-BACKUP" -Force` |

## 📊 Parâmetros Disponíveis

| Parâmetro | Obrigatório | Descrição | Exemplo |
|-----------|-------------|-----------|---------|
| `ZabbixServerIP` | ✅ Sim | IP ou hostname do servidor Zabbix | `"*************"` |
| `AgentHostname` | ❌ Não | Nome do host no Zabbix | `"SERVIDOR-01"` |
| `ZabbixVersion` | ❌ Não | Versão do Zabbix Agent 2 | `"7.0.6"` |
| `InstallPath` | ❌ Não | Caminho de instalação | `"C:\Zabbix"` |
| `LocalMSIPath` | ❌ Não | Arquivo MSI local | `"C:\temp\zabbix.msi"` |
| `DiagnoseOnly` | ❌ Não | Apenas diagnóstico | `-DiagnoseOnly` |
| `Force` | ❌ Não | Força reinstalação + correção | `-Force` |

## 🔍 Diagnóstico Integrado

O script agora inclui **diagnóstico automático** que detecta e corrige problemas comuns:

### **Problemas detectados automaticamente:**
- ✅ **Instalações anteriores corrompidas**
- ✅ **Serviços do Zabbix rodando**
- ✅ **Processos do Zabbix ativos**
- ✅ **Conflitos de arquivos/diretórios**
- ✅ **Problemas de SSL/TLS**
- ✅ **Windows Installer Service**
- ✅ **Espaço insuficiente em disco**
- ✅ **Arquivos temporários**

### **Modos de operação:**

#### **1. Diagnóstico apenas** (sem instalar)
```powershell
-ZabbixServerIP "*************" -DiagnoseOnly
```
- Verifica problemas sem fazer alterações
- Retorna código 0 se sistema estiver limpo
- Retorna código 1 se houver problemas

#### **2. Instalação normal** (diagnóstico básico)
```powershell
-ZabbixServerIP "*************" -AgentHostname "SERVIDOR-01"
```
- Executa diagnóstico básico
- Instala se não houver problemas críticos
- Exibe avisos sobre problemas encontrados

#### **3. Instalação com correção** (recomendado para erro 1603)
```powershell
-ZabbixServerIP "*************" -AgentHostname "SERVIDOR-01" -Force
```
- Executa diagnóstico completo
- **Corrige automaticamente** todos os problemas encontrados
- Instala após limpeza completa

## 🚨 Resolução de Problemas SSL/TLS

### Problema: "Could not create SSL/TLS secure channel"

Este erro é comum em sistemas Windows mais antigos ou com configurações de segurança restritivas.

#### Solução 1: Teste de Diagnóstico
```powershell
# Execute o script de teste primeiro
.\Test-SSL-Download.ps1
```

#### Solução 2: Instalação Offline
1. **Baixe manualmente o MSI:**
   - x64: https://cdn.zabbix.com/zabbix/binaries/stable/7.0/7.0.6/zabbix_agent2-7.0.6-windows-amd64-openssl.msi
   - x86: https://cdn.zabbix.com/zabbix/binaries/stable/7.0/7.0.6/zabbix_agent2-7.0.6-windows-i386-openssl.msi

2. **Coloque o arquivo em uma pasta acessível** (ex: `C:\temp\`)

3. **Execute o script com o parâmetro `-LocalMSIPath`:**
```powershell
-ZabbixServerIP "*************" -LocalMSIPath "C:\temp\zabbix_agent2-7.0.6-windows-amd64-openssl.msi"
```

#### Solução 3: Configuração Manual de TLS
```powershell
# Execute antes do script principal
[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
```

## 🔧 O que o script faz

1. **Verificações iniciais**
   - Verifica privilégios de administrador
   - Detecta arquitetura do sistema (x64/x86)
   - Valida parâmetros de entrada

2. **Remoção de instalações existentes** (se necessário)
   - Para serviços do Zabbix Agent existentes
   - Remove instalações via MSI
   - Limpa diretórios residuais

3. **Download e instalação**
   - Baixa o instalador MSI apropriado (x64 ou x86)
   - Instala silenciosamente com parâmetros personalizados
   - Configura automaticamente servidor e hostname

4. **Configuração**
   - Cria arquivo de configuração otimizado
   - Configura serviço para inicialização automática
   - Adiciona regra de firewall para porta 10050

5. **Testes de conectividade**
   - Testa ping para o servidor Zabbix
   - Verifica conectividade TCP na porta 10051

## 📁 Arquivos criados

- **Instalação**: `C:\Program Files\Zabbix Agent 2\`
- **Configuração**: `C:\Program Files\Zabbix Agent 2\zabbix_agent2.conf`
- **Logs**: `C:\Program Files\Zabbix Agent 2\zabbix_agent2.log`
- **Backup**: `zabbix_agent2.conf.backup.YYYYMMDD_HHMMSS`

## 🔍 Verificação da instalação

Após a execução, você pode verificar:

### Via PowerShell
```powershell
# Verificar serviço
Get-Service "Zabbix Agent 2"

# Verificar processo
Get-Process zabbix_agent2

# Verificar porta
netstat -an | findstr :10050

# Verificar logs
Get-Content "C:\Program Files\Zabbix Agent 2\zabbix_agent2.log" -Tail 20
```

### Via Tactical RMM
- O script exibe logs detalhados durante a execução
- Códigos de saída: 0 = sucesso, 1 = erro

## 🛠️ Troubleshooting

### Problemas comuns

#### 1. Erro de download SSL/TLS
- **Causa**: Problemas com protocolos SSL/TLS ou certificados
- **Sintomas**: "Could not create SSL/TLS secure channel"
- **Soluções**:
  1. Execute o script de teste: `Test-SSL-Download.ps1`
  2. Use instalação offline com `-LocalMSIPath`
  3. Baixe manualmente o MSI e especifique o caminho

#### 2. Erro de download geral
- **Causa**: Sem conexão com internet ou proxy bloqueando
- **Solução**: Verificar conectividade e configurações de proxy

#### 2. Erro MSI 1603 (Erro fatal durante instalação)
- **Causa**: Instalação anterior corrompida, conflitos, permissões
- **Sintomas**: "MSI retornou código de erro: 1603"
- **Soluções**:
  1. Execute o diagnóstico: `Fix-MSI-Error-1603.ps1`
  2. Use parâmetro `-Force` para limpeza completa
  3. Verifique se não há outro Zabbix rodando
  4. Execute como Administrador

#### 3. Erro de instalação MSI geral
- **Causa**: Instalação anterior corrompida
- **Solução**: Usar parâmetro `-Force` para forçar limpeza

#### 3. Serviço não inicia
- **Causa**: Configuração incorreta ou firewall
- **Solução**: Verificar logs e configuração de rede

#### 4. Não conecta ao servidor
- **Causa**: IP incorreto, firewall ou rede
- **Solução**: Testar conectividade manual

### Comandos de diagnóstico

#### Para erro MSI 1603:
```powershell
# Diagnóstico completo do erro 1603
.\Fix-MSI-Error-1603.ps1

# Correção automática
.\Fix-MSI-Error-1603.ps1 -Fix

# Verificar serviços do Zabbix
Get-Service | Where-Object { $_.Name -like "*Zabbix*" }

# Verificar processos do Zabbix
Get-Process | Where-Object { $_.ProcessName -like "*zabbix*" }

# Verificar instalações MSI
Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Zabbix*" }

# Limpeza manual de serviços
sc.exe delete "Zabbix Agent"
sc.exe delete "Zabbix Agent 2"
```

#### Para problemas gerais:
```powershell
# Testar conectividade
Test-Connection -ComputerName "IP_DO_SERVIDOR" -Count 4
Test-NetConnection -ComputerName "IP_DO_SERVIDOR" -Port 10051

# Verificar configuração
Get-Content "C:\Program Files\Zabbix Agent 2\zabbix_agent2.conf"

# Verificar logs
Get-Content "C:\Program Files\Zabbix Agent 2\zabbix_agent2.log" -Tail 50

# Verificar log de instalação MSI
Get-Content "$env:TEMP\zabbix_install.log" -Tail 50

# Reiniciar serviço
Restart-Service "Zabbix Agent 2"
```

## 📝 Logs do script

O script gera logs detalhados com timestamp:
- `[INFO]` - Informações gerais
- `[SUCCESS]` - Operações bem-sucedidas  
- `[WARNING]` - Avisos (não impedem execução)
- `[ERROR]` - Erros críticos (param execução)

## 🔒 Configurações de segurança

- **Firewall**: Regra automática para porta 10050
- **Comandos remotos**: Habilitados por padrão
- **Logs**: Configurados para rotação automática

## 📞 Suporte

Para problemas ou dúvidas:
1. Verificar logs do script no Tactical RMM
2. Executar comandos de diagnóstico
3. Verificar conectividade de rede
4. Consultar logs do Zabbix Agent

---

**Desenvolvido por**: Paulo Matheus - NVirtual  
**Versão**: 1.1  
**Compatibilidade**: Windows 7+ / Server 2012+  
**Zabbix Agent**: v7.0.6 (configurável)
