#!/bin/bash
# Script de Instalacao do Zabbix Agent para Linux - Tactical RMM
# Versao simplificada para compatibilidade total com Tactical RMM
# Autor: <PERSON> - NVirtual
# Versao: 1.1

# Configuracoes padrao
ZABBIX_VERSION="7.0.10"
INSTALL_PATH="/opt/zabbix"
CONFIG_PATH="/etc/zabbix"
SERVICE_NAME="zabbix-agent"

# URLs de download - Repositorio oficial Zabbix
DOWNLOAD_URL_AMD64="https://repo.zabbix.com/zabbix/7.0/ubuntu/pool/main/z/zabbix/zabbix-agent_7.0.10-1+ubuntu22.04_amd64.deb"
DOWNLOAD_URL_ARM64="https://repo.zabbix.com/zabbix/7.0/ubuntu/pool/main/z/zabbix/zabbix-agent_7.0.10-1+ubuntu22.04_arm64.deb"

# URLs alternativas (pacotes DEB e RPM)
ALT_DOWNLOAD_URL_AMD64=(
    "https://repo.zabbix.com/zabbix/7.0/debian/pool/main/z/zabbix/zabbix-agent_7.0.10-1+debian12_amd64.deb"
    "https://repo.zabbix.com/zabbix/7.0/rhel/9/x86_64/zabbix-agent-7.0.10-release1.el9.x86_64.rpm"
    "https://repo.zabbix.com/zabbix/7.0/centos/8/x86_64/zabbix-agent-7.0.10-release1.el8.x86_64.rpm"
)

ALT_DOWNLOAD_URL_ARM64=(
    "https://repo.zabbix.com/zabbix/7.0/debian/pool/main/z/zabbix/zabbix-agent_7.0.10-1+debian12_arm64.deb"
    "https://repo.zabbix.com/zabbix/7.0/rhel/9/aarch64/zabbix-agent-7.0.10-release1.el9.aarch64.rpm"
)

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Funcao para log
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "SUCCESS")
            echo -e "[$timestamp] [${GREEN}SUCCESS${NC}] $message"
            ;;
        "WARNING")
            echo -e "[$timestamp] [${YELLOW}WARNING${NC}] $message"
            ;;
        "ERROR")
            echo -e "[$timestamp] [${RED}ERROR${NC}] $message"
            ;;
        *)
            echo -e "[$timestamp] [INFO] $message"
            ;;
    esac
}

# Funcao para processar argumentos de forma flexivel
parse_arguments() {
    # Metodo 1: Argumentos tradicionais
    while [[ $# -gt 0 ]]; do
        case $1 in
            --server)
                ZABBIX_SERVER="$2"
                shift 2
                ;;
            --hostname)
                AGENT_HOSTNAME="$2"
                shift 2
                ;;
            --force)
                FORCE_INSTALL="true"
                shift
                ;;
            --diagnose-only)
                DIAGNOSE_ONLY="true"
                shift
                ;;
            *)
                # Se nao reconhecer, tentar como string completa
                if [[ "$1" == *"--server"* ]]; then
                    # Extrair server da string
                    ZABBIX_SERVER=$(echo "$1" | sed -n 's/.*--server[[:space:]]*\([^[:space:]]*\).*/\1/p')
                fi
                if [[ "$1" == *"--hostname"* ]]; then
                    # Extrair hostname da string
                    AGENT_HOSTNAME=$(echo "$1" | sed -n 's/.*--hostname[[:space:]]*\([^[:space:]]*\).*/\1/p')
                fi
                if [[ "$1" == *"--force"* ]]; then
                    FORCE_INSTALL="true"
                fi
                if [[ "$1" == *"--diagnose-only"* ]]; then
                    DIAGNOSE_ONLY="true"
                fi
                shift
                ;;
        esac
    done
    
    # Metodo 2: Variaveis de ambiente (fallback)
    if [ -z "$ZABBIX_SERVER" ] && [ ! -z "$ZABBIX_SERVER_IP" ]; then
        ZABBIX_SERVER="$ZABBIX_SERVER_IP"
    fi
    
    if [ -z "$AGENT_HOSTNAME" ] && [ ! -z "$ZABBIX_HOSTNAME" ]; then
        AGENT_HOSTNAME="$ZABBIX_HOSTNAME"
    fi
    
    # Definir hostname padrao
    if [ -z "$AGENT_HOSTNAME" ]; then
        AGENT_HOSTNAME=$(hostname)
    fi
}

# Funcao para verificar root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_message "ERROR" "Este script deve ser executado como root"
        exit 1
    fi
}

# Funcao para detectar arquitetura
detect_architecture() {
    local arch=$(uname -m)
    case "$arch" in
        x86_64) echo "amd64" ;;
        aarch64|arm64) echo "arm64" ;;
        *) log_message "ERROR" "Arquitetura nao suportada: $arch"; exit 1 ;;
    esac
}

# Funcao para instalar dependencias
install_dependencies() {
    log_message "INFO" "Instalando dependencias..."
    
    if command -v apt-get > /dev/null 2>&1; then
        apt-get update -qq > /dev/null 2>&1
        apt-get install -y wget curl tar systemd > /dev/null 2>&1
    elif command -v yum > /dev/null 2>&1; then
        yum install -y wget curl tar systemd > /dev/null 2>&1
    elif command -v dnf > /dev/null 2>&1; then
        dnf install -y wget curl tar systemd > /dev/null 2>&1
    fi
    
    log_message "SUCCESS" "Dependencias instaladas"
}

# Funcao para limpeza de instalacao anterior
cleanup_previous_installation() {
    if [ "$FORCE_INSTALL" = "true" ]; then
        log_message "INFO" "Removendo instalacao anterior..."
        
        # Parar servicos
        systemctl stop zabbix-agent 2>/dev/null
        systemctl stop zabbix_agentd 2>/dev/null
        systemctl disable zabbix-agent 2>/dev/null
        systemctl disable zabbix_agentd 2>/dev/null
        
        # Remover processos
        pkill -f zabbix 2>/dev/null
        sleep 2
        pkill -9 -f zabbix 2>/dev/null
        
        # Remover diretorios
        rm -rf /opt/zabbix /etc/zabbix /var/log/zabbix /var/run/zabbix 2>/dev/null
        rm -f /etc/systemd/system/zabbix-agent.service 2>/dev/null
        systemctl daemon-reload
        
        log_message "SUCCESS" "Limpeza concluida"
    fi
}

# Funcao para testar URL
test_url() {
    local url="$1"
    log_message "INFO" "Testando URL: $url" >&2

    if command -v curl > /dev/null 2>&1; then
        if curl --connect-timeout 10 --max-time 30 -s --head "$url" | head -1 | grep -q "200 OK"; then
            log_message "SUCCESS" "URL acessivel" >&2
            return 0
        fi
    elif command -v wget > /dev/null 2>&1; then
        if wget --timeout=10 --tries=1 --spider "$url" 2>/dev/null; then
            log_message "SUCCESS" "URL acessivel" >&2
            return 0
        fi
    fi

    log_message "WARNING" "URL nao acessivel ou lenta" >&2
    return 1
}

# Funcao para download
download_zabbix_agent() {
    local architecture="$1"
    local download_url=""

    case "$architecture" in
        amd64) download_url="$DOWNLOAD_URL_AMD64" ;;
        arm64) download_url="$DOWNLOAD_URL_ARM64" ;;
        *) log_message "ERROR" "Arquitetura nao suportada" >&2; exit 1 ;;
    esac

    local filename=$(basename "$download_url")
    local download_path="/tmp/$filename"

    log_message "INFO" "Baixando Zabbix Agent v$ZABBIX_VERSION - $architecture" >&2
    log_message "INFO" "URL: $download_url" >&2
    log_message "INFO" "Destino: $download_path" >&2

    # Testar URL antes do download
    if ! test_url "$download_url"; then
        log_message "WARNING" "URL principal nao acessivel, pode haver problemas no download" >&2
    fi

    # Remover arquivo anterior se existir
    rm -f "$download_path"

    # Tentar wget primeiro
    if command -v wget > /dev/null 2>&1; then
        log_message "INFO" "Tentando download com wget..." >&2
        if wget --timeout=60 --tries=3 -O "$download_path" "$download_url" >&2; then
            # Verificar se arquivo foi baixado e tem tamanho adequado
            if [ -f "$download_path" ] && [ -s "$download_path" ]; then
                local file_size=$(stat -c%s "$download_path" 2>/dev/null || stat -f%z "$download_path" 2>/dev/null)
                local file_size_kb=$((file_size / 1024))
                if [ $file_size_kb -gt 1024 ]; then
                    local file_size_mb=$((file_size_kb / 1024))
                    log_message "SUCCESS" "Download concluido com wget - Tamanho: ${file_size_mb}MB" >&2
                else
                    log_message "SUCCESS" "Download concluido com wget - Tamanho: ${file_size_kb}KB" >&2
                fi

                # Verificar se arquivo e valido baseado no tipo
                local filename=$(basename "$download_path")
                if [[ "$filename" == *.deb ]] || [[ "$filename" == *.rpm ]]; then
                    # Para pacotes DEB/RPM, verificar se nao e HTML de erro
                    if file "$download_path" | grep -q "HTML\|text"; then
                        log_message "ERROR" "Arquivo baixado e HTML/texto, nao um pacote valido" >&2
                        rm -f "$download_path"
                    else
                        echo "$download_path"
                        return 0
                    fi
                elif [[ "$filename" == *.tar.gz ]]; then
                    # Para tar.gz, verificar compressao
                    if file "$download_path" | grep -q "gzip compressed"; then
                        echo "$download_path"
                        return 0
                    else
                        log_message "ERROR" "Arquivo baixado nao e um tar.gz valido" >&2
                        rm -f "$download_path"
                    fi
                else
                    # Arquivo desconhecido, aceitar se nao for HTML
                    if ! file "$download_path" | grep -q "HTML\|text"; then
                        echo "$download_path"
                        return 0
                    else
                        log_message "ERROR" "Arquivo baixado parece ser HTML/texto" >&2
                        rm -f "$download_path"
                    fi
                fi
            else
                log_message "ERROR" "Arquivo nao foi baixado corretamente com wget" >&2
            fi
        else
            log_message "WARNING" "wget falhou" >&2
        fi
    fi

    # Fallback para curl
    if command -v curl > /dev/null 2>&1; then
        log_message "INFO" "Tentando download com curl..." >&2
        if curl --connect-timeout 60 --max-time 300 -L "$download_url" -o "$download_path" >&2; then
            # Verificar se arquivo foi baixado e tem tamanho adequado
            if [ -f "$download_path" ] && [ -s "$download_path" ]; then
                local file_size=$(stat -c%s "$download_path" 2>/dev/null || stat -f%z "$download_path" 2>/dev/null)
                local file_size_kb=$((file_size / 1024))
                if [ $file_size_kb -gt 1024 ]; then
                    local file_size_mb=$((file_size_kb / 1024))
                    log_message "SUCCESS" "Download concluido com curl - Tamanho: ${file_size_mb}MB" >&2
                else
                    log_message "SUCCESS" "Download concluido com curl - Tamanho: ${file_size_kb}KB" >&2
                fi

                # Verificar se arquivo e valido baseado no tipo
                local filename=$(basename "$download_path")
                if [[ "$filename" == *.deb ]] || [[ "$filename" == *.rpm ]]; then
                    # Para pacotes DEB/RPM, verificar se nao e HTML de erro
                    if file "$download_path" | grep -q "HTML\|text"; then
                        log_message "ERROR" "Arquivo baixado e HTML/texto, nao um pacote valido" >&2
                        rm -f "$download_path"
                    else
                        echo "$download_path"
                        return 0
                    fi
                elif [[ "$filename" == *.tar.gz ]]; then
                    # Para tar.gz, verificar compressao
                    if file "$download_path" | grep -q "gzip compressed"; then
                        echo "$download_path"
                        return 0
                    else
                        log_message "ERROR" "Arquivo baixado nao e um tar.gz valido" >&2
                        rm -f "$download_path"
                    fi
                else
                    # Arquivo desconhecido, aceitar se nao for HTML
                    if ! file "$download_path" | grep -q "HTML\|text"; then
                        echo "$download_path"
                        return 0
                    else
                        log_message "ERROR" "Arquivo baixado parece ser HTML/texto" >&2
                        rm -f "$download_path"
                    fi
                fi
            else
                log_message "ERROR" "Arquivo nao foi baixado corretamente com curl" >&2
            fi
        else
            log_message "WARNING" "curl falhou" >&2
        fi
    fi

    # Tentar URLs alternativas se disponivel
    local alt_urls=()
    if [ "$architecture" = "amd64" ]; then
        alt_urls=("${ALT_DOWNLOAD_URL_AMD64[@]}")
    elif [ "$architecture" = "arm64" ]; then
        alt_urls=("${ALT_DOWNLOAD_URL_ARM64[@]}")
    fi

    if [ ${#alt_urls[@]} -gt 0 ]; then
        log_message "WARNING" "Tentando URLs alternativas..." >&2
        for alt_url in "${alt_urls[@]}"; do
            log_message "INFO" "Tentando URL alternativa: $alt_url" >&2

            # Tentar com wget
            if command -v wget > /dev/null 2>&1; then
                if wget --timeout=60 --tries=2 -O "$download_path" "$alt_url" >&2; then
                    if [ -f "$download_path" ] && [ -s "$download_path" ]; then
                        log_message "SUCCESS" "Download concluido com URL alternativa (wget)" >&2
                        echo "$download_path"
                        return 0
                    fi
                fi
            fi

            # Tentar com curl
            if command -v curl > /dev/null 2>&1; then
                if curl --connect-timeout 60 --max-time 300 -L "$alt_url" -o "$download_path" >&2; then
                    if [ -f "$download_path" ] && [ -s "$download_path" ]; then
                        log_message "SUCCESS" "Download concluido com URL alternativa (curl)" >&2
                        echo "$download_path"
                        return 0
                    fi
                fi
            fi

            log_message "WARNING" "URL alternativa falhou: $alt_url" >&2
        done
    fi

    log_message "ERROR" "Falha no download - nenhum metodo funcionou" >&2
    log_message "ERROR" "Verifique conectividade com a internet" >&2
    log_message "ERROR" "URLs testadas:" >&2
    log_message "ERROR" "  Principal: $download_url" >&2

    # Mostrar URLs alternativas testadas
    local alt_urls=()
    if [ "$architecture" = "amd64" ]; then
        alt_urls=("${ALT_DOWNLOAD_URL_AMD64[@]}")
    elif [ "$architecture" = "arm64" ]; then
        alt_urls=("${ALT_DOWNLOAD_URL_ARM64[@]}")
    fi

    for alt_url in "${alt_urls[@]}"; do
        log_message "ERROR" "  Alternativa: $alt_url" >&2
    done
    exit 1
}

# Funcao para criar usuario zabbix
create_zabbix_user() {
    if ! id "zabbix" &>/dev/null; then
        useradd -r -d /var/lib/zabbix -s /sbin/nologin -c "Zabbix Agent" zabbix 2>/dev/null
        log_message "SUCCESS" "Usuario zabbix criado"
    fi
}

# Funcao para detectar tipo de arquivo
detect_package_type() {
    local file_path="$1"
    local filename=$(basename "$file_path")

    if [[ "$filename" == *.rpm ]]; then
        echo "rpm"
    elif [[ "$filename" == *.deb ]]; then
        echo "deb"
    elif [[ "$filename" == *.tar.gz ]]; then
        echo "tar.gz"
    else
        echo "unknown"
    fi
}

# Funcao para instalar pacote RPM
install_rpm_package() {
    local package_path="$1"
    log_message "INFO" "Instalando pacote RPM..."

    if command -v rpm > /dev/null 2>&1; then
        if rpm -ivh "$package_path" --force; then
            log_message "SUCCESS" "Pacote RPM instalado com sucesso"
            return 0
        fi
    fi

    if command -v yum > /dev/null 2>&1; then
        if yum localinstall -y "$package_path"; then
            log_message "SUCCESS" "Pacote RPM instalado com yum"
            return 0
        fi
    fi

    if command -v dnf > /dev/null 2>&1; then
        if dnf localinstall -y "$package_path"; then
            log_message "SUCCESS" "Pacote RPM instalado com dnf"
            return 0
        fi
    fi

    log_message "ERROR" "Falha ao instalar pacote RPM"
    return 1
}

# Funcao para instalar pacote DEB
install_deb_package() {
    local package_path="$1"
    log_message "INFO" "Instalando pacote DEB..."

    if command -v dpkg > /dev/null 2>&1; then
        if dpkg -i "$package_path"; then
            log_message "SUCCESS" "Pacote DEB instalado com sucesso"
            return 0
        else
            # Tentar corrigir dependencias
            log_message "INFO" "Tentando corrigir dependencias..."
            if command -v apt-get > /dev/null 2>&1; then
                apt-get install -f -y
                if dpkg -i "$package_path"; then
                    log_message "SUCCESS" "Pacote DEB instalado apos correcao"
                    return 0
                fi
            fi
        fi
    fi

    log_message "ERROR" "Falha ao instalar pacote DEB"
    return 1
}

# Funcao para instalar
install_zabbix_agent() {
    local installer_path="$1"

    log_message "INFO" "Instalando Zabbix Agent..."
    log_message "INFO" "Arquivo de instalacao: $installer_path"

    # Verificar se arquivo existe
    if [ ! -f "$installer_path" ]; then
        log_message "ERROR" "Arquivo de instalacao nao encontrado: $installer_path"
        exit 1
    fi

    # Verificar tamanho do arquivo
    local file_size=$(stat -c%s "$installer_path" 2>/dev/null || stat -f%z "$installer_path" 2>/dev/null)
    local file_size_mb=$((file_size / 1024 / 1024))
    log_message "INFO" "Tamanho do arquivo: ${file_size_mb}MB"

    if [ $file_size -lt 100000 ]; then  # Menos de 100KB
        log_message "ERROR" "Arquivo muito pequeno, provavelmente corrompido"
        exit 1
    fi

    # Detectar tipo de pacote
    local package_type=$(detect_package_type "$installer_path")
    log_message "INFO" "Tipo de pacote detectado: $package_type"

    case "$package_type" in
        "rpm")
            if install_rpm_package "$installer_path"; then
                log_message "SUCCESS" "Instalacao RPM concluida"
                return 0
            else
                log_message "ERROR" "Falha na instalacao RPM"
                exit 1
            fi
            ;;
        "deb")
            if install_deb_package "$installer_path"; then
                log_message "SUCCESS" "Instalacao DEB concluida"
                return 0
            else
                log_message "ERROR" "Falha na instalacao DEB"
                exit 1
            fi
            ;;
        "tar.gz")
            log_message "INFO" "Instalando a partir de tar.gz..."
            # Verificar se e um arquivo tar.gz valido
            if ! file "$installer_path" | grep -q "gzip compressed"; then
                log_message "ERROR" "Arquivo nao e um tar.gz valido"
                log_message "INFO" "Tipo do arquivo: $(file "$installer_path")"
                exit 1
            fi
            ;;
        *)
            log_message "ERROR" "Tipo de pacote nao suportado: $package_type"
            log_message "INFO" "Tipo do arquivo: $(file "$installer_path")"
            exit 1
            ;;
    esac

    # Criar diretorios
    log_message "INFO" "Criando diretorios..."
    mkdir -p "$INSTALL_PATH" "$CONFIG_PATH" "/var/log/zabbix" "/var/run/zabbix" "/var/lib/zabbix"

    # Limpar diretorio temporario de extracao
    rm -rf /tmp/zabbix_extract 2>/dev/null
    mkdir -p /tmp/zabbix_extract

    # Extrair arquivo
    log_message "INFO" "Extraindo arquivo..."
    if tar -xzf "$installer_path" -C /tmp/zabbix_extract/; then
        log_message "SUCCESS" "Arquivo extraido com sucesso"
    else
        log_message "ERROR" "Falha ao extrair arquivo"
        log_message "INFO" "Tentando listar conteudo do arquivo..."
        tar -tzf "$installer_path" | head -10
        exit 1
    fi

    # Encontrar diretorio extraido
    local extracted_dir=$(find /tmp/zabbix_extract -maxdepth 2 -type d -name "*zabbix*" | head -1)
    if [ -z "$extracted_dir" ]; then
        log_message "WARNING" "Diretorio zabbix nao encontrado, listando conteudo..."
        ls -la /tmp/zabbix_extract/
        extracted_dir="/tmp/zabbix_extract"
    fi

    log_message "INFO" "Diretorio extraido: $extracted_dir"

    # Procurar binario em varios locais possiveis
    local binary_path=""
    local possible_paths=(
        "$extracted_dir/bin/zabbix_agentd"
        "$extracted_dir/sbin/zabbix_agentd"
        "$extracted_dir/zabbix_agentd"
        "$(find "$extracted_dir" -name "zabbix_agentd" -type f | head -1)"
    )

    for path in "${possible_paths[@]}"; do
        if [ -f "$path" ]; then
            binary_path="$path"
            break
        fi
    done

    if [ -n "$binary_path" ]; then
        log_message "SUCCESS" "Binario encontrado: $binary_path"
        cp "$binary_path" "$INSTALL_PATH/"
        chmod +x "$INSTALL_PATH/zabbix_agentd"
        log_message "SUCCESS" "Binario instalado em $INSTALL_PATH/zabbix_agentd"
    else
        log_message "ERROR" "Binario zabbix_agentd nao encontrado"
        log_message "INFO" "Conteudo do diretorio extraido:"
        find "$extracted_dir" -type f -name "*zabbix*" | head -10
        exit 1
    fi

    # Criar usuario e definir permissoes
    create_zabbix_user
    chown -R zabbix:zabbix "$INSTALL_PATH" "$CONFIG_PATH" "/var/log/zabbix" "/var/run/zabbix" "/var/lib/zabbix"

    # Limpeza do diretorio temporario
    rm -rf /tmp/zabbix_extract

    log_message "SUCCESS" "Instalacao concluida"
}

# Funcao para configurar
configure_zabbix_agent() {
    local config_file="$CONFIG_PATH/zabbix_agentd.conf"
    
    log_message "INFO" "Configurando Zabbix Agent..."
    
    cat > "$config_file" << EOF
# Configuracao do Zabbix Agent
Server=$ZABBIX_SERVER
ServerActive=$ZABBIX_SERVER
Hostname=$AGENT_HOSTNAME
LogType=file
LogFile=/var/log/zabbix/zabbix_agentd.log
LogFileSize=10
EnableRemoteCommands=1
Timeout=30
ListenPort=10050
StartAgents=3
BufferSend=5
BufferSize=100
User=zabbix
PidFile=/var/run/zabbix/zabbix_agentd.pid
EOF
    
    chown zabbix:zabbix "$config_file"
    chmod 644 "$config_file"
    
    log_message "SUCCESS" "Configuracao criada"
}

# Funcao para criar servico
create_systemd_service() {
    local service_file="/etc/systemd/system/$SERVICE_NAME.service"
    
    cat > "$service_file" << EOF
[Unit]
Description=Zabbix Agent
After=network.target

[Service]
Type=forking
Restart=on-failure
PIDFile=/var/run/zabbix/zabbix_agentd.pid
ExecStart=$INSTALL_PATH/zabbix_agentd -c $CONFIG_PATH/zabbix_agentd.conf
User=zabbix
Group=zabbix

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable "$SERVICE_NAME"
    
    log_message "SUCCESS" "Servico criado"
}

# Funcao para configurar firewall
configure_firewall() {
    log_message "INFO" "Configurando firewall..."
    
    if command -v ufw > /dev/null 2>&1; then
        ufw allow 10050/tcp > /dev/null 2>&1
    elif command -v firewall-cmd > /dev/null 2>&1; then
        firewall-cmd --permanent --add-port=10050/tcp > /dev/null 2>&1
        firewall-cmd --reload > /dev/null 2>&1
    elif command -v iptables > /dev/null 2>&1; then
        iptables -A INPUT -p tcp --dport 10050 -j ACCEPT > /dev/null 2>&1
    fi
    
    log_message "SUCCESS" "Firewall configurado"
}

# Funcao para iniciar servico
start_zabbix_service() {
    log_message "INFO" "Iniciando servico..."
    
    if systemctl start "$SERVICE_NAME"; then
        sleep 2
        if systemctl is-active --quiet "$SERVICE_NAME"; then
            log_message "SUCCESS" "Servico iniciado com sucesso"
        else
            log_message "ERROR" "Servico nao esta rodando"
            journalctl -u "$SERVICE_NAME" --no-pager -n 5
            exit 1
        fi
    else
        log_message "ERROR" "Falha ao iniciar servico"
        exit 1
    fi
}

# Funcao principal
main() {
    log_message "SUCCESS" "=== INSTALACAO DO ZABBIX AGENT PARA LINUX ==="
    log_message "INFO" "Servidor Zabbix: $ZABBIX_SERVER"
    log_message "INFO" "Hostname: $AGENT_HOSTNAME"
    
    check_root
    
    local architecture=$(detect_architecture)
    log_message "INFO" "Arquitetura: $architecture"
    
    install_dependencies
    cleanup_previous_installation
    
    if [ "$DIAGNOSE_ONLY" = "true" ]; then
        log_message "SUCCESS" "Diagnostico concluido - sistema pronto"
        exit 0
    fi
    
    log_message "INFO" "Iniciando download do Zabbix Agent..."
    local installer_path=$(download_zabbix_agent "$architecture")

    if [ -z "$installer_path" ] || [ ! -f "$installer_path" ]; then
        log_message "ERROR" "Falha no download do Zabbix Agent"
        exit 1
    fi

    log_message "INFO" "Download concluido, iniciando instalacao..."
    log_message "INFO" "Arquivo baixado: $installer_path"

    install_zabbix_agent "$installer_path"
    log_message "INFO" "Instalacao concluida, iniciando configuracao..."

    configure_zabbix_agent
    log_message "INFO" "Configuracao concluida, criando servico..."

    create_systemd_service
    log_message "INFO" "Servico criado, configurando firewall..."

    configure_firewall
    log_message "INFO" "Firewall configurado, iniciando servico..."

    start_zabbix_service
    log_message "INFO" "Todas as etapas concluidas!"
    
    # Limpeza
    rm -f "$installer_path"
    rm -rf /tmp/*zabbix*agent* 2>/dev/null
    
    log_message "SUCCESS" "=== INSTALACAO CONCLUIDA COM SUCESSO! ==="
    log_message "INFO" "Configuracao: $CONFIG_PATH/zabbix_agentd.conf"
    log_message "INFO" "Logs: /var/log/zabbix/zabbix_agentd.log"
    log_message "INFO" "Status: systemctl status $SERVICE_NAME"
}

# Processar argumentos e executar
parse_arguments "$@"

# Validar servidor obrigatorio
if [ -z "$ZABBIX_SERVER" ]; then
    log_message "ERROR" "Servidor Zabbix nao especificado"
    log_message "ERROR" "Use: --server IP_SERVIDOR ou defina ZABBIX_SERVER_IP=IP"
    exit 1
fi

# Executar instalacao
main
