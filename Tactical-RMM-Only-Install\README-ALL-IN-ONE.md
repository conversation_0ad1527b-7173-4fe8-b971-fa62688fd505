# Tactical RMM Agent - Instalador All-in-One

## 🚀 Script Único e Completo

Este é um **script all-in-one** super simplificado para instalação do Tactical RMM Agent. 

**Apenas 3 perguntas:**
1. **Tipo**: server ou workstation
2. **Client ID**: ID numérico do cliente
3. **Site ID**: ID numérico do site

**Todo o resto é automático!** 🎯

## 📋 Características

- ✅ **Uma única pergunta por vez** - interface super simples
- ✅ **Detecção automática** de sistema e arquitetura
- ✅ **Configuração pré-definida** da NVirtual
- ✅ **Download automático** com URLs de fallback
- ✅ **Verificação completa** pós-instalação
- ✅ **Logs coloridos** e informativos
- ✅ **Limpeza automática** de arquivos temporários

## 🎯 Como Usar

### Método Simples (Recomendado)

```bash
# 1. Baixar o script
wget https://raw.githubusercontent.com/seu-repo/tactical-rmm-installer.sh

# 2. <PERSON> permis<PERSON>
chmod +x tactical-rmm-installer.sh

# 3. Executar como root
sudo ./tactical-rmm-installer.sh
```

### Exemplo de Uso

```bash
$ ./tactical-rmm-installer.sh

████████╗ █████╗  ██████╗████████╗██╗ ██████╗ █████╗ ██╗         ██████╗ ███╗   ███╗███╗   ███╗
╚══██╔══╝██╔══██╗██╔════╝╚══██╔══╝██║██╔════╝██╔══██╗██║         ██╔══██╗████╗ ████║████╗ ████║
   ██║   ███████║██║        ██║   ██║██║     ███████║██║         ██████╔╝██╔████╔██║██╔████╔██║
   ██║   ██╔══██║██║        ██║   ██║██║     ██╔══██║██║         ██╔══██╗██║╚██╔╝██║██║╚██╔╝██║
   ██║   ██║  ██║╚██████╗   ██║   ██║╚██████╗██║  ██║███████╗    ██║  ██║██║ ╚═╝ ██║██║ ╚═╝ ██║
   ╚═╝   ╚═╝  ╚═╝ ╚═════╝   ╚═╝   ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝    ╚═╝  ╚═╝╚═╝     ╚═╝╚═╝     ╚═╝

                           INSTALADOR ALL-IN-ONE - TACTICAL RMM
                              Desenvolvido por Paulo Matheus - NVirtual

[11:15:30] Arquitetura x86_64 detectada: x86_64
[11:15:30] Ubuntu 22.04 detectado
[11:15:30] Configuração do Tactical RMM Agent

Tipos de agente disponíveis:
  1. server      - Para servidores
  2. workstation - Para estações de trabalho

Escolha o tipo (1 ou 2): 1

Digite o Client ID (número): 123

Digite o Site ID (número): 456

[INFO] Configurações:
[INFO] • Tipo: server
[INFO] • Client ID: 123
[INFO] • Site ID: 456
[INFO] • Arquitetura: x86_64

Confirma a instalação? (s/N): s

[11:15:45] 🚀 Iniciando instalação do Tactical RMM Agent...
[11:15:45] 📦 Atualizando sistema...
[11:15:50] 🔧 Instalando dependências...
[11:15:55] ⬇️  Baixando script do Tactical RMM...
[11:16:00] ✅ Script baixado com sucesso!
[11:16:00] 🔨 Instalando Tactical RMM Agent...
[11:16:30] ✅ Tactical RMM Agent instalado com sucesso!
[11:16:40] 🔍 Verificando instalação...
[11:16:40] ✅ Serviço tacticalagent: ATIVO
[11:16:40] ✅ Processo tacticalagent: RODANDO
[11:16:40] 🌐 Testando conectividade...
[11:16:42] ✅ mesh.centralmesh.nvirtual.com.br: OK
[11:16:44] ✅ api.centralmesh.nvirtual.com.br: OK

==========================================
           INSTALAÇÃO CONCLUÍDA
==========================================

[11:16:45] 🎉 INSTALAÇÃO BEM-SUCEDIDA!
```

## 🔧 O que o Script Faz Automaticamente

1. **🔍 Detecção do Sistema**
   - Identifica Ubuntu/Debian
   - Detecta arquitetura (x86_64/ARM)
   - Escolhe URLs corretas automaticamente

2. **📦 Preparação do Ambiente**
   - Atualiza sistema (`apt update`)
   - Instala dependências necessárias
   - Verifica privilégios sudo

3. **⬇️ Download Inteligente**
   - Tenta múltiplas URLs de fallback
   - Verifica integridade do arquivo
   - Retry automático em caso de falha

4. **🔨 Instalação Completa**
   - Executa instalação do Tactical RMM
   - Configura serviços automaticamente
   - Aplica configurações da NVirtual

5. **✅ Verificação Pós-Instalação**
   - Verifica status dos serviços
   - Testa processos em execução
   - Valida conectividade com servidores

6. **🧹 Limpeza**
   - Remove arquivos temporários
   - Limpa downloads desnecessários

## 📋 Pré-requisitos

- ✅ Sistema Linux (Ubuntu/Debian)
- ✅ Acesso root (sudo)
- ✅ Conexão com internet
- ✅ **DEVE ser executado como root**

## 🌐 Configurações Pré-definidas

O script já vem configurado para a NVirtual:

- **Mesh Server**: `mesh.centralmesh.nvirtual.com.br`
- **API Server**: `api.centralmesh.nvirtual.com.br`
- **Auth Key**: Incluída no script
- **URLs ARM/x86**: Configuradas automaticamente

## 🔍 Verificação Pós-Instalação

Após a instalação, use estes comandos:

```bash
# Verificar status do serviço (como root)
systemctl status tacticalagent

# Ver logs em tempo real (como root)
journalctl -u tacticalagent -f

# Reiniciar se necessário (como root)
systemctl restart tacticalagent

# Verificar processo
ps aux | grep tactical
```

## 🆘 Solução de Problemas

### Serviço não está rodando
```bash
# Verificar logs
sudo journalctl -u tacticalagent -n 20

# Reiniciar serviço
sudo systemctl restart tacticalagent

# Verificar status
sudo systemctl status tacticalagent
```

### Problemas de conectividade
```bash
# Testar conectividade
curl -I https://mesh.centralmesh.nvirtual.com.br
curl -I https://api.centralmesh.nvirtual.com.br

# Verificar DNS
nslookup mesh.centralmesh.nvirtual.com.br
```

### Reinstalar se necessário
```bash
# Parar serviço
sudo systemctl stop tacticalagent

# Remover instalação anterior
sudo apt remove --purge tacticalagent

# Executar script novamente
./tactical-rmm-installer.sh
```

## 🎯 Vantagens do Script All-in-One

- **🚀 Rapidez**: Apenas 3 perguntas e pronto
- **🔧 Simplicidade**: Um único arquivo para tudo
- **🛡️ Confiabilidade**: Verificações em cada etapa
- **📱 Portabilidade**: Funciona em qualquer Linux
- **🎨 Interface**: Logs coloridos e informativos
- **🔄 Automação**: Zero configuração manual

## 📊 Comparação com Scripts Separados

| Característica | All-in-One | Scripts Separados |
|----------------|-------------|-------------------|
| Arquivos | 1 | 9 |
| Perguntas | 3 | 5+ |
| Configuração | Automática | Manual |
| Complexidade | Baixa | Média |
| Manutenção | Simples | Complexa |

## 👨‍💻 Informações Técnicas

- **Linguagem**: Bash
- **Compatibilidade**: Ubuntu 20.04+, Debian 10+
- **Arquiteturas**: x86_64, ARM64, ARMv7
- **Dependências**: curl, wget, systemd
- **Tamanho**: ~9KB

## 📄 Licença

Este script é propriedade da **NVirtual** e destinado ao uso interno da empresa.

## 🆘 Suporte

Para suporte técnico, entre em contato com a equipe de TI da NVirtual.

---

**Desenvolvido por Paulo Matheus - NVirtual**  
**Versão: 2.0.0**  
**Data: 2025-01-21**
