# Configuração no Tactical RMM - Passo a Passo

## 🎯 Guia Rápido para Configurar o Script no Tactical RMM

### 1. Upload do Script

1. **Acesse o Tactical RMM**
2. **Vá para Scripts > Add Script**
3. **Configure os campos:**
   - **Name**: `Instalar Zabbix Agent 2`
   - **Description**: `Instalação automatizada do Zabbix Agent 2 para Windows`
   - **Category**: `Monitoring` ou `System`
   - **Script Type**: `PowerShell`
   - **Shell**: `powershell`

4. **Cole o conteúdo do arquivo** `Install-ZabbixAgent2-Windows.ps1`

### 2. Configuração dos Argumentos

No campo **"Default Arguments"** ou ao executar o script, use:

#### ✅ Configuração Básica (Recomendada)
```
-ZabbixServerIP "SEU_IP_AQUI"
```

#### ✅ Configuração Completa
```
-ZabbixServerIP "SEU_IP_AQUI" -AgentHostname "NOME_PERSONALIZADO"
```

### 3. Exemplos por Cenário

#### 🏢 **Cenário 1: Empresa com servidor Zabbix interno**
```
-ZabbixServerIP "*************"
```
- Usa o nome do computador como hostname no Zabbix
- Ideal para redes internas

#### 🏪 **Cenário 2: Filiais com nomes padronizados**
```
-ZabbixServerIP "*********" -AgentHostname "FILIAL-SP-PC01"
```
- Define nome específico para identificação
- Útil para organização por localização

#### 🌐 **Cenário 3: Servidor Zabbix externo (FQDN)**
```
-ZabbixServerIP "zabbix.empresa.com.br" -AgentHostname "CLIENTE-VENDAS"
```
- Usa nome de domínio em vez de IP
- Hostname personalizado para identificação

#### 🔄 **Cenário 4: Reinstalação/Atualização**
```
-ZabbixServerIP "*************" -AgentHostname "SERVIDOR-BACKUP" -Force
```
- Remove instalação anterior
- Instala versão limpa

### 4. Execução do Script

#### **Opção A: Execução Individual**
1. Selecione o computador/agente
2. Vá para **Scripts**
3. Escolha **"Instalar Zabbix Agent 2"**
4. **Modifique os argumentos** se necessário
5. Clique em **Run**

#### **Opção B: Execução em Massa**
1. Selecione múltiplos agentes
2. **Bulk Actions > Run Script**
3. Escolha **"Instalar Zabbix Agent 2"**
4. **Configure argumentos únicos** ou use padrão
5. Execute

#### **Opção C: Automação via Policy**
1. Crie uma **Policy**
2. Adicione **Script Check**
3. Configure para executar em novos agentes
4. Define argumentos padrão

### 5. Monitoramento da Execução

#### **Durante a execução:**
- ✅ **Status**: Running
- 📊 **Logs**: Acompanhe em tempo real
- ⏱️ **Tempo**: ~2-5 minutos (dependendo da conexão)

#### **Códigos de retorno:**
- **0**: ✅ Sucesso - Instalação concluída
- **1**: ❌ Erro - Verificar logs para detalhes

#### **Logs típicos de sucesso:**
```
[2025-01-12 10:30:15] [INFO] === INSTALAÇÃO DO ZABBIX AGENT 2 PARA WINDOWS ===
[2025-01-12 10:30:16] [INFO] Arquitetura detectada: x64
[2025-01-12 10:30:17] [SUCCESS] Download concluído
[2025-01-12 10:30:25] [SUCCESS] Zabbix Agent 2 instalado com sucesso!
[2025-01-12 10:30:26] [SUCCESS] Configuração aplicada com sucesso!
[2025-01-12 10:30:27] [SUCCESS] Serviço iniciado com sucesso!
[2025-01-12 10:30:28] [SUCCESS] === INSTALAÇÃO CONCLUÍDA COM SUCESSO! ===
```

### 6. Verificação Pós-Instalação

#### **Via Tactical RMM:**
1. Execute script de verificação:
```powershell
Get-Service "Zabbix Agent 2" | Select-Object Name, Status, StartType
```

2. Teste de conectividade:
```powershell
Test-NetConnection -ComputerName "SEU_IP_ZABBIX" -Port 10051
```

#### **No servidor Zabbix:**
1. Verifique se o host aparece em **Configuration > Hosts**
2. Confirme status **"Available"** 
3. Teste itens de monitoramento

### 7. Configurações Avançadas

#### **Argumentos opcionais:**

| Parâmetro | Uso | Exemplo |
|-----------|-----|---------|
| `-ZabbixVersion` | Versão específica | `-ZabbixVersion "7.0.5"` |
| `-InstallPath` | Caminho personalizado | `-InstallPath "D:\Zabbix"` |
| `-Force` | Forçar reinstalação | `-Force` |

#### **Exemplo completo:**
```
-ZabbixServerIP "*************" -AgentHostname "SERVIDOR-PRINCIPAL" -ZabbixVersion "7.0.6" -Force
```

### 8. Troubleshooting Comum

#### **❌ Erro: "Não é possível baixar o instalador"**
- **Causa**: Sem internet ou proxy
- **Solução**: Verificar conectividade

#### **❌ Erro: "MSI retornou código de erro"**
- **Causa**: Instalação anterior corrompida
- **Solução**: Usar `-Force`

#### **❌ Erro: "Serviço não inicia"**
- **Causa**: Configuração de rede
- **Solução**: Verificar IP do servidor

#### **❌ Erro: "Access Denied"**
- **Causa**: Privilégios insuficientes
- **Solução**: Verificar se Tactical RMM roda como SYSTEM

### 9. Boas Práticas

#### **✅ Nomenclatura de Hosts:**
- Use padrões consistentes: `EMPRESA-LOCAL-TIPO-NUM`
- Exemplos: `NVIRTUAL-SP-SRV-01`, `CLIENTE-RJ-PC-05`

#### **✅ Organização:**
- Crie grupos no Zabbix por localização
- Use templates específicos por tipo de equipamento

#### **✅ Monitoramento:**
- Configure alertas para agentes offline
- Monitore logs de instalação

#### **✅ Manutenção:**
- Execute verificações periódicas
- Mantenha versões atualizadas

### 10. Scripts Auxiliares

#### **Verificação rápida:**
```powershell
# Adicione como script separado no Tactical RMM
$service = Get-Service "Zabbix Agent 2" -ErrorAction SilentlyContinue
if ($service) {
    Write-Output "Status: $($service.Status)"
    Write-Output "StartType: $($service.StartType)"
    if (Test-Path "C:\Program Files\Zabbix Agent 2\zabbix_agent2.conf") {
        $config = Get-Content "C:\Program Files\Zabbix Agent 2\zabbix_agent2.conf"
        $server = ($config | Where-Object { $_ -match "^Server=" }) -replace "Server=", ""
        $hostname = ($config | Where-Object { $_ -match "^Hostname=" }) -replace "Hostname=", ""
        Write-Output "Servidor: $server"
        Write-Output "Hostname: $hostname"
    }
} else {
    Write-Output "Zabbix Agent 2 não instalado"
}
```

#### **Desinstalação:**
```powershell
# Script para remover Zabbix Agent 2
Stop-Service "Zabbix Agent 2" -Force -ErrorAction SilentlyContinue
$product = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Zabbix Agent 2*" }
if ($product) { $product.Uninstall() }
Remove-Item "C:\Program Files\Zabbix Agent 2" -Recurse -Force -ErrorAction SilentlyContinue
Get-NetFirewallRule -DisplayName "*Zabbix*" | Remove-NetFirewallRule -ErrorAction SilentlyContinue
Write-Output "Zabbix Agent 2 removido"
```

---

**💡 Dica**: Salve este guia como referência e adapte os exemplos para sua infraestrutura específica.

**📞 Suporte**: Para dúvidas, consulte os logs detalhados do script ou execute os comandos de diagnóstico.
