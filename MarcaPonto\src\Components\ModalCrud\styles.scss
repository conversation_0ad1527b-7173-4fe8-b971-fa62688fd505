@import '../../Styles/responsive';

.modal__crud--wrapper {
    height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.8);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99;

    .modal__close {
        position: absolute;
        top: 30px;
        right: 30px;
        cursor: pointer;
        z-index: 3;

        &:hover {
            svg {
                transform: scale(1.1);
            }
        }

        @include max-sm{
            background: #a6a6a6;
            border-radius: 50%;
        }
    }

    .modal__content {
        width: 90%;
        background-color: #fff;
        border-radius: 25px;
        padding: 40px 20px;

        &.content__scroll{
            position: absolute;
            top: 50px;
            bottom: 50px;
            overflow-y: auto;
        }

        @include sm {
            width: 45%;
        }


        @include lg{
            width: 27%;
        }

        form {
            .form__group {
                margin: 15px auto;
                width: 80%;


                label {
                    font-weight: 700;
                }

                .bt {
                    margin-top: 20px;
                }
            }

        }
    }
}