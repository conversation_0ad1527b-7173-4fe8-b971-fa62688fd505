import requests
import json

API_URL = "https://api.centralmesh.nvirtual.com.br"
API_TOKEN = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"

headers = {"X-API-KEY": API_TOKEN}

print("=== INVESTIGANDO ESTRUTURA WMI ===")

# Buscar agente específico
agent_id = "VrZpwJxyWWZAGSyLivzyDKnmcFEqgHiwMXnIbDxy"  # FLAVIAMILAGRE
r = requests.get(f"{API_URL}/agents/{agent_id}/", headers=headers)
agent = r.json()

print(f"Agente: {agent.get('hostname')}")
print(f"Cliente: {agent.get('client_name')}")

wmi = agent.get('wmi_detail', {})
print(f"\nTipo WMI: {type(wmi)}")

if isinstance(wmi, dict):
    print("\nChaves WMI disponíveis:")
    for k, v in wmi.items():
        if isinstance(v, list):
            print(f"  {k}: {len(v)} itens")
        else:
            print(f"  {k}: {type(v).__name__}")
    
    # Verificar discos
    disk_data = wmi.get('disk', [])
    print(f"\nTotal de discos: {len(disk_data)}")
    
    if disk_data:
        print("\nPrimeiro disco:")
        first_disk = disk_data[0]
        print(f"Tipo do disco: {type(first_disk)}")
        if isinstance(first_disk, dict):
            for k, v in first_disk.items():
                print(f"  {k}: {v}")
        elif isinstance(first_disk, list):
            print(f"Disco é uma lista com {len(first_disk)} itens")
            if first_disk:
                print("Primeiro item da lista:")
                for k, v in first_disk[0].items():
                    print(f"  {k}: {v}")
        else:
            print(f"Disco: {first_disk}")
    else:
        print("Nenhum disco encontrado!")
        
    # Verificar outras estruturas
    print(f"\nCPU: {len(wmi.get('cpu', []))} itens")
    print(f"Memória: {len(wmi.get('mem', []))} itens")
    print(f"BIOS: {len(wmi.get('bios', []))} itens")
    
else:
    print("WMI não é um dicionário!")
    print(f"Conteúdo: {str(wmi)[:200]}")
