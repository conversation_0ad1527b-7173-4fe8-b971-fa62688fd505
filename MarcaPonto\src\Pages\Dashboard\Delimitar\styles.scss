@import '../../../Styles/responsive';

.delimitar__wrapper{
    display: flex;
    margin-top: 20px;
    flex-direction: column;
    height: 64vh;

    @include md{
        flex-direction: row;
    }

    .del__users{
        flex: 1;
        background-color: #fff;
        padding: 20px;
        overflow: auto;

        ul{
            margin-top: 30px;

            li{
                .col__info{
                    margin: 15px 0;
                    background-color: #f6f6f6;
                    padding: 12px;
                    border-radius: 20px;
                    cursor: pointer;

                    &:hover{
                        transform: scale(1.02);
                    }

                    h6{
                        font-size: 16px;
                        font-weight: 700;
                    }

                    p{
                        font-size: 14px;
                    }
                }
            }
        }
    }

    .del__map{
        flex: 3;
        position: relative;

        .editing__now{
            position: absolute;
            bottom: 5px;
            left: 5px;
            background-color: #fff;
            padding: 10px;
            z-index: 999;

            h5{
                span{
                    font-weight: 700;
                    margin-left: 2px;
                }
            }
        }

        .cancel__editing{
            position: absolute;
            bottom: 5px;
            right: 5px;
            background-color: #fff;
            padding: 10px;
            z-index: 999;
            cursor: pointer;

            h5{
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;

                svg{
                    margin-left: 10px;
                }
            }
        }

        .leaflet-container{
            width: 100%;
            height: 100%;
        }

        .loading__map{
            padding: 20px;
            z-index: 9999;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            border-radius: 20px;
            width: 50%;

            h3{
                text-align: center;
                font-size: 20px;
            }
        }
    }
}