# Script de Instalacao do Zabbix Agent 2 para Windows
# Compativel com Tactical RMM e PowerShell 2.0+
# Autor: <PERSON> - NVirtual
# Versao: 1.4 - Compatibilidade melhorada

param(
    [Parameter(Mandatory=$true)]
    [string]$ZabbixServerIP,

    [Parameter(Mandatory=$false)]
    [string]$AgentHostname,

    [Parameter(Mandatory=$false)]
    [string]$ZabbixVersion = "7.0.6",

    [Parameter(Mandatory=$false)]
    [string]$InstallPath = "C:\Program Files\Zabbix Agent 2",

    [Parameter(Mandatory=$false)]
    [switch]$Force,

    [Parameter(Mandatory=$false)]
    [string]$LocalMSIPath,

    [Parameter(Mandatory=$false)]
    [switch]$DiagnoseOnly
)

# Configuracoes com compatibilidade para versoes antigas
$ErrorActionPreference = "Stop"
if ($PSVersionTable.PSVersion.Major -ge 3) {
    $ProgressPreference = "SilentlyContinue"
}

# Definir hostname se nao fornecido (compativel com PS 2.0+)
if (-not $AgentHostname) {
    if ($env:COMPUTERNAME) {
        $AgentHostname = $env:COMPUTERNAME
    } else {
        $AgentHostname = [System.Environment]::MachineName
    }
}

# URLs de download
$DownloadURL64 = "https://cdn.zabbix.com/zabbix/binaries/stable/7.0/7.0.6/zabbix_agent2-7.0.6-windows-amd64-openssl.msi"
$DownloadURL32 = "https://cdn.zabbix.com/zabbix/binaries/stable/7.0/7.0.6/zabbix_agent2-7.0.6-windows-i386-openssl.msi"

# URLs alternativas
$AlternativeURL64 = @(
    "https://repo.zabbix.com/zabbix/7.0/windows/zabbix_agent2-7.0.6-windows-amd64-openssl.msi"
)
$AlternativeURL32 = @(
    "https://repo.zabbix.com/zabbix/7.0/windows/zabbix_agent2-7.0.6-windows-i386-openssl.msi"
)

# Funcao para log com timestamp (compativel PS 2.0+)
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )

    # Compatibilidade com versoes antigas do PowerShell
    try {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    } catch {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }

    $logMessage = "[$timestamp] [$Level] $Message"

    # Usar Write-Host com cores se disponivel (PS 2.0+)
    if ($PSVersionTable.PSVersion.Major -ge 2) {
        switch($Level) {
            "ERROR" {
                try { Write-Host $logMessage -ForegroundColor Red }
                catch { Write-Host $logMessage }
            }
            "WARNING" {
                try { Write-Host $logMessage -ForegroundColor Yellow }
                catch { Write-Host $logMessage }
            }
            "SUCCESS" {
                try { Write-Host $logMessage -ForegroundColor Green }
                catch { Write-Host $logMessage }
            }
            default {
                Write-Host $logMessage
            }
        }
    } else {
        # Fallback para versoes muito antigas
        Write-Output $logMessage
    }
}

# Funcao para verificar se e administrador (compativel PS 2.0+)
function Test-Administrator {
    try {
        # Metodo preferido para PS 2.0+
        $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
        $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
        return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    } catch {
        # Fallback para versoes muito antigas
        try {
            $identity = [System.Security.Principal.WindowsIdentity]::GetCurrent()
            $principal = New-Object System.Security.Principal.WindowsPrincipal($identity)
            return $principal.IsInRole([System.Security.Principal.WindowsBuiltInRole]::Administrator)
        } catch {
            # Ultimo fallback - assumir que nao e admin se nao conseguir verificar
            Write-Log "Nao foi possivel verificar privilegios de administrador" "WARNING"
            return $false
        }
    }
}

# Funcao para configurar protocolos SSL/TLS (compativel PS 2.0+)
function Initialize-SecurityProtocols {
    Write-Log "Configurando protocolos de seguranca..."

    try {
        # Verificar versao do PowerShell de forma compativel
        if ($PSVersionTable -and $PSVersionTable.PSVersion) {
            $psVersion = $PSVersionTable.PSVersion.Major
            Write-Log "PowerShell versao $psVersion"
        } else {
            Write-Log "PowerShell versao 2 ou anterior"
            $psVersion = 2
        }

        # Verificar protocolos disponiveis
        try {
            $availableProtocols = [System.Net.ServicePointManager]::SecurityProtocol
            Write-Log "Protocolos SSL/TLS disponíveis - $availableProtocols"
        } catch {
            Write-Log "Nao foi possivel verificar protocolos disponiveis"
        }

        # Configurar protocolos de forma compativel
        try {
            if ([Net.SecurityProtocolType].GetEnumNames() -contains "Tls12") {
                [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12 -bor [Net.SecurityProtocolType]::Tls11 -bor [Net.SecurityProtocolType]::Tls
                Write-Log "TLS 1.2, 1.1 e 1.0 habilitados" "SUCCESS"
            } elseif ([Net.SecurityProtocolType].GetEnumNames() -contains "Tls11") {
                [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls11 -bor [Net.SecurityProtocolType]::Tls
                Write-Log "TLS 1.1 e 1.0 habilitados" "WARNING"
            } else {
                [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls
                Write-Log "Apenas TLS 1.0 disponivel" "WARNING"
            }
        } catch {
            Write-Log "Erro ao configurar protocolos TLS: $($_.Exception.Message)" "WARNING"
        }

        # Verificar configuracao atual
        try {
            $currentProtocol = [System.Net.ServicePointManager]::SecurityProtocol
            Write-Log "Protocolo atual - $currentProtocol"
        } catch {
            Write-Log "Nao foi possivel verificar protocolo atual"
        }

        Write-Log "Protocolos SSL/TLS configurados com sucesso" "SUCCESS"
    }
    catch {
        Write-Log "Erro ao configurar protocolos SSL/TLS" "WARNING"
    }
}

# Funcao para detectar arquitetura (compativel PS 2.0+)
function Get-SystemArchitecture {
    try {
        # Metodo preferido para PS 3.0+
        if ([Environment]::Is64BitOperatingSystem) {
            return "x64"
        } else {
            return "x86"
        }
    } catch {
        # Fallback para PS 2.0
        try {
            $arch = $env:PROCESSOR_ARCHITECTURE
            if ($arch -eq "AMD64" -or $arch -eq "x64") {
                return "x64"
            } else {
                return "x86"
            }
        } catch {
            # Ultimo fallback
            if ((Get-WmiObject Win32_OperatingSystem).OSArchitecture -like "*64*") {
                return "x64"
            } else {
                return "x86"
            }
        }
    }
}

# Funcao para diagnostico do sistema
function Invoke-SystemDiagnosis {
    param([bool]$FixIssues = $false)
    
    Write-Log "=== DIAGNOSTICO DO SISTEMA ===" -Level "SUCCESS"
    $issuesFound = 0
    
    # Verificar privilegios de administrador
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    $isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    
    if ($isAdmin) {
        Write-Log "[OK] Executando como Administrador" -Level "SUCCESS"
    } else {
        Write-Log "[ERRO] NAO esta executando como Administrador" -Level "ERROR"
        $issuesFound++
    }
    
    # Verificar servicos do Zabbix
    $zabbixServices = Get-Service | Where-Object { $_.Name -like "*Zabbix*" }
    if ($zabbixServices) {
        Write-Log "[AVISO] Encontrados servicos do Zabbix" -Level "WARNING"
        $issuesFound++
        
        if ($FixIssues) {
            foreach ($service in $zabbixServices) {
                try {
                    if ($service.Status -eq "Running") {
                        Stop-Service -Name $service.Name -Force -TimeoutSec 30 -ErrorAction SilentlyContinue
                    }
                    & sc.exe delete $service.Name 2>&1 | Out-Null
                    Write-Log "Servico $($service.Name) removido" -Level "SUCCESS"
                }
                catch {
                    Write-Log "Erro ao remover servico $($service.Name)" -Level "WARNING"
                }
            }
        }
    } else {
        Write-Log "[OK] Nenhum servico do Zabbix encontrado" -Level "SUCCESS"
    }
    
    # Verificar processos do Zabbix
    $zabbixProcesses = Get-Process | Where-Object { $_.ProcessName -like "*zabbix*" }
    if ($zabbixProcesses) {
        Write-Log "[AVISO] Encontrados processos do Zabbix rodando" -Level "WARNING"
        $issuesFound++
        
        if ($FixIssues) {
            foreach ($proc in $zabbixProcesses) {
                try {
                    Stop-Process -Id $proc.Id -Force
                    Write-Log "Processo $($proc.ProcessName) terminado" -Level "SUCCESS"
                }
                catch {
                    Write-Log "Erro ao terminar processo" -Level "WARNING"
                }
            }
        }
    } else {
        Write-Log "[OK] Nenhum processo do Zabbix rodando" -Level "SUCCESS"
    }
    
    # Verificar instalacoes MSI existentes
    try {
        $zabbixProducts = Get-WmiObject -Class Win32_Product -Filter "Name LIKE '%Zabbix%'" -ErrorAction SilentlyContinue
        if ($zabbixProducts) {
            Write-Log "[AVISO] Encontradas instalacoes MSI do Zabbix" -Level "WARNING"
            $issuesFound++
            
            if ($FixIssues) {
                foreach ($product in $zabbixProducts) {
                    try {
                        $result = $product.Uninstall()
                        if ($result.ReturnValue -eq 0) {
                            Write-Log "Produto removido com sucesso" -Level "SUCCESS"
                        }
                    }
                    catch {
                        Write-Log "Erro ao remover produto MSI" -Level "WARNING"
                    }
                }
            }
        } else {
            Write-Log "[OK] Nenhuma instalacao MSI do Zabbix encontrada" -Level "SUCCESS"
        }
    }
    catch {
        Write-Log "Erro ao consultar produtos MSI" -Level "WARNING"
    }
    
    # Verificar diretorios do Zabbix
    $zabbixDirs = @(
        "C:\Program Files\Zabbix Agent",
        "C:\Program Files\Zabbix Agent 2",
        "C:\Program Files (x86)\Zabbix Agent",
        "C:\Program Files (x86)\Zabbix Agent 2"
    )
    
    $foundDirs = @()
    foreach ($dir in $zabbixDirs) {
        if (Test-Path $dir) {
            $foundDirs += $dir
            Write-Log "[AVISO] Diretorio encontrado $dir" -Level "WARNING"
        }
    }
    
    if ($foundDirs.Count -eq 0) {
        Write-Log "[OK] Nenhum diretorio do Zabbix encontrado" -Level "SUCCESS"
    } else {
        $issuesFound++
        if ($FixIssues) {
            foreach ($dir in $foundDirs) {
                try {
                    Remove-Item -Path $dir -Recurse -Force -ErrorAction Stop
                    Write-Log "Diretorio removido $dir" -Level "SUCCESS"
                }
                catch {
                    Write-Log "Erro ao remover diretorio" -Level "WARNING"
                }
            }
        }
    }
    
    # Resumo do diagnostico
    Write-Log "=== RESUMO DO DIAGNOSTICO ===" -Level "SUCCESS"
    if ($issuesFound -eq 0) {
        Write-Log "[OK] Sistema limpo - pronto para instalacao" -Level "SUCCESS"
        return $true
    } else {
        Write-Log "[AVISO] Encontrados $issuesFound problemas" -Level "WARNING"
        return $false
    }
}

# Funcao para download do instalador
function Download-ZabbixAgent {
    param([string]$Architecture)
    
    $downloadUrl = if ($Architecture -eq "x64") { $DownloadURL64 } else { $DownloadURL32 }
    $fileName = Split-Path $downloadUrl -Leaf
    $downloadPath = Join-Path $env:TEMP $fileName
    
    Write-Log "Baixando Zabbix Agent 2 v$ZabbixVersion - $Architecture"
    Write-Log "URL $downloadUrl"
    
    try {
        # Configurar protocolos de seguranca de forma compativel
        try {
            [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12 -bor [Net.SecurityProtocolType]::Tls11 -bor [Net.SecurityProtocolType]::Tls
        } catch {
            # Fallback para versoes antigas
            try {
                [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls11 -bor [Net.SecurityProtocolType]::Tls
            } catch {
                [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls
            }
        }

        # Remover arquivo anterior se existir
        if (Test-Path $downloadPath) {
            Remove-Item $downloadPath -Force -ErrorAction SilentlyContinue
        }

        # Tentar download com Invoke-WebRequest (PS 3.0+)
        if ($PSVersionTable.PSVersion.Major -ge 3) {
            try {
                Write-Log "Tentando download com Invoke-WebRequest..."
                Invoke-WebRequest -Uri $downloadUrl -OutFile $downloadPath -UseBasicParsing
                Write-Log "Download concluido com Invoke-WebRequest" "SUCCESS"
                return $downloadPath
            }
            catch {
                Write-Log "Invoke-WebRequest falhou" "WARNING"
            }
        }

        # Fallback para WebClient (compativel PS 2.0+)
        Write-Log "Tentando download com WebClient..."
        $webClient = $null
        try {
            $webClient = New-Object System.Net.WebClient
            $webClient.Headers.Add("User-Agent", "PowerShell-ZabbixInstaller/1.0")
            $webClient.DownloadFile($downloadUrl, $downloadPath)
            Write-Log "Download concluido com WebClient" "SUCCESS"
            return $downloadPath
        } finally {
            if ($webClient -ne $null) {
                try {
                    $webClient.Dispose()
                } catch {
                    # Ignorar erros de dispose
                }
            }
        }
    }
    catch {
        Write-Log "Erro no download principal" -Level "ERROR"
        
        # Tentar URLs alternativas
        $alternativeUrls = if ($Architecture -eq "x64") { $AlternativeURL64 } else { $AlternativeURL32 }
        
        foreach ($altUrl in $alternativeUrls) {
            try {
                Write-Log "Tentando URL alternativa $altUrl"
                Invoke-WebRequest -Uri $altUrl -OutFile $downloadPath -UseBasicParsing -TimeoutSec 300
                Write-Log "Download concluido com URL alternativa" -Level "SUCCESS"
                return $downloadPath
            }
            catch {
                Write-Log "Falha na URL alternativa" -Level "WARNING"
                continue
            }
        }
        
        throw "Nao foi possivel baixar o instalador do Zabbix Agent 2"
    }
}

# Funcao para instalar o Zabbix Agent 2
function Install-ZabbixAgent {
    param([string]$InstallerPath)

    Write-Log "Instalando Zabbix Agent 2..."

    # Verificar se o arquivo MSI existe
    if (-not (Test-Path $InstallerPath)) {
        throw "Arquivo MSI nao encontrado $InstallerPath"
    }

    $fileSize = (Get-Item $InstallerPath).Length
    Write-Log "Tamanho do arquivo MSI $([math]::Round($fileSize/1MB, 2)) MB"

    if ($fileSize -lt 1MB) {
        throw "Arquivo MSI parece estar corrompido"
    }

    # Parametros de instalacao silenciosa com log detalhado
    $logPath = Join-Path $env:TEMP "zabbix_install.log"
    $msiArgs = @(
        "/i", "`"$InstallerPath`"",
        "/quiet",
        "/norestart",
        "/l*v", "`"$logPath`"",
        "INSTALLDIR=`"$InstallPath`"",
        "SERVER=$ZabbixServerIP",
        "SERVERACTIVE=$ZabbixServerIP",
        "HOSTNAME=$AgentHostname",
        "ENABLEREMOTECOMMANDS=1",
        "LOGTYPE=file"
    )

    Write-Log "Executando msiexec com parametros de instalacao"
    Write-Log "Log de instalacao sera salvo em $logPath"

    try {
        $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $msiArgs -Wait -PassThru -NoNewWindow

        # Analisar codigo de saida do MSI
        switch ($process.ExitCode) {
            0 {
                Write-Log "Zabbix Agent 2 instalado com sucesso!" -Level "SUCCESS"
                return
            }
            1603 {
                $errorMsg = "Erro fatal durante instalacao (1603). Possiveis causas:`n"
                $errorMsg += "- Instalacao anterior corrompida`n"
                $errorMsg += "- Permissoes insuficientes`n"
                $errorMsg += "- Conflito com software existente"
                Write-Log $errorMsg -Level "ERROR"
            }
            1618 {
                Write-Log "Outra instalacao esta em andamento - Erro 1618" -Level "ERROR"
            }
            1619 {
                Write-Log "Pacote de instalacao nao encontrado - Erro 1619" -Level "ERROR"
            }
            1620 {
                Write-Log "Pacote de instalacao nao pode ser aberto - Erro 1620" -Level "ERROR"
            }
            1633 {
                Write-Log "Plataforma nao suportada - Erro 1633" -Level "ERROR"
            }
            default {
                Write-Log "MSI retornou codigo de erro desconhecido $($process.ExitCode)" -Level "ERROR"
            }
        }

        # Tentar ler o log de instalacao para mais detalhes
        if (Test-Path $logPath) {
            Write-Log "Analisando log de instalacao..." -Level "INFO"
            $logContent = Get-Content $logPath -Tail 20
            $errorLines = $logContent | Where-Object { $_ -match "error|failed|exception" }

            if ($errorLines) {
                Write-Log "Erros encontrados no log" -Level "ERROR"
                foreach ($line in $errorLines | Select-Object -First 3) {
                    Write-Log "  $line" -Level "ERROR"
                }
            }

            Write-Log "Log completo disponivel em $logPath" -Level "INFO"
        }

        throw "Instalacao falhou com codigo $($process.ExitCode)"
    }
    catch {
        Write-Log "Erro na instalacao $($_.Exception.Message)" -Level "ERROR"

        # Sugestoes de resolucao
        Write-Log "SUGESTOES PARA RESOLUCAO" -Level "WARNING"
        Write-Log "1. Execute o script com parametro -Force" -Level "INFO"
        Write-Log "2. Verifique se nao ha outro Zabbix Agent rodando" -Level "INFO"
        Write-Log "3. Tente executar como Administrador" -Level "INFO"
        Write-Log "4. Verifique o log detalhado em $logPath" -Level "INFO"

        throw
    }
}

# Funcao para configurar o arquivo de configuracao
function Configure-ZabbixAgent {
    $configPath = Join-Path $InstallPath "zabbix_agent2.conf"

    if (-not (Test-Path $configPath)) {
        Write-Log "Arquivo de configuracao nao encontrado $configPath" -Level "ERROR"
        return
    }

    Write-Log "Configurando arquivo $configPath"

    # Backup da configuracao original
    $backupPath = "$configPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item -Path $configPath -Destination $backupPath
    Write-Log "Backup criado $backupPath"

    # Configuracoes personalizadas
    $config = @"
# Configuracao do Zabbix Agent 2
# Gerado automaticamente em $(Get-Date)

# Servidor Zabbix
Server=$ZabbixServerIP
ServerActive=$ZabbixServerIP

# Identificacao do agente
Hostname=$AgentHostname

# Configuracoes de log
LogType=file
LogFile=$InstallPath\zabbix_agent2.log
LogFileSize=10

# Configuracoes de seguranca
EnableRemoteCommands=1
UnsafeUserParameters=0

# Configuracoes de performance
Timeout=30
Include=$InstallPath\zabbix_agent2.d\*.conf

# Configuracoes de rede
ListenPort=10050
StartAgents=3

# Buffer de dados
BufferSend=5
BufferSize=100
"@

    try {
        $config | Out-File -FilePath $configPath -Encoding UTF8 -Force
        Write-Log "Configuracao aplicada com sucesso!" -Level "SUCCESS"
    }
    catch {
        Write-Log "Erro ao configurar $($_.Exception.Message)" -Level "ERROR"
        throw
    }
}

# Funcao para configurar o servico
function Configure-ZabbixService {
    Write-Log "Configurando servico Zabbix Agent 2..."

    try {
        # Verificar se o servico existe
        $service = Get-Service -Name "Zabbix Agent 2" -ErrorAction SilentlyContinue

        if ($service) {
            # Configurar para inicializacao automatica
            Set-Service -Name "Zabbix Agent 2" -StartupType Automatic

            # Iniciar o servico
            Start-Service -Name "Zabbix Agent 2"

            # Verificar status
            $service = Get-Service -Name "Zabbix Agent 2"
            if ($service.Status -eq "Running") {
                Write-Log "Servico iniciado com sucesso!" -Level "SUCCESS"
            } else {
                Write-Log "Servico nao esta rodando. Status $($service.Status)" -Level "WARNING"
            }
        } else {
            Write-Log "Servico Zabbix Agent 2 nao encontrado" -Level "ERROR"
        }
    }
    catch {
        Write-Log "Erro ao configurar servico $($_.Exception.Message)" -Level "ERROR"
        throw
    }
}

# Funcao para configurar firewall
function Configure-Firewall {
    Write-Log "Configurando regras de firewall..."

    try {
        # Remover regras existentes
        Get-NetFirewallRule -DisplayName "*Zabbix*" -ErrorAction SilentlyContinue | Remove-NetFirewallRule -ErrorAction SilentlyContinue

        # Criar nova regra para porta 10050
        New-NetFirewallRule -DisplayName "Zabbix Agent 2" -Direction Inbound -Protocol TCP -LocalPort 10050 -Action Allow -Profile Any
        Write-Log "Regra de firewall criada para porta 10050" -Level "SUCCESS"
    }
    catch {
        Write-Log "Erro ao configurar firewall $($_.Exception.Message)" -Level "WARNING"
    }
}

# Funcao para testar conectividade
function Test-ZabbixConnectivity {
    Write-Log "Testando conectividade com servidor Zabbix..."

    try {
        # Testar ping
        $pingResult = Test-Connection -ComputerName $ZabbixServerIP -Count 2 -Quiet
        if ($pingResult) {
            Write-Log "Ping para $ZabbixServerIP OK" -Level "SUCCESS"
        } else {
            Write-Log "Ping para $ZabbixServerIP FALHOU" -Level "WARNING"
        }

        # Testar porta 10051 (Zabbix Server)
        $tcpTest = Test-NetConnection -ComputerName $ZabbixServerIP -Port 10051 -WarningAction SilentlyContinue
        if ($tcpTest.TcpTestSucceeded) {
            Write-Log "Conexao TCP para $ZabbixServerIP porta 10051 OK" -Level "SUCCESS"
        } else {
            Write-Log "Conexao TCP para $ZabbixServerIP porta 10051 FALHOU" -Level "WARNING"
        }
    }
    catch {
        Write-Log "Erro no teste de conectividade $($_.Exception.Message)" -Level "WARNING"
    }
}

# Funcao principal
function Main {
    Write-Log "=== INSTALACAO DO ZABBIX AGENT 2 PARA WINDOWS ===" -Level "SUCCESS"
    Write-Log "Versao $ZabbixVersion"
    Write-Log "Servidor Zabbix $ZabbixServerIP"
    Write-Log "Hostname $AgentHostname"
    Write-Log "Caminho de instalacao $InstallPath"

    # Verificar privilegios de administrador
    if (-not (Test-Administrator)) {
        Write-Log "Este script deve ser executado como Administrador!" -Level "ERROR"
        exit 1
    }

    # Configurar protocolos SSL/TLS
    Initialize-SecurityProtocols

    # Detectar arquitetura
    $architecture = Get-SystemArchitecture
    Write-Log "Arquitetura detectada $architecture"

    # Modo diagnostico apenas
    if ($DiagnoseOnly) {
        Write-Log "=== MODO DIAGNOSTICO ATIVADO ===" -Level "INFO"
        $systemClean = Invoke-SystemDiagnosis -FixIssues $false
        if ($systemClean) {
            Write-Log "Sistema pronto para instalacao do Zabbix Agent 2" -Level "SUCCESS"
            exit 0
        } else {
            Write-Log "Problemas encontrados. Execute novamente com -Force" -Level "WARNING"
            exit 1
        }
    }

    # Diagnostico e correcao automatica se Force estiver habilitado
    if ($Force) {
        Write-Log "=== EXECUTANDO DIAGNOSTICO E CORRECAO ===" -Level "INFO"
        $systemClean = Invoke-SystemDiagnosis -FixIssues $true
        if (-not $systemClean) {
            Write-Log "Alguns problemas nao puderam ser corrigidos automaticamente" -Level "WARNING"
        }
    } else {
        # Diagnostico basico sem correcao
        $systemClean = Invoke-SystemDiagnosis -FixIssues $false
        if (-not $systemClean) {
            Write-Log "Problemas detectados. Recomenda-se usar -Force" -Level "WARNING"
        }
    }

    try {
        # Determinar caminho do instalador
        if ($LocalMSIPath -and (Test-Path $LocalMSIPath)) {
            Write-Log "Usando arquivo MSI local $LocalMSIPath" -Level "SUCCESS"
            $installerPath = $LocalMSIPath
        } else {
            if ($LocalMSIPath) {
                Write-Log "Arquivo MSI local nao encontrado $LocalMSIPath" -Level "WARNING"
                Write-Log "Tentando download online..." -Level "INFO"
            }
            # Download do instalador
            $installerPath = Download-ZabbixAgent -Architecture $architecture
        }

        # Instalar Zabbix Agent 2
        Install-ZabbixAgent -InstallerPath $installerPath

        # Configurar arquivo de configuracao
        Configure-ZabbixAgent

        # Configurar servico
        Configure-ZabbixService

        # Configurar firewall
        Configure-Firewall

        # Testar conectividade
        Test-ZabbixConnectivity

        # Limpeza (apenas se foi baixado, nao se foi fornecido localmente)
        if (-not $LocalMSIPath -or -not (Test-Path $LocalMSIPath)) {
            Write-Log "Removendo arquivo temporario $installerPath"
            Remove-Item -Path $installerPath -Force -ErrorAction SilentlyContinue
        } else {
            Write-Log "Mantendo arquivo MSI local $installerPath"
        }

        Write-Log "=== INSTALACAO CONCLUIDA COM SUCESSO! ===" -Level "SUCCESS"
        Write-Log "O Zabbix Agent 2 esta instalado e rodando"
        Write-Log "Configuracao $InstallPath\zabbix_agent2.conf"
        Write-Log "Logs $InstallPath\zabbix_agent2.log"

    }
    catch {
        Write-Log "ERRO DURANTE A INSTALACAO $($_.Exception.Message)" -Level "ERROR"
        exit 1
    }
}

# Executar script principal
Main
