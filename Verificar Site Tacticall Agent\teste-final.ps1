# Teste final do script corrigido
Write-Host "=== TESTE FINAL - SCRIPT CORRIGIDO ==="

# Verificar se o arquivo existe
if (Test-Path "Set-TacticalSite-Cliente copy.ps1") {
    Write-Host "OK - Arquivo encontrado"
    
    # Tentar carregar sem executar
    try {
        $content = Get-Content "Set-TacticalSite-Cliente copy.ps1" -Raw
        Write-Host "OK - Conteudo carregado"
        
        # Verificar estrutura básica
        if ($content -match "param\(") {
            Write-Host "OK - Parametros encontrados"
        }
        
        if ($content -match "function Get-LocalIP") {
            Write-Host "OK - Funcao Get-LocalIP encontrada"
        }
        
        if ($content -match "if.*WhatIf") {
            Write-Host "OK - Modo WhatIf implementado"
        }
        
        if ($content -match "Write-Verbose") {
            Write-Host "OK - Modo Verbose implementado"
        }
        
        Write-Host ""
        Write-Host "RESULTADO: Script esta pronto para uso!"
        Write-Host ""
        Write-Host "Comandos para testar:"
        Write-Host "  Get-Help .\Set-TacticalSite-Cliente copy.ps1"
        Write-Host "  .\Set-TacticalSite-Cliente copy.ps1 -WhatIf"
        Write-Host "  .\Set-TacticalSite-Cliente copy.ps1 -Verbose"
        
    } catch {
        Write-Host "ERRO: $($_.Exception.Message)"
    }
} else {
    Write-Host "ERRO - Arquivo nao encontrado"
}

Write-Host ""
Write-Host "=== FIM DO TESTE ==="
