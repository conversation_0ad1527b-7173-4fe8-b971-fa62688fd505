#!/bin/bash

# Tactical RMM Agent - Instalador All-in-One
# Autor: <PERSON> Matheus - NVirtual
# Data: 2025-01-21
# Versão: 2.0.0
#
# Script completo para instalação do Tactical RMM Agent
# Apenas pergunta: <PERSON><PERSON><PERSON>, Client ID e Site ID
# Faz todo o resto automaticamente

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Funções de log
log() { echo -e "${GREEN}[$(date '+%H:%M:%S')] $1${NC}"; }
error() { echo -e "${RED}[ERRO] $1${NC}"; exit 1; }
warning() { echo -e "${YELLOW}[AVISO] $1${NC}"; }
info() { echo -e "${BLUE}[INFO] $1${NC}"; }
title() { echo -e "${CYAN}$1${NC}"; }

# Banner
clear
echo "
████████╗ █████╗  ██████╗████████╗██╗ ██████╗ █████╗ ██╗         ██████╗ ███╗   ███╗███╗   ███╗
╚══██╔══╝██╔══██╗██╔════╝╚══██╔══╝██║██╔════╝██╔══██╗██║         ██╔══██╗████╗ ████║████╗ ████║
   ██║   ███████║██║        ██║   ██║██║     ███████║██║         ██████╔╝██╔████╔██║██╔████╔██║
   ██║   ██╔══██║██║        ██║   ██║██║     ██╔══██║██║         ██╔══██╗██║╚██╔╝██║██║╚██╔╝██║
   ██║   ██║  ██║╚██████╗   ██║   ██║╚██████╗██║  ██║███████╗    ██║  ██║██║ ╚═╝ ██║██║ ╚═╝ ██║
   ╚═╝   ╚═╝  ╚═╝ ╚═════╝   ╚═╝   ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝    ╚═╝  ╚═╝╚═╝     ╚═╝╚═╝     ╚═╝
   
"
title "                           INSTALADOR ALL-IN-ONE - TACTICAL RMM"
title "                              Desenvolvido por Paulo Matheus - NVirtual"
echo
clear
echo "                                                                    
                                                                               
               ████                                                            
  █████████████████                                                            
  ██ ███     ██ ███ ██      ███ ██ █████████  ██████ ███      ██     ███     ██       
  ██ ████    ██ ███ ███    ███  ██ ██████████ ██████ ███      ██   ██████    ███      
  ██ ██████  ██ ██  ████  ███   ██ ███    ███   ██   ███      ██  ███ ████   ███      
  ██ ██  ██████ ██    ██████    ██ ███  ████    ██    ██      ██ ███   ████  ███      
  ██ ██    ████ ██     ████     ██ ███   ████   ██    █████████ ███      ███ █████████
 ██████     ███ ██      ██      ██ ██      ███  ██       █████  ██        ██ █████████
 ████████████████                                                     
 ████                                                                 
                                                                       
                                                                               
"
title "                           INSTALADOR ALL-IN-ONE - TACTICAL RMM"
title "                              Desenvolvido por Paulo Matheus - NVirtual"
echo

# Verificações iniciais
if [[ $EUID -ne 0 ]]; then
    error "Este script deve ser executado como root. Execute: sudo $0"
fi

log "✅ Executando como root - OK"

# Detectar sistema e arquitetura
ARCHITECTURE=$(uname -m)
IS_ARM=false

if [[ "$ARCHITECTURE" == "aarch64" ]] || [[ "$ARCHITECTURE" == "armv7l" ]] || [[ "$ARCHITECTURE" == "arm64" ]]; then
    IS_ARM=true
    log "Arquitetura ARM detectada: $ARCHITECTURE"
else
    log "Arquitetura x86_64 detectada: $ARCHITECTURE"
fi

# Detectar sistema operacional
if grep -q "Ubuntu" /etc/os-release; then
    OS_VERSION=$(grep VERSION_ID /etc/os-release | cut -d'"' -f2)
    log "Ubuntu $OS_VERSION detectado"
elif grep -q "Debian" /etc/os-release; then
    log "Debian detectado"
else
    log "Sistema Linux genérico detectado"
fi

# Configurações fixas da NVirtual
TACTICAL_API_URL="https://api.centralmesh.nvirtual.com.br"
TACTICAL_AUTH_KEY="ecd275ac5baa7e615674a38f2de333f00dd2635e179f9a08e4026db2e5856ae3"

# URLs do Mesh Agent baseadas na arquitetura
if [[ "$IS_ARM" == "true" ]]; then
    TACTICAL_MESH_URL="https://mesh.centralmesh.nvirtual.com.br/meshagents?id=7Nss2LHe67mTwByGHQ3H3lOI4x8Awfk6kwbQgxSMMq%40qIJKjK6OOSBMWfXBYgPlb&installflags=0&meshinstall=26"
else
    TACTICAL_MESH_URL="https://mesh.centralmesh.nvirtual.com.br/meshagents?id=7Nss2LHe67mTwByGHQ3H3lOI4x8Awfk6kwbQgxSMMq%40qIJKjK6OOSBMWfXBYgPlb&installflags=2&meshinstall=6"
fi

# Solicitar informações do usuário
log "Configuração do Tactical RMM Agent"
echo

# Tipo de agente
echo "Tipos de agente disponíveis:"
echo "  1. server      - Para servidores"
echo "  2. workstation - Para estações de trabalho"
echo
while true; do
    read -p "Escolha o tipo (1 ou 2): " AGENT_TYPE_CHOICE
    case $AGENT_TYPE_CHOICE in
        1) AGENT_TYPE="server"; break ;;
        2) AGENT_TYPE="workstation"; break ;;
        *) warning "Escolha 1 ou 2!" ;;
    esac
done

# Client ID
echo
while true; do
    read -p "Digite o ID do Cliente do Tactical (número): " CLIENT_ID
    if [[ "$CLIENT_ID" =~ ^[0-9]+$ ]]; then
        break
    else
        warning "Client ID deve ser um número!"
    fi
done

# Site ID
echo
while true; do
    read -p "Digite o ID do Site (Filial) do Tactical (número): " SITE_ID
    if [[ "$SITE_ID" =~ ^[0-9]+$ ]]; then
        break
    else
        warning "Site ID deve ser um número!"
    fi
done

# Confirmação
echo
info "Configurações:"
info "• Tipo: $AGENT_TYPE"
info "• Client ID: $CLIENT_ID"
info "• Site ID: $SITE_ID"
info "• Arquitetura: $ARCHITECTURE"
echo

read -p "Confirma a instalação? (s/N): " CONFIRM
if [[ ! "$CONFIRM" =~ ^[Ss]$ ]]; then
    error "Instalação cancelada pelo usuário."
fi

# Início da instalação
echo
log "🚀 Iniciando instalação do Tactical RMM Agent..."

# Atualizar sistema
log "📦 Atualizando sistema..."
apt-get update -y >/dev/null 2>&1

# Instalar dependências
log "🔧 Instalando dependências..."
apt-get install -y curl wget unzip build-essential >/dev/null 2>&1

# Baixar script do Tactical RMM
log "⬇️  Baixando script do Tactical RMM..."
cd /tmp

# URLs alternativas para download
DOWNLOAD_URLS=(
    "https://raw.githubusercontent.com/netvolt/LinuxRMM-Script/main/rmmagent-linux.sh"
    "https://github.com/netvolt/LinuxRMM-Script/raw/main/rmmagent-linux.sh"
    "https://raw.githubusercontent.com/netvolt/LinuxRMM-Script/master/rmmagent-linux.sh"
)

SCRIPT_DOWNLOADED=false
for url in "${DOWNLOAD_URLS[@]}"; do
    info "Tentando: $url"
    if wget --timeout=30 --tries=2 "$url" -O rmmagent-linux.sh 2>/dev/null; then
        if [[ -f "rmmagent-linux.sh" ]] && [[ -s "rmmagent-linux.sh" ]]; then
            chmod +x rmmagent-linux.sh
            SCRIPT_DOWNLOADED=true
            log "✅ Script baixado com sucesso!"
            break
        fi
    fi
    warning "Falha no download, tentando próxima URL..."
done

if [[ "$SCRIPT_DOWNLOADED" == "false" ]]; then
    error "❌ Não foi possível baixar o script do Tactical RMM. Verifique sua conexão."
fi

# Executar instalação
log "🔨 Instalando Tactical RMM Agent..."
info "• Client ID: $CLIENT_ID"
info "• Site ID: $SITE_ID"
info "• Tipo: $AGENT_TYPE"

if ./rmmagent-linux.sh install "$TACTICAL_MESH_URL" "$TACTICAL_API_URL" "$CLIENT_ID" "$SITE_ID" "$TACTICAL_AUTH_KEY" "$AGENT_TYPE"; then
    log "✅ Tactical RMM Agent instalado com sucesso!"
else
    error "❌ Falha na instalação do Tactical RMM Agent!"
fi

# Aguardar serviços iniciarem
log "⏳ Aguardando serviços iniciarem..."
sleep 10

# Verificar instalação
log "🔍 Verificando instalação..."

# Verificar serviços
SERVICES_OK=true
for service in "tacticalagent" "rmmagent"; do
    if systemctl list-units --type=service | grep -q "$service"; then
        if systemctl is-active --quiet "$service"; then
            info "✅ Serviço $service: ATIVO"
        else
            warning "⚠️  Serviço $service: INATIVO"
            SERVICES_OK=false
        fi
    fi
done

# Verificar processos
PROCESSES_FOUND=false
for process in "tacticalagent" "rmmagent"; do
    if pgrep "$process" >/dev/null 2>&1; then
        info "✅ Processo $process: RODANDO"
        PROCESSES_FOUND=true
    fi
done

if [[ "$PROCESSES_FOUND" == "false" ]]; then
    warning "⚠️  Nenhum processo do Tactical RMM encontrado"
fi

# Testar conectividade
log "🌐 Testando conectividade..."
CONNECTIVITY_OK=true

for server in "mesh.centralmesh.nvirtual.com.br" "api.centralmesh.nvirtual.com.br"; do
    if curl -I "https://$server" --connect-timeout 10 >/dev/null 2>&1; then
        info "✅ $server: OK"
    else
        warning "⚠️  $server: FALHA"
        CONNECTIVITY_OK=false
    fi
done

# Limpeza
log "🧹 Limpando arquivos temporários..."
rm -f /tmp/rmmagent-linux.sh /tmp/rmmagent.tar.gz 2>/dev/null
rm -rf /tmp/rmmagent-master 2>/dev/null

# Resumo final
echo
title "=========================================="
title "           INSTALAÇÃO CONCLUÍDA"
title "=========================================="
echo

if [[ "$SERVICES_OK" == "true" ]] && [[ "$PROCESSES_FOUND" == "true" ]] && [[ "$CONNECTIVITY_OK" == "true" ]]; then
    log "🎉 INSTALAÇÃO BEM-SUCEDIDA!"
else
    warning "⚠️  INSTALAÇÃO CONCLUÍDA COM AVISOS"
fi

echo
info "📋 Resumo da Instalação:"
info "• Tipo de Agent: $AGENT_TYPE"
info "• Client ID: $CLIENT_ID"
info "• Site ID: $SITE_ID"
info "• Mesh Server: mesh.centralmesh.nvirtual.com.br"
info "• API Server: api.centralmesh.nvirtual.com.br"
info "• Arquitetura: $ARCHITECTURE"

echo
info "🔧 Comandos úteis (como root):"
echo "  systemctl status tacticalagent     # Ver status"
echo "  journalctl -u tacticalagent -f     # Ver logs"
echo "  systemctl restart tacticalagent    # Reiniciar"
echo "  systemctl stop tacticalagent       # Parar"
echo "  systemctl start tacticalagent      # Iniciar"

echo
title "Desenvolvido por Paulo Matheus - NVirtual"
title "=========================================="

log "🏁 Script finalizado!"
