export const LatestPointsColums = [
    {
        name: "Nome",
        selector: "nome",
        dataIndex: "nome",
    },
    {
        name: "Entrada",
        selector: "entrada",
        dataIndex: "entrada",
    },
    {
        name: "Inicio Intervalo",
        selector: "inicio_intervalo",
        dataIndex: "inicio_intervalo",
    },
    {
        name: "Retorno Intervalo",
        selector: "retorno_intervalo",
    },
    {
        name: "<PERSON><PERSON><PERSON>",
        selector: "saida",
        dataIndex: "saida",
    },
    {
        name: "Data",
        selector: "data",
        dataIndex: "data",
    },
    {
        name: "Observa<PERSON><PERSON><PERSON>",
        selector: "observacoes",
        dataIndex: "observacoes",
    },
];

export const LatestPointsData = [
    {
        nome: "<PERSON>",
        entrada: "08:00",
        inicio_intervalo: "11:00",
        retorno_intervalo: "12:00",
        saida: "17:00",
        data: "26/09/2020",
        observacoes: "-",
    },
    {
        nome: "<PERSON>",
        entrada: "08:00",
        inicio_intervalo: "11:00",
        retorno_intervalo: "12:00",
        saida: "17:00",
        data: "27/09/2020",
        observacoes: "-",
    },
    {
        nome: "Nicolas Marqui",
        entrada: "08:00",
        inicio_intervalo: "11:00",
        retorno_intervalo: "12:00",
        saida: "17:00",
        data: "28/09/2020",
        observacoes: "-",
    },
    {
        nome: "Nicolas Marqui",
        entrada: "08:00",
        inicio_intervalo: "11:00",
        retorno_intervalo: "12:00",
        saida: "17:00",
        data: "29/09/2020",
        observacoes: "-",
    },
    {
        nome: "Nicolas Marqui",
        entrada: "08:00",
        inicio_intervalo: "11:00",
        retorno_intervalo: "12:00",
        saida: "17:00",
        data: "29/09/2020",
        observacoes: "-",
    },
];

export const UsuariosData = [
    {
        id: 1,
        nome: "Cesar Augusto Seleguin Facioli",
        email: "<EMAIL>",
        funcaoId: 1,
        expedienteId: 1,
        ativo: "true",
    },
    {
        id: 11,
        nome: "Heitor Augusto Melecardi do Amaral",
        email: "<EMAIL>",
        funcaoId: 11,
        expedienteId: 1,
        ativo: "true",
    },
    {
        id: 1,
        nome: "Cesar Augusto Seleguin Facioli",
        email: "<EMAIL>",
        funcaoId: 1,
        expedienteId: 1,
        ativo: "true",
    },
    {
        id: 11,
        nome: "Heitor Augusto Melecardi do Amaral",
        email: "<EMAIL>",
        funcaoId: 11,
        expedienteId: 1,
        ativo: "true",
    },
    {
        id: 1,
        nome: "Cesar Augusto Seleguin Facioli",
        email: "<EMAIL>",
        funcaoId: 1,
        expedienteId: 1,
        ativo: "true",
    },
    {
        id: 11,
        nome: "Heitor Augusto Melecardi do Amaral",
        email: "<EMAIL>",
        funcaoId: 11,
        expedienteId: 1,
        ativo: "true",
    },
    {
        id: 1,
        nome: "Cesar Augusto Seleguin Facioli",
        email: "<EMAIL>",
        funcaoId: 1,
        expedienteId: 1,
        ativo: "true",
    },
    {
        id: 11,
        nome: "Heitor Augusto Melecardi do Amaral",
        email: "<EMAIL>",
        funcaoId: 11,
        expedienteId: 1,
        ativo: "true",
    },
    {
        id: 1,
        nome: "Cesar Augusto Seleguin Facioli",
        email: "<EMAIL>",
        funcaoId: 1,
        expedienteId: 1,
        ativo: "true",
    },
    {
        id: 11,
        nome: "Heitor Augusto Melecardi do Amaral",
        email: "<EMAIL>",
        funcaoId: 11,
        expedienteId: 1,
        ativo: "true",
    },
    {
        id: 1,
        nome: "Cesar Augusto Seleguin Facioli",
        email: "<EMAIL>",
        funcaoId: 1,
        expedienteId: 1,
        ativo: "true",
    },
    {
        id: 11,
        nome: "Heitor Augusto Melecardi do Amaral",
        email: "<EMAIL>",
        funcaoId: 11,
        expedienteId: 1,
        ativo: "true",
    },
];
