{"__inputs": [{"name": "DS_TESTE-GRAYLOG", "label": "Teste-Graylog", "description": "", "type": "datasource", "pluginId": "elasticsearch", "pluginName": "Elasticsearch"}], "__elements": {}, "__requires": [{"type": "datasource", "id": "elasticsearch", "name": "Elasticsearch", "version": "1.0.0"}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "10.1.4"}, {"type": "panel", "id": "piechart", "name": "Pie chart", "version": ""}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 8, "panels": [], "title": "Tentativas de Invasão bloqueadas", "type": "row"}, {"datasource": {"type": "elasticsearch", "uid": "${DS_TESTE-GRAYLOG}"}, "description": "Quantidade de tentativas de Invação do Roteador nos ultimso 30 dias", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 100000}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 0, "y": 1}, "hideTimeOverride": true, "id": 2, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "10.1.4", "targets": [{"alias": "", "bucketAggs": [{"field": "timestamp", "id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "datasource": {"type": "elasticsearch", "uid": "${DS_TESTE-GRAYLOG}"}, "metrics": [{"id": "1", "type": "count"}], "query": "streams:6729011a9ba2c029b5694ff6 AND source:avila AND timestamp:[now-30d TO now]", "refId": "A", "timeField": "timestamp"}], "timeFrom": "30d", "title": "Ultimos 30 Dias", "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "${DS_TESTE-GRAYLOG}"}, "description": "Quantidade de tentativas de Invação do Roteador nos ultimso 30 dias", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 100000}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 4, "y": 1}, "hideTimeOverride": true, "id": 4, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "10.1.4", "targets": [{"alias": "", "bucketAggs": [{"field": "timestamp", "id": "2", "settings": {"interval": "7d"}, "type": "date_histogram"}], "datasource": {"type": "elasticsearch", "uid": "${DS_TESTE-GRAYLOG}"}, "metrics": [{"id": "1", "type": "count"}], "query": "streams:6729011a9ba2c029b5694ff6 AND source:avila AND timestamp:[now-7d TO now]", "refId": "A", "timeField": "timestamp"}], "timeFrom": "7d", "title": "Ultimos 7 Dias", "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "${DS_TESTE-GRAYLOG}"}, "description": "Quantidade de tentativas de Invação do Roteador nos ultimso 30 dias", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 100000}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 8, "y": 1}, "hideTimeOverride": true, "id": 7, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "10.1.4", "targets": [{"alias": "", "bucketAggs": [{"field": "timestamp", "id": "2", "settings": {"interval": "7d"}, "type": "date_histogram"}], "datasource": {"type": "elasticsearch", "uid": "${DS_TESTE-GRAYLOG}"}, "metrics": [{"id": "1", "type": "count"}], "query": "streams:6729011a9ba2c029b5694ff6 AND source:avila AND timestamp:[now-1d TO now]", "refId": "A", "timeField": "timestamp"}], "timeFrom": "1d", "title": "Ultimas 24 Horas", "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "${DS_TESTE-GRAYLOG}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Tentativas", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "displayName": "Tentativas", "mappings": [{"options": {"0": {"index": 0, "text": "<PERSON><PERSON><PERSON>"}}, "type": "value"}], "min": 1, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 500}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 12, "y": 1}, "hideTimeOverride": false, "id": 6, "options": {"legend": {"calcs": ["sum"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "bucketAggs": [{"field": "timestamp", "id": "2", "settings": {"interval": "30d", "min_doc_count": "0", "timeZone": "utc", "trimEdges": "0"}, "type": "date_histogram"}], "datasource": {"type": "elasticsearch", "uid": "${DS_TESTE-GRAYLOG}"}, "metrics": [{"id": "1", "type": "count"}], "query": "streams:6729011a9ba2c029b5694ff6 AND source:avila", "refId": "A", "timeField": "timestamp"}], "timeFrom": "90d", "title": "Tentativa de Invasão - Trimestral", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "elasticsearch", "uid": "${DS_TESTE-GRAYLOG}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 14, "w": 12, "x": 0, "y": 9}, "id": 5, "options": {"displayLabels": ["name", "percent", "value"], "legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["allValues"], "fields": "/^Count$/", "values": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.1.4", "targets": [{"alias": "", "bucketAggs": [{"field": "source", "id": "2", "settings": {"min_doc_count": "1", "order": "desc", "orderBy": "_term", "size": "0"}, "type": "terms"}], "datasource": {"type": "elasticsearch", "uid": "${DS_TESTE-GRAYLOG}"}, "metrics": [{"id": "1", "type": "count"}], "query": "streams:6729011a9ba2c029b5694ff6 AND source:* AND timestamp:[now-30d TO now]", "refId": "A", "timeField": "timestamp"}], "title": "Por Filial - 30 Dias", "transformations": [{"id": "groupBy", "options": {"fields": {"Count": {"aggregations": []}, "source": {"aggregations": ["count"], "operation": "aggregate"}}}}], "type": "piechart"}, {"datasource": {"type": "elasticsearch", "uid": "${DS_TESTE-GRAYLOG}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 63, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "dashed"}}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#6ED0E0", "value": 20}, {"color": "#EAB839", "value": 50}, {"color": "dark-red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 12, "y": 12}, "hideTimeOverride": true, "id": 1, "options": {"legend": {"calcs": ["sum"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "bucketAggs": [{"field": "timestamp", "id": "2", "settings": {"interval": "1d", "timeZone": "utc", "trimEdges": "0"}, "type": "date_histogram"}], "datasource": {"type": "elasticsearch", "uid": "${DS_TESTE-GRAYLOG}"}, "metrics": [{"id": "1", "type": "count"}], "query": "streams:6729011a9ba2c029b5694ff6 AND source:* AND timestamp:[now-30d TO now]", "refId": "A", "timeField": "timestamp"}], "timeFrom": "30d", "title": "Tentativa de Invasão - 30 Dias", "transformations": [{"id": "filterByValue", "options": {"filters": [{"config": {"id": "equal", "options": {"value": "0"}}, "fieldName": "Count"}], "match": "all", "type": "exclude"}}], "type": "timeseries"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Tentativas de Invasão - Modelo Clientes 01", "uid": "e7c58576-a760-4b2d-8c23-8fc40258d129", "version": 4, "weekStart": ""}