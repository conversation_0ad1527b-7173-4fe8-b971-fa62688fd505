#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Script completo para geração de inventário Tactical RMM com envio por email e anexo Excel

.DESCRIPTION
    Script único e auto-contido para execução no servidor Tactical RMM (Ubuntu Server).
    Gera inventário completo do cliente, cria arquivo Excel com múltiplas abas e
    envia por email com anexo automático.

.PARAMETER ClientId
    ID do cliente no Tactical RMM (obrigatório)

.PARAMETER EmailTo
    Email(s) de destino separados por vírgula (obrigatório)

.PARAMETER EmailSubject
    Assunto do email (opcional - será gerado automaticamente)

.PARAMETER SMTPServer
    Servidor SMTP (padrão: smtp.gmail.com)

.PARAMETER SMTPPort
    Porta SMTP (padrão: 587)

.PARAMETER SMTPUser
    Usuário SMTP (obrigatório)

.PARAMETER SMTPPassword
    Senha SMTP (obrigatório)

.PARAMETER IncludeOffline
    Incluir estações offline (padrão: true)

.EXAMPLE
    # Execução básica
    pwsh Tactical-Inventory-Complete.ps1 -ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "senha123"

.NOTES
    Versão: 1.0
    Autor: NVirtual
    Data: 2025-01-03
    
    Este script é auto-contido e inclui:
    - Verificação e instalação automática do módulo ImportExcel
    - Geração de inventário via API Tactical RMM
    - Criação de arquivo Excel com múltiplas abas
    - Envio de email HTML com anexo Excel
    - Limpeza automática de arquivos temporários
    - Logs detalhados coloridos
#>

param(
    [Parameter(Mandatory=$true)]
    [int]$ClientId,
    
    [Parameter(Mandatory=$true)]
    [string]$EmailTo,
    
    [Parameter(Mandatory=$true)]
    [string]$SMTPUser,
    
    [Parameter(Mandatory=$true)]
    [string]$SMTPPassword,
    
    [string]$EmailSubject = "",
    [string]$SMTPServer = "smtp.gmail.com",
    [int]$SMTPPort = 587,
    [bool]$IncludeOffline = $true,
    [string]$OutputPath = "/tmp"
)

# ============================================================================
# CONFIGURAÇÕES GLOBAIS
# ============================================================================

# API Tactical RMM
$Global:API_URL = "https://api.centralmesh.nvirtual.com.br"
$Global:API_TOKEN = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"

# Configurações de execução
$Global:TEMP_DIR = "/tmp/tactical-inventory-$(Get-Date -Format 'yyyyMMddHHmmss')"
$Global:LOG_ENABLED = $true

# ============================================================================
# FUNÇÕES DE LOG E UTILIDADES
# ============================================================================

function Write-TacticalLog {
    param([string]$Message, [string]$Level = "INFO")
    
    if (-not $Global:LOG_ENABLED) { return }
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "ERROR" { 
            Write-Host $logMessage -ForegroundColor Red
            Write-Error $Message
        }
        "WARN"  { Write-Host $logMessage -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
        "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
        default { Write-Host $logMessage -ForegroundColor White }
    }
}

function Test-Prerequisites {
    Write-TacticalLog "Verificando pré-requisitos..."
    
    # Verificar se está no Linux
    if (-not $IsLinux) {
        Write-TacticalLog "Este script deve ser executado no Linux" "ERROR"
        return $false
    }
    
    # Verificar PowerShell Core
    if ($PSVersionTable.PSEdition -ne "Core") {
        Write-TacticalLog "PowerShell Core necessário" "ERROR"
        return $false
    }
    
    # Criar diretório temporário
    try {
        New-Item -Path $Global:TEMP_DIR -ItemType Directory -Force | Out-Null
        Write-TacticalLog "Diretório temporário criado: $Global:TEMP_DIR" "SUCCESS"
    } catch {
        Write-TacticalLog "Erro ao criar diretório temporário: $($_.Exception.Message)" "ERROR"
        return $false
    }
    
    return $true
}

function Install-RequiredModules {
    Write-TacticalLog "Verificando módulos necessários..."
    
    # Verificar e instalar ImportExcel
    try {
        Import-Module ImportExcel -ErrorAction Stop
        $version = (Get-Module ImportExcel).Version
        Write-TacticalLog "Módulo ImportExcel já instalado: v$version" "SUCCESS"
    } catch {
        Write-TacticalLog "Instalando módulo ImportExcel..." "WARN"
        try {
            Install-Module ImportExcel -Force -Scope CurrentUser -AllowClobber -Repository PSGallery
            Import-Module ImportExcel -Force
            $version = (Get-Module ImportExcel).Version
            Write-TacticalLog "Módulo ImportExcel instalado: v$version" "SUCCESS"
        } catch {
            Write-TacticalLog "Erro ao instalar ImportExcel: $($_.Exception.Message)" "ERROR"
            return $false
        }
    }
    
    return $true
}

# ============================================================================
# FUNÇÕES DE API
# ============================================================================

function Get-TacticalData {
    param([string]$Endpoint, [string]$Description)
    
    try {
        Write-TacticalLog "Buscando $Description da API..."
        
        $headers = @{
            "X-API-KEY" = $Global:API_TOKEN
            "Content-Type" = "application/json"
            "User-Agent" = "TacticalInventory-Complete/1.0"
        }
        
        $uri = "$($Global:API_URL)/$Endpoint"
        $data = Invoke-RestMethod -Uri $uri -Headers $headers -TimeoutSec 60 -Method Get
        
        Write-TacticalLog "$Description obtidos: $($data.Count) registros" "SUCCESS"
        return $data
        
    } catch {
        Write-TacticalLog "Erro ao buscar $Description`: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function Get-InventoryData {
    param([int]$ClientId, [bool]$IncludeOffline)
    
    Write-TacticalLog "Coletando dados de inventário para cliente ID: $ClientId"
    
    # Buscar dados da API
    $agents = Get-TacticalData -Endpoint "agents/" -Description "agentes"
    $clients = Get-TacticalData -Endpoint "clients/" -Description "clientes"
    $sites = Get-TacticalData -Endpoint "clients/sites/" -Description "sites"
    
    # Encontrar cliente específico
    $targetClient = $clients | Where-Object { $_.id -eq $ClientId } | Select-Object -First 1
    if (-not $targetClient) {
        throw "Cliente com ID $ClientId não encontrado"
    }
    
    $clientName = $targetClient.name
    Write-TacticalLog "Cliente encontrado: $clientName" "SUCCESS"
    
    # Filtrar agentes do cliente
    $clientAgents = $agents | Where-Object { $_.client -eq $ClientId }
    Write-TacticalLog "Agentes do cliente: $($clientAgents.Count)"
    
    # Filtrar offline se necessário
    if (-not $IncludeOffline) {
        $clientAgents = $clientAgents | Where-Object { 
            if ($_.last_seen) {
                $lastSeen = [DateTime]::Parse($_.last_seen)
                $timeDiff = (Get-Date) - $lastSeen
                $timeDiff.TotalHours -lt 24
            } else {
                $false
            }
        }
        Write-TacticalLog "Após filtro offline: $($clientAgents.Count) agentes"
    }
    
    if ($clientAgents.Count -eq 0) {
        throw "Nenhum agente encontrado para o cliente especificado"
    }
    
    # Processar dados dos agentes
    $inventoryData = @()
    foreach ($agent in $clientAgents) {
        $inventoryData += Format-AgentData -Agent $agent -Clients $clients -Sites $sites
    }
    
    return @{
        ClientName = $clientName
        InventoryData = $inventoryData
        TotalAgents = $inventoryData.Count
    }
}

function Format-AgentData {
    param($Agent, $Clients, $Sites)
    
    # Buscar informações do cliente e site
    $client = $Clients | Where-Object { $_.id -eq $Agent.client } | Select-Object -First 1
    $site = $Sites | Where-Object { $_.id -eq $Agent.site } | Select-Object -First 1
    
    # Determinar status
    $status = if ($Agent.last_seen) {
        $lastSeen = [DateTime]::Parse($Agent.last_seen)
        $timeDiff = (Get-Date) - $lastSeen
        if ($timeDiff.TotalMinutes -lt 5) { "Online" }
        elseif ($timeDiff.TotalHours -lt 24) { "Recente" }
        else { "Offline" }
    } else { "Desconhecido" }
    
    return [PSCustomObject]@{
        'ID_Agente' = $Agent.agent_id
        'Hostname' = $Agent.hostname
        'Cliente' = if ($client) { $client.name } else { "N/A" }
        'Site' = if ($site) { $site.name } else { "N/A" }
        'Status' = $status
        'Sistema_Operacional' = if ($Agent.operating_system) { $Agent.operating_system } else { "N/A" }
        'Versao_OS' = if ($Agent.plat) { $Agent.plat } else { "N/A" }
        'Arquitetura' = if ($Agent.arch) { $Agent.arch } else { "N/A" }
        'IP_Publico' = if ($Agent.public_ip) { $Agent.public_ip } else { "N/A" }
        'Agente_Versao' = if ($Agent.version) { $Agent.version } else { "N/A" }
        'Ultimo_Contato' = if ($Agent.last_seen) { 
            [DateTime]::Parse($Agent.last_seen).ToString("dd/MM/yyyy HH:mm:ss") 
        } else { "N/A" }
        'Tempo_Boot' = if ($Agent.boot_time) { 
            [DateTime]::Parse($Agent.boot_time).ToString("dd/MM/yyyy HH:mm:ss") 
        } else { "N/A" }
        'CPU_Modelo' = if ($Agent.cpu_model) { $Agent.cpu_model } else { "N/A" }
        'RAM_Total_GB' = if ($Agent.total_ram) { 
            [Math]::Round($Agent.total_ram / 1GB, 2) 
        } else { "N/A" }
        'Antivirus' = if ($Agent.antivirus) { $Agent.antivirus } else { "N/A" }
        'Dominio' = if ($Agent.domain) { $Agent.domain } else { "N/A" }
        'Usuario_Logado' = if ($Agent.logged_in_username) { $Agent.logged_in_username } else { "N/A" }
        'Servicos_Falhas' = if ($Agent.services_failing) { $Agent.services_failing } else { 0 }
        'Checks_Falhas' = if ($Agent.checks_failing) { $Agent.checks_failing } else { 0 }
        'Manutencao' = if ($Agent.maintenance_mode) { "Sim" } else { "Não" }
        'Monitoramento' = if ($Agent.monitoring_type) { $Agent.monitoring_type } else { "N/A" }
        'Data_Instalacao' = if ($Agent.install_time) { 
            [DateTime]::Parse($Agent.install_time).ToString("dd/MM/yyyy HH:mm:ss") 
        } else { "N/A" }
        'Observacoes' = if ($Agent.description) { $Agent.description } else { "" }
    }
}

# ============================================================================
# FUNÇÕES DE GERAÇÃO DE ARQUIVOS
# ============================================================================

function New-ExcelReport {
    param($InventoryData, $ClientName)

    try {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $fileName = "TacticalRMM_Inventario_${ClientName}_$timestamp.xlsx"
        $filePath = Join-Path $Global:TEMP_DIR $fileName

        Write-TacticalLog "Criando arquivo Excel: $fileName"

        # Agrupar dados por site
        $siteGroups = $InventoryData | Group-Object Site

        # Criar abas por site
        $isFirstSheet = $true
        foreach ($siteGroup in $siteGroups) {
            $siteName = ($siteGroup.Name -replace '[^\w\s-]', '').Trim()
            if ([string]::IsNullOrEmpty($siteName)) { $siteName = "Site_Desconhecido" }

            $siteData = $siteGroup.Group

            if ($isFirstSheet) {
                $siteData | Export-Excel -Path $filePath -WorksheetName $siteName -AutoSize -AutoFilter -FreezeTopRow -BoldTopRow
                $isFirstSheet = $false
            } else {
                $siteData | Export-Excel -Path $filePath -WorksheetName $siteName -AutoSize -AutoFilter -FreezeTopRow -BoldTopRow -Append
            }
        }

        # Criar aba de resumo
        $summaryData = @()
        foreach ($siteGroup in $siteGroups) {
            $siteStats = $siteGroup.Group
            $summaryData += [PSCustomObject]@{
                'Site' = $siteGroup.Name
                'Total_Estacoes' = $siteStats.Count
                'Online' = ($siteStats | Where-Object { $_.Status -eq "Online" }).Count
                'Recente' = ($siteStats | Where-Object { $_.Status -eq "Recente" }).Count
                'Offline' = ($siteStats | Where-Object { $_.Status -eq "Offline" }).Count
                'Falhas_Servicos' = ($siteStats | Measure-Object -Property Servicos_Falhas -Sum).Sum
                'Falhas_Checks' = ($siteStats | Measure-Object -Property Checks_Falhas -Sum).Sum
                'RAM_Total_GB' = ($siteStats | Where-Object { $_.RAM_Total_GB -ne "N/A" } | Measure-Object -Property RAM_Total_GB -Sum).Sum
            }
        }

        $summaryData | Export-Excel -Path $filePath -WorksheetName "Resumo_Geral" -AutoSize -AutoFilter -FreezeTopRow -BoldTopRow -Append

        Write-TacticalLog "Arquivo Excel criado: $filePath" "SUCCESS"
        return $filePath

    } catch {
        Write-TacticalLog "Erro ao criar Excel: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function New-HTMLReport {
    param($InventoryData, $ClientName, $ReportStats)

    $html = @"
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Relatório de Inventário - $ClientName</title>
    <style>
        body { font-family: 'Segoe UI', Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); }
        .header h1 { margin: 0; font-size: 32px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { margin: 8px 0; opacity: 0.9; font-size: 16px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 25px; border-radius: 15px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); text-align: center; transition: transform 0.3s ease; }
        .stat-card:hover { transform: translateY(-5px); }
        .stat-number { font-size: 36px; font-weight: bold; margin-bottom: 8px; }
        .stat-label { color: #666; font-size: 14px; text-transform: uppercase; letter-spacing: 1px; }
        .online { color: #10b981; }
        .recent { color: #f59e0b; }
        .offline { color: #ef4444; }
        .total { color: #6366f1; }
        .sites { color: #8b5cf6; }
        .attachment-note { background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%); border: 2px solid #0288d1; padding: 20px; border-radius: 15px; margin: 25px 0; }
        .attachment-note h3 { margin: 0 0 10px 0; color: #01579b; font-size: 18px; }
        .site-section { background: white; margin-bottom: 25px; border-radius: 15px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); overflow: hidden; }
        .site-header { background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); color: white; padding: 20px; }
        .site-header h2 { margin: 0; font-size: 24px; }
        .agents-table { width: 100%; border-collapse: collapse; }
        .agents-table th, .agents-table td { padding: 15px 12px; text-align: left; border-bottom: 1px solid #e5e7eb; }
        .agents-table th { background: #f8fafc; font-weight: 600; color: #374151; }
        .agents-table tr:hover { background: #f8fafc; }
        .status-online { color: #10b981; font-weight: bold; }
        .status-recent { color: #f59e0b; font-weight: bold; }
        .status-offline { color: #ef4444; font-weight: bold; }
        .footer { text-align: center; margin-top: 40px; padding: 25px; color: #6b7280; background: white; border-radius: 15px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); }
        .no-agents { padding: 40px; text-align: center; color: #9ca3af; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Relatório de Inventário Tactical RMM</h1>
            <p><strong>Cliente:</strong> $ClientName</p>
            <p><strong>Data/Hora:</strong> $(Get-Date -Format "dd/MM/yyyy HH:mm:ss")</p>
            <p><strong>Servidor:</strong> $(hostname) - Ubuntu Server</p>
            <p><strong>Gerado por:</strong> NVirtual Tactical RMM</p>
        </div>

        <div class="attachment-note">
            <h3>📎 Arquivo Excel Anexado</h3>
            <p>Este email inclui um arquivo Excel completo com todos os dados do inventário, organizados em abas separadas por site, além de uma aba de resumo com estatísticas consolidadas.</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number total">$($ReportStats.TotalAgents)</div>
                <div class="stat-label">Total de Estações</div>
            </div>
            <div class="stat-card">
                <div class="stat-number online">$($ReportStats.OnlineCount)</div>
                <div class="stat-label">Online</div>
            </div>
            <div class="stat-card">
                <div class="stat-number recent">$($ReportStats.RecentCount)</div>
                <div class="stat-label">Recente</div>
            </div>
            <div class="stat-card">
                <div class="stat-number offline">$($ReportStats.OfflineCount)</div>
                <div class="stat-label">Offline</div>
            </div>
            <div class="stat-card">
                <div class="stat-number sites">$($ReportStats.SitesCount)</div>
                <div class="stat-label">Sites</div>
            </div>
        </div>
"@

    # Adicionar seções por site
    $siteGroups = $InventoryData | Group-Object Site | Sort-Object Name

    foreach ($siteGroup in $siteGroups) {
        $siteName = $siteGroup.Name
        $siteAgents = $siteGroup.Group | Sort-Object Hostname

        $html += @"
        <div class="site-section">
            <div class="site-header">
                <h2>🏢 $siteName ($($siteAgents.Count) estações)</h2>
            </div>
"@

        if ($siteAgents.Count -gt 0) {
            $html += @"
            <table class="agents-table">
                <thead>
                    <tr>
                        <th>Hostname</th>
                        <th>Status</th>
                        <th>Sistema Operacional</th>
                        <th>CPU</th>
                        <th>RAM (GB)</th>
                        <th>Último Contato</th>
                        <th>Usuário</th>
                    </tr>
                </thead>
                <tbody>
"@

            foreach ($agent in $siteAgents) {
                $statusClass = switch ($agent.Status) {
                    "Online" { "status-online" }
                    "Recente" { "status-recent" }
                    "Offline" { "status-offline" }
                    default { "" }
                }

                $html += @"
                    <tr>
                        <td><strong>$($agent.Hostname)</strong></td>
                        <td class="$statusClass">$($agent.Status)</td>
                        <td>$($agent.Sistema_Operacional)</td>
                        <td>$($agent.CPU_Modelo)</td>
                        <td>$($agent.RAM_Total_GB)</td>
                        <td>$($agent.Ultimo_Contato)</td>
                        <td>$($agent.Usuario_Logado)</td>
                    </tr>
"@
            }

            $html += @"
                </tbody>
            </table>
"@
        } else {
            $html += '<div class="no-agents">Nenhuma estação encontrada neste site</div>'
        }

        $html += '</div>'
    }

    $html += @"
        <div class="footer">
            <p><strong>Relatório gerado automaticamente pelo Tactical RMM da NVirtual</strong></p>
            <p>Servidor: $(hostname) | PowerShell: $($PSVersionTable.PSVersion) | $(Get-Date -Format "dd/MM/yyyy HH:mm:ss")</p>
            <p>Para mais informações, entre em contato com o suporte técnico</p>
        </div>
    </div>
</body>
</html>
"@

    return $html
}

# ============================================================================
# FUNÇÕES DE EMAIL
# ============================================================================

function Send-InventoryReport {
    param($HtmlContent, $ExcelFilePath, $ClientName, $EmailTo, $EmailSubject, $SMTPConfig)

    try {
        # Definir assunto se não fornecido
        if ([string]::IsNullOrEmpty($EmailSubject)) {
            $EmailSubject = "📊 Relatório de Inventário - $ClientName - $(Get-Date -Format 'dd/MM/yyyy HH:mm')"
        }

        Write-TacticalLog "Preparando envio de email..."
        Write-TacticalLog "Destinatário(s): $EmailTo"
        Write-TacticalLog "Assunto: $EmailSubject"
        Write-TacticalLog "Anexo: $(Split-Path $ExcelFilePath -Leaf)"

        # Verificar se arquivo Excel existe
        if (-not (Test-Path $ExcelFilePath)) {
            throw "Arquivo Excel não encontrado: $ExcelFilePath"
        }

        $fileSize = (Get-Item $ExcelFilePath).Length
        Write-TacticalLog "Tamanho do anexo: $([Math]::Round($fileSize / 1KB, 2)) KB"

        # Configurar credenciais SMTP
        $securePassword = ConvertTo-SecureString $SMTPConfig.Password -AsPlainText -Force
        $credential = New-Object System.Management.Automation.PSCredential($SMTPConfig.User, $securePassword)

        # Preparar lista de destinatários
        $recipients = $EmailTo -split '[,;]' | ForEach-Object { $_.Trim() } | Where-Object { $_ -ne "" }

        # Preparar parâmetros do email
        $emailParams = @{
            To = $recipients
            From = $SMTPConfig.User
            Subject = $EmailSubject
            Body = $HtmlContent
            BodyAsHtml = $true
            SmtpServer = $SMTPConfig.Server
            Port = $SMTPConfig.Port
            UseSsl = $true
            Credential = $credential
            Attachments = $ExcelFilePath
            Encoding = [System.Text.Encoding]::UTF8
        }

        # Enviar email
        Write-TacticalLog "Enviando email via $($SMTPConfig.Server):$($SMTPConfig.Port)..."
        Send-MailMessage @emailParams

        Write-TacticalLog "Email enviado com sucesso!" "SUCCESS"
        Write-TacticalLog "Destinatários: $($recipients.Count)" "SUCCESS"
        Write-TacticalLog "Anexo incluído: $(Split-Path $ExcelFilePath -Leaf)" "SUCCESS"

        return $true

    } catch {
        Write-TacticalLog "Erro ao enviar email: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# ============================================================================
# FUNÇÕES DE LIMPEZA
# ============================================================================

function Remove-TempFiles {
    param([string]$TempDir)

    try {
        if (Test-Path $TempDir) {
            Remove-Item $TempDir -Recurse -Force
            Write-TacticalLog "Arquivos temporários removidos: $TempDir" "SUCCESS"
        }
    } catch {
        Write-TacticalLog "Aviso: Não foi possível remover arquivos temporários: $($_.Exception.Message)" "WARN"
    }
}

function Get-ReportStatistics {
    param($InventoryData)

    $onlineCount = ($InventoryData | Where-Object { $_.Status -eq "Online" }).Count
    $offlineCount = ($InventoryData | Where-Object { $_.Status -eq "Offline" }).Count
    $recentCount = ($InventoryData | Where-Object { $_.Status -eq "Recente" }).Count
    $sitesCount = ($InventoryData | Group-Object Site).Count

    return @{
        TotalAgents = $InventoryData.Count
        OnlineCount = $onlineCount
        OfflineCount = $offlineCount
        RecentCount = $recentCount
        SitesCount = $sitesCount
    }
}

# ============================================================================
# FUNÇÃO PRINCIPAL
# ============================================================================

function Start-TacticalInventoryReport {
    param($ClientId, $EmailTo, $SMTPUser, $SMTPPassword, $EmailSubject, $SMTPServer, $SMTPPort, $IncludeOffline)

    try {
        Write-TacticalLog "=== INICIANDO RELATÓRIO DE INVENTÁRIO TACTICAL RMM ===" "SUCCESS"
        Write-TacticalLog "Cliente ID: $ClientId"
        Write-TacticalLog "Email(s): $EmailTo"
        Write-TacticalLog "Servidor: $(hostname)"
        Write-TacticalLog "PowerShell: $($PSVersionTable.PSVersion)"
        Write-TacticalLog "Sistema: $(uname -a)"

        # Verificar pré-requisitos
        if (-not (Test-Prerequisites)) {
            throw "Falha na verificação de pré-requisitos"
        }

        # Instalar módulos necessários
        if (-not (Install-RequiredModules)) {
            throw "Falha na instalação de módulos"
        }

        # Configurar SMTP
        $smtpConfig = @{
            Server = $SMTPServer
            Port = $SMTPPort
            User = $SMTPUser
            Password = $SMTPPassword
        }

        # Coletar dados de inventário
        Write-TacticalLog "Coletando dados de inventário..."
        $inventoryResult = Get-InventoryData -ClientId $ClientId -IncludeOffline $IncludeOffline

        $clientName = $inventoryResult.ClientName
        $inventoryData = $inventoryResult.InventoryData

        # Calcular estatísticas
        $reportStats = Get-ReportStatistics -InventoryData $inventoryData

        Write-TacticalLog "=== ESTATÍSTICAS DO RELATÓRIO ===" "SUCCESS"
        Write-TacticalLog "Cliente: $clientName"
        Write-TacticalLog "Total de estações: $($reportStats.TotalAgents)"
        Write-TacticalLog "Online: $($reportStats.OnlineCount)"
        Write-TacticalLog "Recente: $($reportStats.RecentCount)"
        Write-TacticalLog "Offline: $($reportStats.OfflineCount)"
        Write-TacticalLog "Sites: $($reportStats.SitesCount)"

        # Gerar arquivo Excel
        Write-TacticalLog "Gerando arquivo Excel..."
        $excelFilePath = New-ExcelReport -InventoryData $inventoryData -ClientName $clientName

        # Gerar relatório HTML
        Write-TacticalLog "Gerando relatório HTML..."
        $htmlReport = New-HTMLReport -InventoryData $inventoryData -ClientName $clientName -ReportStats $reportStats

        # Enviar email com anexo
        Write-TacticalLog "Enviando relatório por email..."
        $emailSent = Send-InventoryReport -HtmlContent $htmlReport -ExcelFilePath $excelFilePath -ClientName $clientName -EmailTo $EmailTo -EmailSubject $EmailSubject -SMTPConfig $smtpConfig

        if ($emailSent) {
            Write-TacticalLog "=== RELATÓRIO ENVIADO COM SUCESSO ===" "SUCCESS"
            Write-TacticalLog "Cliente: $clientName"
            Write-TacticalLog "Destinatário(s): $EmailTo"
            Write-TacticalLog "Total de estações: $($reportStats.TotalAgents)"
            Write-TacticalLog "Arquivo Excel: $(Split-Path $excelFilePath -Leaf)"

            # Remover arquivos temporários
            Remove-TempFiles -TempDir $Global:TEMP_DIR

            return $true
        } else {
            Write-TacticalLog "Falha no envio do email" "ERROR"
            Write-TacticalLog "Arquivo Excel mantido em: $excelFilePath" "INFO"
            return $false
        }

    } catch {
        Write-TacticalLog "Erro durante a execução: $($_.Exception.Message)" "ERROR"
        Write-TacticalLog "Stack trace: $($_.ScriptStackTrace)" "ERROR"

        # Tentar limpar arquivos temporários mesmo em caso de erro
        try {
            Remove-TempFiles -TempDir $Global:TEMP_DIR
        } catch {
            # Ignorar erros de limpeza
        }

        return $false
    }
}

# ============================================================================
# EXECUÇÃO PRINCIPAL
# ============================================================================

try {
    $result = Start-TacticalInventoryReport -ClientId $ClientId -EmailTo $EmailTo -SMTPUser $SMTPUser -SMTPPassword $SMTPPassword -EmailSubject $EmailSubject -SMTPServer $SMTPServer -SMTPPort $SMTPPort -IncludeOffline $IncludeOffline

    if ($result) {
        Write-TacticalLog "=== EXECUÇÃO CONCLUÍDA COM SUCESSO ===" "SUCCESS"
        exit 0
    } else {
        Write-TacticalLog "=== EXECUÇÃO FALHOU ===" "ERROR"
        exit 1
    }
} catch {
    Write-TacticalLog "Erro crítico: $($_.Exception.Message)" "ERROR"
    exit 1
}

# ============================================================================
# FIM DO SCRIPT
# ============================================================================
