#!/bin/bash

# Script de configuração para preparar os arquivos do first boot

set -e

echo "=== Configurando First Boot para Raspberry Pi ==="

# Verificar se estamos executando como root
if [ "$EUID" -ne 0 ]; then
    echo "Este script deve ser executado como root (use sudo)"
    exit 1
fi

# Diretório atual do script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "Copiando arquivos..."

# Copiar o script principal
cp "$SCRIPT_DIR/first-boot.sh" /usr/local/bin/
chmod +x /usr/local/bin/first-boot.sh

# Copiar o serviço systemd
cp "$SCRIPT_DIR/first-boot.service" /etc/systemd/system/

# Recarregar systemd
systemctl daemon-reload

# Habilitar o serviço
systemctl enable first-boot.service

echo "=== Configuração concluída ==="
echo ""
echo "IMPORTANTE:"
echo "1. Edite /usr/local/bin/first-boot.sh e configure:"
echo "   - GITHUB_REPO: URL do seu repositório"
echo "   - SCRIPT_PATH: Caminho do script no repositório"
echo "   - GITHUB_TOKEN: Token se for repositório privado"
echo ""
echo "2. O script será executado automaticamente no próximo boot"
echo "3. Após a execução, o serviço será desabilitado automaticamente"
echo ""
echo "Para testar manualmente: sudo systemctl start first-boot.service"
echo "Para ver logs: sudo journalctl -u first-boot.service"
