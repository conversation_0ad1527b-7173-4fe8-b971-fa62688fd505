#!/bin/bash

# Script de instalação rápida do Tactical RMM Agent
# Autor: <PERSON> - NVirtual
# Data: 2025-01-21
# Versão: 1.0.0

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Banner
echo "
=========================================="
echo "  INSTALAÇÃO RÁPIDA - TACTICAL RMM"
echo "=========================================="
log "Instalação Rápida Tactical RMM - NVirtual"
echo

# Verificar se o script principal existe
MAIN_SCRIPT="install-tactical-only.sh"
if [[ ! -f "$MAIN_SCRIPT" ]]; then
    error "Script principal '$MAIN_SCRIPT' não encontrado no diretório atual!"
fi

# Verificar se o arquivo de configuração existe
CONFIG_FILE="config"
if [[ ! -f "$CONFIG_FILE" ]]; then
    warning "Arquivo de configuração 'config' não encontrado."
    info "Criando arquivo de configuração a partir do exemplo..."
    
    if [[ -f "config.example" ]]; then
        cp config.example config
        info "Arquivo 'config' criado a partir de 'config.example'"
        echo
        warning "⚠️  IMPORTANTE: Edite o arquivo 'config' antes de continuar!"
        info "Configure pelo menos:"
        info "• TACTICAL_CLIENT_ID"
        info "• TACTICAL_CLIENT_FILIAL"
        echo
        read -p "Pressione Enter após editar o arquivo 'config'..."
    else
        error "Arquivo 'config.example' também não encontrado!"
    fi
fi

# Carregar configurações
log "Carregando configurações do arquivo 'config'..."
source "$CONFIG_FILE"

# Validar configurações obrigatórias
if [[ -z "$TACTICAL_CLIENT_ID" ]]; then
    error "TACTICAL_CLIENT_ID não está definido no arquivo de configuração!"
fi

if [[ -z "$TACTICAL_CLIENT_FILIAL" ]]; then
    error "TACTICAL_CLIENT_FILIAL não está definido no arquivo de configuração!"
fi

# Mostrar resumo da configuração
echo
info "Configurações carregadas:"
info "• Cliente ID: $TACTICAL_CLIENT_ID"
info "• Filial: $TACTICAL_CLIENT_FILIAL"
info "• Tipo de Agente: ${TACTICAL_AGENT_TYPE:-server}"
info "• Modo Silencioso: ${SILENT_MODE:-false}"
echo

# Confirmar instalação se não estiver em modo silencioso
if [[ "${SILENT_MODE:-false}" != "true" ]]; then
    read -p "Confirma a instalação com essas configurações? (s/N): " CONFIRM
    if [[ ! "$CONFIRM" =~ ^[Ss]$ ]]; then
        log "Instalação cancelada pelo usuário."
        exit 0
    fi
fi

# Preparar argumentos para o script principal
SCRIPT_ARGS=""

# Verificar se deve pular atualização do sistema
if [[ "${SKIP_SYSTEM_UPDATE:-false}" == "true" ]]; then
    SCRIPT_ARGS="$SCRIPT_ARGS --skip-update"
fi

# Verificar se está em modo silencioso
if [[ "${SILENT_MODE:-false}" == "true" ]]; then
    SCRIPT_ARGS="$SCRIPT_ARGS --silent"
fi

# Executar instalação
log "Iniciando instalação do Tactical RMM Agent..."
echo

# Criar script temporário com as configurações
TEMP_SCRIPT="/tmp/tactical_install_auto.sh"
cat > "$TEMP_SCRIPT" << EOF
#!/bin/bash
export TACTICAL_CLIENT_ID="$TACTICAL_CLIENT_ID"
export TACTICAL_CLIENT_FILIAL="$TACTICAL_CLIENT_FILIAL"
export TACTICAL_AGENT_TYPE="${TACTICAL_AGENT_TYPE:-server}"
export SILENT_MODE="${SILENT_MODE:-false}"
export SKIP_SYSTEM_UPDATE="${SKIP_SYSTEM_UPDATE:-false}"
export DOWNLOAD_TIMEOUT="${DOWNLOAD_TIMEOUT:-30}"
export MAX_DOWNLOAD_ATTEMPTS="${MAX_DOWNLOAD_ATTEMPTS:-3}"

# Executar o script principal com as variáveis de ambiente
./$MAIN_SCRIPT --auto
EOF

chmod +x "$TEMP_SCRIPT"

# Executar instalação
if "$TEMP_SCRIPT"; then
    log "✅ Instalação concluída com sucesso!"
    
    # Verificar status após instalação
    if [[ -f "check-tactical-status.sh" ]]; then
        echo
        info "Executando verificação de status..."
        chmod +x check-tactical-status.sh
        ./check-tactical-status.sh 2
    fi
else
    error "❌ Falha na instalação!"
fi

# Limpar arquivo temporário
rm -f "$TEMP_SCRIPT"

echo
log "Script de instalação rápida finalizado!"
