@import '../../Styles/responsive';

.side__block{
    margin: 5px 0;

    span{
        font-size: 14px;
        color: rgba(0, 0, 0, 0.49);
        margin-bottom: 10px;
        display: none;
    }

    @include sm{
        margin: 25px 0;

        span{
            display: block;
        }
    }

    ul{
        li{
            position: relative;

            a{
                text-decoration: none;
            }

            // &:hover{
            //     .dropdown__menu{
            //         display: block;
            //         opacity: 1;
            //     }
            // }

            // .dropdown__menu{
            //     position: absolute;
            //     left: 99%;
            //     top: -34px;
            //     background-color: #ececec;
            //     transition: opacity 0.2s cubic-bezier(0.19, 1, 0.22, 1);
            //     padding-left: 20px;
            //     z-index: 20;
            //     width: 242px;
            //     padding: 20px;
            //     border-radius: 20px;
            //     // display: none;
            //     // opacity: 0;
                
            //     ul{
            //         li{
            //             a{
            //                 font-size: 14px;
            //             }
            //         }
            //     }
            // }
        }
    }
}