#!/bin/bash
# Script de Instalacao do Zabbix Agent para Linux - Versao Corrigida
# Autor: <PERSON> - NVirtual
# Versao: 1.3 - Corrigida para Tactical RMM

# Configuracoes padrao
ZABBIX_VERSION="7.0.10"
SERVICE_NAME="zabbix-agent"

# URLs de download
DOWNLOAD_URL_AMD64="https://repo.zabbix.com/zabbix/7.0/ubuntu/pool/main/z/zabbix/zabbix-agent_7.0.10-1+ubuntu22.04_amd64.deb"
DOWNLOAD_URL_ARM64="https://repo.zabbix.com/zabbix/7.0/ubuntu/pool/main/z/zabbix/zabbix-agent_7.0.10-1+ubuntu22.04_arm64.deb"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Funcao para log
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "SUCCESS")
            echo -e "[$timestamp] [${GREEN}SUCCESS${NC}] $message"
            ;;
        "WARNING")
            echo -e "[$timestamp] [${YELLOW}WARNING${NC}] $message"
            ;;
        "ERROR")
            echo -e "[$timestamp] [${RED}ERROR${NC}] $message"
            ;;
        *)
            echo -e "[$timestamp] [INFO] $message"
            ;;
    esac
}

# Funcao para processar argumentos
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --server)
                ZABBIX_SERVER="$2"
                shift 2
                ;;
            --hostname)
                AGENT_HOSTNAME="$2"
                shift 2
                ;;
            --force)
                FORCE_INSTALL="true"
                shift
                ;;
            --diagnose-only)
                DIAGNOSE_ONLY="true"
                shift
                ;;
            *)
                # Tentar extrair da string completa
                if [[ "$1" == *"--server"* ]]; then
                    ZABBIX_SERVER=$(echo "$1" | sed -n 's/.*--server[[:space:]]*\([^[:space:]]*\).*/\1/p')
                fi
                if [[ "$1" == *"--hostname"* ]]; then
                    AGENT_HOSTNAME=$(echo "$1" | sed -n 's/.*--hostname[[:space:]]*\([^[:space:]]*\).*/\1/p')
                fi
                if [[ "$1" == *"--force"* ]]; then
                    FORCE_INSTALL="true"
                fi
                shift
                ;;
        esac
    done
    
    # Variaveis de ambiente como fallback
    if [ -z "$ZABBIX_SERVER" ] && [ ! -z "$ZABBIX_SERVER_IP" ]; then
        ZABBIX_SERVER="$ZABBIX_SERVER_IP"
    fi
    
    if [ -z "$AGENT_HOSTNAME" ] && [ ! -z "$ZABBIX_HOSTNAME" ]; then
        AGENT_HOSTNAME="$ZABBIX_HOSTNAME"
    fi
    
    # Definir hostname padrao
    if [ -z "$AGENT_HOSTNAME" ]; then
        AGENT_HOSTNAME=$(hostname)
    fi
}

# Funcao para verificar root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_message "ERROR" "Este script deve ser executado como root"
        exit 1
    fi
}

# Funcao para detectar arquitetura
detect_architecture() {
    local arch=$(uname -m)
    case "$arch" in
        x86_64) echo "amd64" ;;
        aarch64|arm64) echo "arm64" ;;
        *) log_message "ERROR" "Arquitetura nao suportada: $arch"; exit 1 ;;
    esac
}

# Funcao para instalar dependencias
install_dependencies() {
    log_message "INFO" "Instalando dependencias..."
    
    if command -v apt-get > /dev/null 2>&1; then
        apt-get update -qq > /dev/null 2>&1
        apt-get install -y wget curl systemd > /dev/null 2>&1
    elif command -v yum > /dev/null 2>&1; then
        yum install -y wget curl systemd > /dev/null 2>&1
    elif command -v dnf > /dev/null 2>&1; then
        dnf install -y wget curl systemd > /dev/null 2>&1
    fi
    
    log_message "SUCCESS" "Dependencias instaladas"
}

# Funcao para limpeza completa (sempre executada)
cleanup_previous_installation() {
    log_message "INFO" "Removendo instalacao anterior (se existir)..."

    # Parar e desabilitar servicos
    systemctl stop zabbix-agent 2>/dev/null
    systemctl stop zabbix-agent2 2>/dev/null
    systemctl disable zabbix-agent 2>/dev/null
    systemctl disable zabbix-agent2 2>/dev/null

    # Terminar processos zabbix
    pkill -f zabbix 2>/dev/null
    sleep 2
    pkill -9 -f zabbix 2>/dev/null

    # Remover pacotes
    log_message "INFO" "Removendo pacotes zabbix existentes..."
    apt-get remove --purge -y zabbix-agent zabbix-agent2 2>/dev/null
    apt-get autoremove -y 2>/dev/null
    yum remove -y zabbix-agent zabbix-agent2 2>/dev/null
    dnf remove -y zabbix-agent zabbix-agent2 2>/dev/null

    # Remover diretorios e arquivos de configuracao
    log_message "INFO" "Removendo arquivos de configuracao..."
    rm -rf /etc/zabbix 2>/dev/null
    rm -rf /var/log/zabbix 2>/dev/null
    rm -rf /var/run/zabbix 2>/dev/null
    rm -rf /var/lib/zabbix 2>/dev/null
    rm -rf /usr/share/zabbix 2>/dev/null

    # Remover servicos systemd
    rm -f /etc/systemd/system/zabbix-agent.service 2>/dev/null
    rm -f /etc/systemd/system/zabbix-agent2.service 2>/dev/null
    rm -f /lib/systemd/system/zabbix-agent.service 2>/dev/null
    rm -f /lib/systemd/system/zabbix-agent2.service 2>/dev/null

    # Recarregar systemd
    systemctl daemon-reload
    systemctl reset-failed 2>/dev/null

    # Remover usuario zabbix se existir
    userdel zabbix 2>/dev/null
    groupdel zabbix 2>/dev/null

    log_message "SUCCESS" "Limpeza completa concluida"
}

# Funcao para download simples
download_zabbix_agent() {
    local architecture="$1"
    local download_url=""

    case "$architecture" in
        amd64) download_url="$DOWNLOAD_URL_AMD64" ;;
        arm64) download_url="$DOWNLOAD_URL_ARM64" ;;
        *)
            echo "ERROR: Arquitetura nao suportada" >&2
            exit 1
            ;;
    esac

    local filename=$(basename "$download_url")
    local download_path="/tmp/$filename"

    # Remover arquivo anterior
    rm -f "$download_path"

    # Download com wget (silencioso)
    if command -v wget > /dev/null 2>&1; then
        if wget -q "$download_url" -O "$download_path" 2>/dev/null; then
            if [ -f "$download_path" ] && [ -s "$download_path" ]; then
                echo "$download_path"
                return 0
            fi
        fi
    fi

    # Fallback para curl (silencioso)
    if command -v curl > /dev/null 2>&1; then
        if curl -sL "$download_url" -o "$download_path" 2>/dev/null; then
            if [ -f "$download_path" ] && [ -s "$download_path" ]; then
                echo "$download_path"
                return 0
            fi
        fi
    fi

    echo "ERROR: Falha no download" >&2
    exit 1
}

# Funcao para instalar pacote DEB
install_deb_package() {
    local package_path="$1"
    log_message "INFO" "Instalando pacote DEB..."

    # Atualizar cache de pacotes
    if command -v apt-get > /dev/null 2>&1; then
        log_message "INFO" "Atualizando cache de pacotes..."
        apt-get update -qq > /dev/null 2>&1
    fi

    # Instalar pacote
    if command -v dpkg > /dev/null 2>&1; then
        log_message "INFO" "Executando dpkg -i..."
        if dpkg -i "$package_path"; then
            log_message "SUCCESS" "Pacote DEB instalado com sucesso"

            # Parar servico que pode ter iniciado automaticamente
            systemctl stop zabbix-agent 2>/dev/null
            systemctl disable zabbix-agent 2>/dev/null

            return 0
        else
            # Corrigir dependencias
            log_message "INFO" "Corrigindo dependencias com apt-get..."
            if command -v apt-get > /dev/null 2>&1; then
                apt-get install -f -y

                # Tentar instalar novamente
                log_message "INFO" "Tentando instalar novamente apos correcao..."
                if dpkg -i "$package_path"; then
                    log_message "SUCCESS" "Pacote DEB instalado apos correcao"

                    # Parar servico que pode ter iniciado automaticamente
                    systemctl stop zabbix-agent 2>/dev/null
                    systemctl disable zabbix-agent 2>/dev/null

                    return 0
                fi
            fi
        fi
    fi

    log_message "ERROR" "Falha ao instalar pacote DEB"
    return 1
}

# Funcao para configurar
configure_zabbix_agent() {
    log_message "INFO" "Configurando Zabbix Agent..."

    local config_file="/etc/zabbix/zabbix_agentd.conf"

    # Criar diretorio se nao existir
    mkdir -p /etc/zabbix

    # Verificar se arquivo de configuração existe, se não, criar um básico
    if [ ! -f "$config_file" ]; then
        log_message "WARNING" "Arquivo de configuracao nao encontrado, criando um basico..."

        # Criar arquivo de configuração básico
        cat > "$config_file" << 'EOF'
# This is a configuration file for Zabbix agent daemon (Unix)
# To get more information about Zabbix, visit http://www.zabbix.com

############ GENERAL PARAMETERS #################

### Option: PidFile
#	Name of PID file.
#
# Mandatory: no
# Default:
# PidFile=/tmp/zabbix_agentd.pid

PidFile=/var/run/zabbix/zabbix_agentd.pid

### Option: LogType
#	Specifies where log messages are written to:
#		system  - syslog
#		file    - file specified with LogFile parameter
#		console - standard output
#
# Mandatory: no
# Default:
# LogType=file

### Option: LogFile
#	Log file name for LogType 'file' parameter.
#
# Mandatory: yes, if LogType is set to file, otherwise no
# Default:
# LogFile=

LogFile=/var/log/zabbix/zabbix_agentd.log

### Option: LogFileSize
#	Maximum size of log file in MB.
#	0 - disable automatic log rotation.
#
# Mandatory: no
# Range: 0-1024
# Default:
# LogFileSize=1

LogFileSize=0

### Option: DebugLevel
#	Specifies debug level:
#	0 - basic information about starting and stopping of Zabbix processes
#	1 - critical information
#	2 - error information
#	3 - warnings
#	4 - for debugging (produces lots of information)
#	5 - extended debugging (produces even more information)
#
# Mandatory: no
# Range: 0-5
# Default:
# DebugLevel=3

### Option: SourceIP
#	Source IP address for outgoing connections.
#
# Mandatory: no
# Default:
# SourceIP=

### Option: EnableRemoteCommands
#	Whether remote commands from Zabbix server are allowed.
#	0 - not allowed
#	1 - allowed
#
# Mandatory: no
# Default:
# EnableRemoteCommands=0

### Option: LogRemoteCommands
#	Enable logging of executed shell commands as warnings.
#	0 - disabled
#	1 - enabled
#
# Mandatory: no
# Default:
# LogRemoteCommands=0

############ PASSIVE CHECKS RELATED PARAMETERS #################

### Option: Server
#	List of comma delimited IP addresses, optionally in CIDR notation, or DNS names of Zabbix servers and Zabbix proxies.
#	Incoming connections will be accepted only from the hosts listed here.
#	If IPv6 support is enabled then '127.0.0.1', '::127.0.0.1', '::ffff:127.0.0.1' are treated equally
#	and '::/0' will allow any IPv4 or IPv6 address.
#	'0.0.0.0/0' can be used to allow any IPv4 address.
#	Example: Server=127.0.0.1,***********/24,::1,2001:db8::/32,zabbix.example.com
#
# Mandatory: yes, if StartAgents is not explicitly set to 0
# Default:
# Server=

Server=127.0.0.1

### Option: ListenPort
#	Agent will listen on this port for connections from the server.
#
# Mandatory: no
# Range: 1024-32767
# Default:
# ListenPort=10050

### Option: ListenIP
#	List of comma delimited IP addresses that the agent should listen on.
#	First IP address is sent to Zabbix server if connecting to it to retrieve list of active checks.
#
# Mandatory: no
# Default:
# ListenIP=0.0.0.0

### Option: StartAgents
#	Number of pre-forked instances of zabbix_agentd that process passive checks.
#	If set to 0, disables passive checks and the agent will not listen on any TCP port.
#
# Mandatory: no
# Range: 0-100
# Default:
# StartAgents=3

############ ACTIVE CHECKS RELATED PARAMETERS #################

### Option: ServerActive
#	List of comma delimited IP:port (or DNS name:port) pairs of Zabbix servers and Zabbix proxies for active checks.
#	If port is not specified, default port is used.
#	IPv6 addresses must be enclosed in square brackets if port for that host is specified.
#	If port is not specified, square brackets for IPv6 addresses are optional.
#	If this parameter is not specified, active checks are disabled.
#	Example: ServerActive=127.0.0.1:10051,zabbix.domain:10051,::1:10051,[::1]:10051,[2001:db8::1]:10051,zabbix.example.com:10051
#
# Mandatory: no
# Default:
# ServerActive=

ServerActive=127.0.0.1

### Option: Hostname
#	Unique, case sensitive hostname.
#	Required for active checks and must match hostname as configured on the server.
#	Value is acquired from HostnameItem if undefined.
#
# Mandatory: no
# Default:
# Hostname=

Hostname=Zabbix server

### Option: HostnameItem
#	Item used for generating Hostname if it is undefined. Ignored if Hostname is defined.
#	Does not support UserParameters or aliases.
#
# Mandatory: no
# Default:
# HostnameItem=system.hostname

### Option: HostMetadata
#	Optional parameter that defines host metadata.
#	Host metadata is used at host auto-registration process.
#	An agent will issue an error and not start if the value is over limit of 255 characters.
#	If not defined, value will be acquired from HostMetadataItem.
#
# Mandatory: no
# Range: 0-255 characters
# Default:
# HostMetadata=

### Option: HostMetadataItem
#	Optional parameter that defines an item used for getting host metadata.
#	Host metadata is used at host auto-registration process.
#	During an auto-registration request an agent will log a warning message if
#	the value returned by specified item is over limit of 255 characters.
#	This option is only used when HostMetadata is not defined.
#
# Mandatory: no
# Default:
# HostMetadataItem=

### Option: RefreshActiveChecks
#	How often list of active checks is refreshed, in seconds.
#
# Mandatory: no
# Range: 60-3600
# Default:
# RefreshActiveChecks=120

### Option: BufferSend
#	Do not keep data longer than N seconds in buffer.
#
# Mandatory: no
# Range: 1-3600
# Default:
# BufferSend=5

### Option: BufferSize
#	Maximum number of values in a memory buffer. The agent will send
#	all collected data to Zabbix Server or Proxy if the buffer is full.
#
# Mandatory: no
# Range: 2-65535
# Default:
# BufferSize=100

### Option: MaxLinesPerSecond
#	Maximum number of new lines the agent will send per second to Zabbix Server
#	or Proxy processing 'log' and 'logrt' active checks.
#	The provided value will be overridden by the parameter 'maxlines',
#	provided in 'log' or 'logrt' item keys.
#
# Mandatory: no
# Range: 1-1000
# Default:
# MaxLinesPerSecond=20

############ ADVANCED PARAMETERS #################

### Option: Alias
#	Sets an alias for an item key. It can be used to substitute long and complex item key with a smaller and simpler one.
#	Multiple Alias parameters may be present. Multiple parameters with the same Alias key are not allowed.
#	Different Alias keys may reference the same item key.
#	For example, to retrieve the ID of user 'zabbix':
#	Alias=zabbix.userid:vfs.file.regexp[/etc/passwd,^zabbix:.:([0-9]+),,,,\1]
#	Now shorthand key zabbix.userid may be used to retrieve data.
#	Aliases can be used in HostMetadataItem but not in HostnameItem parameters.
#
# Mandatory: no
# Range:
# Default:

### Option: Timeout
#	Spend no more than Timeout seconds on processing
#
# Mandatory: no
# Range: 1-30
# Default:
# Timeout=3

### Option: AllowRoot
#	Allow the agent to run as 'root'. If disabled and the agent is started by 'root', the agent
#	will try to switch to the user specified by the User configuration option instead.
#	Has no effect if started under a regular user.
#	0 - do not allow
#	1 - allow
#
# Mandatory: no
# Default:
# AllowRoot=0

### Option: User
#	Drop privileges to a specific, existing user on the system.
#	Only has effect if run as 'root' and AllowRoot is disabled.
#
# Mandatory: no
# Default:
# User=zabbix

User=zabbix

### Option: Include
#	You may include individual files or all files in a directory in the configuration file.
#	Installing Zabbix will create include directory in /usr/local/etc, unless modified during the compile time.
#
# Mandatory: no
# Default:
# Include=

Include=/etc/zabbix/zabbix_agentd.d/*.conf

# Include=/usr/local/etc/zabbix_agentd.userparams.conf
# Include=/usr/local/etc/zabbix_agentd.conf.d/
# Include=/usr/local/etc/zabbix_agentd.conf.d/*.conf

####### USER-DEFINED MONITORED PARAMETERS #######

### Option: UnsafeUserParameters
#	Allow all characters to be passed in arguments to user-defined parameters.
#	The following characters are not allowed:
#	\ ' " ` * ? [ ] { } ~ $ ! & ; ( ) < > | # @
#	Additionally, newline characters are not allowed.
#	0 - do not allow
#	1 - allow
#
# Mandatory: no
# Range: 0-1
# Default:
# UnsafeUserParameters=0

### Option: UserParameter
#	User-defined parameter to monitor. There can be several user-defined parameters.
#	Format: UserParameter=<key>,<shell command>
#	See 'zabbix_agentd' directory for examples.
#
# Mandatory: no
# Default:
# UserParameter=

####### LOADABLE MODULES #######

### Option: LoadModulePath
#	Full path to location of agent modules.
#	Default depends on compilation options.
#	To see the default path run command "zabbix_agentd --help".
#
# Mandatory: no
# Default:
# LoadModulePath=${libdir}/modules

### Option: LoadModule
#	Module to load at agent startup. Modules are used to extend functionality of the agent.
#	Formats:
#		LoadModule=<module.so>
#		LoadModule=<path/module.so>
#		LoadModule=</abs_path/module.so>
#	Either the module must be located in directory specified by LoadModulePath or the path must precede the module name.
#	If the preceding path is absolute (starts with '/') then LoadModulePath is ignored.
#	It is allowed to include multiple LoadModule parameters.
#
# Mandatory: no
# Default:
# LoadModule=

####### TLS-RELATED PARAMETERS #######

### Option: TLSConnect
#	How the agent should connect to server or proxy. Used for active checks.
#	Only one value can be specified:
#		unencrypted - connect without encryption
#		psk         - connect using TLS and a pre-shared key
#		cert        - connect using TLS and a certificate
#
# Mandatory: yes, if TLS certificate or PSK parameters are defined (even for 'unencrypted' connection)
# Default:
# TLSConnect=unencrypted

### Option: TLSAccept
#	What incoming connections to accept.
#	Multiple values can be specified, separated by comma:
#		unencrypted - accept connections without encryption
#		psk         - accept connections secured with TLS and a pre-shared key
#		cert        - accept connections secured with TLS and a certificate
#
# Mandatory: yes, if TLS certificate or PSK parameters are defined (even for 'unencrypted' connection)
# Default:
# TLSAccept=unencrypted

### Option: TLSCAFile
#	Full pathname of a file containing the top-level CA(s) certificates for
#	peer certificate verification.
#
# Mandatory: no
# Default:
# TLSCAFile=

### Option: TLSCRLFile
#	Full pathname of a file containing revoked certificates.
#
# Mandatory: no
# Default:
# TLSCRLFile=

### Option: TLSServerCertIssuer
#	Allowed server certificate issuer.
#
# Mandatory: no
# Default:
# TLSServerCertIssuer=

### Option: TLSServerCertSubject
#	Allowed server certificate subject.
#
# Mandatory: no
# Default:
# TLSServerCertSubject=

### Option: TLSCertFile
#	Full pathname of a file containing the agent certificate or certificate chain.
#
# Mandatory: no
# Default:
# TLSCertFile=

### Option: TLSKeyFile
#	Full pathname of a file containing the agent private key.
#
# Mandatory: no
# Default:
# TLSKeyFile=

### Option: TLSPSKIdentity
#	Unique, case sensitive string used to identify the pre-shared key.
#
# Mandatory: no
# Default:
# TLSPSKIdentity=

### Option: TLSPSKFile
#	Full pathname of a file containing the pre-shared key.
#
# Mandatory: no
# Default:
# TLSPSKFile=
EOF

        log_message "SUCCESS" "Arquivo de configuracao basico criado"
    fi

    # Backup da configuracao original
    cp "$config_file" "$config_file.backup"
    log_message "INFO" "Backup criado: $config_file.backup"

    # Modificar apenas os campos necessários usando sed
    log_message "INFO" "Modificando configuracoes especificas..."

    # Configurar Server (descomenta e define valor)
    sed -i "s/^# Server=.*/Server=$ZABBIX_SERVER/" "$config_file"
    sed -i "s/^Server=.*/Server=$ZABBIX_SERVER/" "$config_file"

    # Configurar Hostname (descomenta e define valor)
    sed -i "s/^# Hostname=.*/Hostname=$AGENT_HOSTNAME/" "$config_file"
    sed -i "s/^Hostname=.*/Hostname=$AGENT_HOSTNAME/" "$config_file"

    # Habilitar comandos remotos (descomenta e define valor)
    sed -i "s/^# EnableRemoteCommands=.*/EnableRemoteCommands=1/" "$config_file"
    sed -i "s/^EnableRemoteCommands=.*/EnableRemoteCommands=1/" "$config_file"

    # Verificar se as modificações foram aplicadas
    log_message "INFO" "Verificando configuracoes aplicadas..."

    local server_config=$(grep "^Server=" "$config_file" | head -1)
    local hostname_config=$(grep "^Hostname=" "$config_file" | head -1)
    local commands_config=$(grep "^EnableRemoteCommands=" "$config_file" | head -1)

    if [[ "$server_config" == "Server=$ZABBIX_SERVER" ]]; then
        log_message "SUCCESS" "Server configurado: $server_config"
    else
        log_message "WARNING" "Server pode nao ter sido configurado corretamente"
    fi

    if [[ "$hostname_config" == "Hostname=$AGENT_HOSTNAME" ]]; then
        log_message "SUCCESS" "Hostname configurado: $hostname_config"
    else
        log_message "WARNING" "Hostname pode nao ter sido configurado corretamente"
    fi

    if [[ "$commands_config" == "EnableRemoteCommands=1" ]]; then
        log_message "SUCCESS" "EnableRemoteCommands configurado: $commands_config"
    else
        log_message "WARNING" "EnableRemoteCommands pode nao ter sido configurado corretamente"
    fi

    # Definir permissoes
    chown zabbix:zabbix "$config_file" 2>/dev/null
    chmod 644 "$config_file"

    log_message "SUCCESS" "Configuracao aplicada usando arquivo original da instalacao"
}

# Funcao para configurar firewall
configure_firewall() {
    log_message "INFO" "Configurando firewall..."
    
    if command -v ufw > /dev/null 2>&1; then
        ufw allow 10050/tcp > /dev/null 2>&1
    elif command -v firewall-cmd > /dev/null 2>&1; then
        firewall-cmd --permanent --add-port=10050/tcp > /dev/null 2>&1
        firewall-cmd --reload > /dev/null 2>&1
    elif command -v iptables > /dev/null 2>&1; then
        iptables -A INPUT -p tcp --dport 10050 -j ACCEPT > /dev/null 2>&1
    fi
    
    log_message "SUCCESS" "Firewall configurado"
}

# Funcao para iniciar servico
start_zabbix_service() {
    log_message "INFO" "Iniciando servico..."
    
    systemctl daemon-reload
    systemctl enable zabbix-agent > /dev/null 2>&1
    
    if systemctl start zabbix-agent; then
        sleep 2
        if systemctl is-active --quiet zabbix-agent; then
            log_message "SUCCESS" "Servico iniciado com sucesso"
        else
            log_message "ERROR" "Servico nao esta rodando"
            exit 1
        fi
    else
        log_message "ERROR" "Falha ao iniciar servico"
        exit 1
    fi
}

# Funcao principal
main() {
    log_message "SUCCESS" "=== INSTALACAO DO ZABBIX AGENT PARA LINUX ==="
    log_message "INFO" "Servidor Zabbix: $ZABBIX_SERVER"
    log_message "INFO" "Hostname: $AGENT_HOSTNAME"
    
    check_root
    
    local architecture=$(detect_architecture)
    log_message "INFO" "Arquitetura: $architecture"
    
    install_dependencies

    # Sempre fazer limpeza completa antes de instalar
    cleanup_previous_installation

    if [ "$DIAGNOSE_ONLY" = "true" ]; then
        log_message "SUCCESS" "Diagnostico concluido - sistema pronto"
        exit 0
    fi
    
    # Download
    log_message "INFO" "Baixando Zabbix Agent v$ZABBIX_VERSION - $architecture"
    local installer_path=$(download_zabbix_agent "$architecture")

    # Verificar se download foi bem-sucedido
    if [ ! -f "$installer_path" ]; then
        log_message "ERROR" "Arquivo nao foi baixado: $installer_path"
        exit 1
    fi

    # Mostrar informações do arquivo baixado
    local file_size=$(stat -c%s "$installer_path" 2>/dev/null || stat -f%z "$installer_path" 2>/dev/null)
    local file_size_kb=$((file_size / 1024))
    log_message "SUCCESS" "Download concluido - Tamanho: ${file_size_kb}KB"
    log_message "INFO" "Arquivo: $installer_path"
    
    # Instalar
    if install_deb_package "$installer_path"; then
        log_message "SUCCESS" "Instalacao concluida"
    else
        log_message "ERROR" "Falha na instalacao"
        exit 1
    fi
    
    # Configurar
    configure_zabbix_agent
    configure_firewall
    start_zabbix_service
    
    # Limpeza
    rm -f "$installer_path"
    
    log_message "SUCCESS" "=== INSTALACAO CONCLUIDA COM SUCESSO! ==="
    log_message "INFO" "Configuracao: /etc/zabbix/zabbix_agentd.conf"
    log_message "INFO" "Logs: /var/log/zabbix/zabbix_agentd.log"
    log_message "INFO" "Status: systemctl status zabbix-agent"
}

# Processar argumentos e executar
parse_arguments "$@"

# Validar servidor obrigatorio
if [ -z "$ZABBIX_SERVER" ]; then
    log_message "ERROR" "Servidor Zabbix nao especificado"
    log_message "ERROR" "Use: --server IP_SERVIDOR ou defina ZABBIX_SERVER_IP=IP"
    exit 1
fi

# Executar instalacao
main
