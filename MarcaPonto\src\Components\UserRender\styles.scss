@import '../../Styles/responsive';

.user__grid--container{

    display: block;

    & > div{
        margin: 10px 0;
    }

    @include md{
        margin-top: 14px;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-template-rows: 1fr;
        gap: 10px 10px;
        grid-template-areas:
            "ponto espelho config";

        .ponto { grid-area: ponto; }
        .espelho { grid-area: espelho; }
        .config { grid-area: config; }

        & > div{
            margin: 0;
            max-height: 360px;
            overflow-y: auto;
        }
    }

    .grid__icon{
        display: flex;
        height: 80%;
        justify-content: center;
        align-items: center;
    }

}