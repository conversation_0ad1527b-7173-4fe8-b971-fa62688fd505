#!/bin/bash

# Script para atualizar Zabbix Proxy para versão 7.0 LTS
# Versão: 1.0
# Data: 2025-07-01
# Autor: <PERSON>

set -e  # Parar execução em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log com timestamp
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

# Configurações
ZABBIX_CONF_PATH="/etc/zabbix/zabbix_proxy.conf"
BACKUP_DIR="/tmp/zabbix_backup_$(date +%Y%m%d_%H%M%S)"
ZABBIX_SERVICE="zabbix-proxy"
ZABBIX_USER="zabbix"
ZABBIX_DB_PATH="/var/lib/zabbix/zabbix_proxy.db"
ZABBIX_DB_DIR="/var/lib/zabbix"

# Verificar se é executado como root
if [[ $EUID -ne 0 ]]; then
   log_error "Este script deve ser executado como root (sudo)"
   exit 1
fi

log "🚀 Iniciando atualização do Zabbix Proxy para versão 7.0 LTS"

# Função para detectar distribuição Linux
detect_distro() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        VER=$(lsb_release -sr)
    else
        log_error "Não foi possível detectar a distribuição Linux"
        exit 1
    fi

    log "Sistema detectado: $OS $VER"
}

# Função para verificar versão atual do Zabbix
check_current_version() {
    if command -v zabbix_proxy >/dev/null 2>&1; then
        CURRENT_VERSION=$(zabbix_proxy --version | head -n1 | awk '{print $3}')
        log "Versão atual do Zabbix Proxy: $CURRENT_VERSION"
    else
        log_warning "Zabbix Proxy não encontrado. Será feita instalação nova."
        CURRENT_VERSION="não instalado"
    fi
}

# Função para criar backup
create_backup() {
    log "📦 Criando backup da configuração e banco de dados..."

    # Criar diretório de backup
    mkdir -p "$BACKUP_DIR"

    # Backup do arquivo de configuração
    if [ -f "$ZABBIX_CONF_PATH" ]; then
        cp "$ZABBIX_CONF_PATH" "$BACKUP_DIR/zabbix_proxy.conf.backup"
        log_success "Backup da configuração criado: $BACKUP_DIR/zabbix_proxy.conf.backup"
    else
        log_warning "Arquivo de configuração não encontrado: $ZABBIX_CONF_PATH"
    fi

    # Backup do banco de dados SQLite3
    if [ -f "$ZABBIX_DB_PATH" ]; then
        log "Criando backup do banco de dados SQLite3..."
        cp "$ZABBIX_DB_PATH" "$BACKUP_DIR/zabbix_proxy.db.backup"

        # Criar dump SQL do banco para facilitar migração se necessário
        sqlite3 "$ZABBIX_DB_PATH" .dump > "$BACKUP_DIR/zabbix_proxy_dump.sql"

        # Verificar integridade do backup
        if sqlite3 "$BACKUP_DIR/zabbix_proxy.db.backup" "PRAGMA integrity_check;" | grep -q "ok"; then
            log_success "Backup do banco SQLite3 criado e verificado"
        else
            log_error "Erro na integridade do backup do banco de dados"
            exit 1
        fi
    else
        log_warning "Banco de dados SQLite3 não encontrado: $ZABBIX_DB_PATH"
    fi

    # Backup de outros arquivos importantes
    if [ -d "/etc/zabbix" ]; then
        cp -r /etc/zabbix "$BACKUP_DIR/etc_zabbix_backup"
        log_success "Backup do diretório /etc/zabbix criado"
    fi

    # Backup dos logs (últimos 100 linhas)
    if [ -f "/var/log/zabbix/zabbix_proxy.log" ]; then
        tail -n 100 /var/log/zabbix/zabbix_proxy.log > "$BACKUP_DIR/zabbix_proxy.log.backup"
        log_success "Backup dos logs criado"
    fi

    # Backup das permissões do diretório do banco
    if [ -d "$ZABBIX_DB_DIR" ]; then
        ls -la "$ZABBIX_DB_DIR" > "$BACKUP_DIR/db_permissions.txt"
        log_success "Backup das permissões do diretório do banco criado"
    fi

    log_success "Backup completo salvo em: $BACKUP_DIR"
}

# Função para parar serviços
stop_services() {
    log "🛑 Parando serviços do Zabbix..."

    if systemctl is-active --quiet $ZABBIX_SERVICE; then
        systemctl stop $ZABBIX_SERVICE
        log_success "Serviço $ZABBIX_SERVICE parado"
    else
        log_warning "Serviço $ZABBIX_SERVICE não estava rodando"
    fi
}

# Função para atualizar repositório e instalar Zabbix 7.0 LTS
install_zabbix_7() {
    log "📥 Instalando Zabbix 7.0 LTS..."

    case "$OS" in
        *"Ubuntu"*|*"Debian"*)
            install_zabbix_debian
            ;;
        *"CentOS"*|*"Red Hat"*|*"Rocky"*|*"AlmaLinux"*)
            install_zabbix_rhel
            ;;
        *)
            log_error "Distribuição não suportada: $OS"
            exit 1
            ;;
    esac
}

# Instalação para Debian/Ubuntu
install_zabbix_debian() {
    log "Instalando para Debian/Ubuntu com SQLite3..."

    # Remover repositórios antigos do Zabbix
    rm -f /etc/apt/sources.list.d/zabbix*

    # Baixar e instalar repositório Zabbix 7.0 LTS
    wget https://repo.zabbix.com/zabbix/7.0/ubuntu/pool/main/z/zabbix-release/zabbix-release_7.0-2+ubuntu$(lsb_release -rs)_all.deb
    dpkg -i zabbix-release_7.0-2+ubuntu$(lsb_release -rs)_all.deb

    # Atualizar lista de pacotes
    apt update

    # Instalar Zabbix Proxy com SQLite3
    apt install -y zabbix-proxy-sqlite3 zabbix-sql-scripts sqlite3

    log_success "Zabbix 7.0 LTS com SQLite3 instalado com sucesso"
}

# Instalação para RHEL/CentOS
install_zabbix_rhel() {
    log "Instalando para RHEL/CentOS com SQLite3..."

    # Remover repositórios antigos
    rm -f /etc/yum.repos.d/zabbix*

    # Instalar repositório Zabbix 7.0 LTS
    rpm -Uvh https://repo.zabbix.com/zabbix/7.0/rhel/$(rpm -E %{rhel})/x86_64/zabbix-release-7.0-2.el$(rpm -E %{rhel}).noarch.rpm

    # Limpar cache
    dnf clean all

    # Instalar Zabbix Proxy com SQLite3
    dnf install -y zabbix-proxy-sqlite3 zabbix-sql-scripts sqlite

    log_success "Zabbix 7.0 LTS com SQLite3 instalado com sucesso"
}

# Função para restaurar configuração e banco de dados
restore_configuration() {
    log "🔄 Restaurando configuração e banco de dados do backup..."

    # Restaurar arquivo de configuração
    if [ -f "$BACKUP_DIR/zabbix_proxy.conf.backup" ]; then
        # Fazer backup da configuração nova (caso precise reverter)
        cp "$ZABBIX_CONF_PATH" "$BACKUP_DIR/zabbix_proxy.conf.new"

        # Restaurar configuração antiga
        cp "$BACKUP_DIR/zabbix_proxy.conf.backup" "$ZABBIX_CONF_PATH"

        # Ajustar permissões
        chown root:$ZABBIX_USER "$ZABBIX_CONF_PATH"
        chmod 640 "$ZABBIX_CONF_PATH"

        log_success "Configuração restaurada do backup"
    else
        log_warning "Arquivo de backup da configuração não encontrado. Usando configuração padrão."
    fi

    # Restaurar banco de dados SQLite3
    restore_sqlite_database
}

# Função específica para restaurar banco SQLite3
restore_sqlite_database() {
    log "🗄️ Restaurando banco de dados SQLite3..."

    # Verificar se existe backup do banco
    if [ -f "$BACKUP_DIR/zabbix_proxy.db.backup" ]; then
        # Criar diretório do banco se não existir
        mkdir -p "$ZABBIX_DB_DIR"

        # Fazer backup do banco novo (se existir)
        if [ -f "$ZABBIX_DB_PATH" ]; then
            mv "$ZABBIX_DB_PATH" "$BACKUP_DIR/zabbix_proxy.db.new"
            log "Banco novo salvo como backup"
        fi

        # Restaurar banco antigo
        cp "$BACKUP_DIR/zabbix_proxy.db.backup" "$ZABBIX_DB_PATH"

        # Ajustar permissões
        chown $ZABBIX_USER:$ZABBIX_USER "$ZABBIX_DB_PATH"
        chmod 660 "$ZABBIX_DB_PATH"
        chown $ZABBIX_USER:$ZABBIX_USER "$ZABBIX_DB_DIR"
        chmod 755 "$ZABBIX_DB_DIR"

        # Verificar integridade do banco restaurado
        if sqlite3 "$ZABBIX_DB_PATH" "PRAGMA integrity_check;" | grep -q "ok"; then
            log_success "Banco de dados SQLite3 restaurado e verificado"
        else
            log_error "Erro na integridade do banco restaurado"

            # Tentar restaurar do dump SQL
            if [ -f "$BACKUP_DIR/zabbix_proxy_dump.sql" ]; then
                log "Tentando restaurar do dump SQL..."
                rm -f "$ZABBIX_DB_PATH"
                sqlite3 "$ZABBIX_DB_PATH" < "$BACKUP_DIR/zabbix_proxy_dump.sql"

                # Ajustar permissões novamente
                chown $ZABBIX_USER:$ZABBIX_USER "$ZABBIX_DB_PATH"
                chmod 660 "$ZABBIX_DB_PATH"

                if sqlite3 "$ZABBIX_DB_PATH" "PRAGMA integrity_check;" | grep -q "ok"; then
                    log_success "Banco restaurado do dump SQL com sucesso"
                else
                    log_error "Falha ao restaurar banco do dump SQL"
                    exit 1
                fi
            else
                log_error "Dump SQL não encontrado. Não é possível restaurar o banco"
                exit 1
            fi
        fi
    else
        log_warning "Backup do banco SQLite3 não encontrado"
        log "Criando novo banco de dados..."

        # Criar novo banco se não existir backup
        create_new_sqlite_database
    fi
}

# Função para criar novo banco SQLite3
create_new_sqlite_database() {
    log "🆕 Criando novo banco de dados SQLite3..."

    # Criar diretório se não existir
    mkdir -p "$ZABBIX_DB_DIR"

    # Criar banco vazio
    touch "$ZABBIX_DB_PATH"

    # Ajustar permissões
    chown $ZABBIX_USER:$ZABBIX_USER "$ZABBIX_DB_PATH"
    chmod 660 "$ZABBIX_DB_PATH"
    chown $ZABBIX_USER:$ZABBIX_USER "$ZABBIX_DB_DIR"
    chmod 755 "$ZABBIX_DB_DIR"

    log_success "Novo banco de dados SQLite3 criado"
}

# Função para verificar e ajustar configuração
check_configuration() {
    log "🔍 Verificando configuração e banco de dados..."

    # Verificar se o arquivo de configuração existe
    if [ ! -f "$ZABBIX_CONF_PATH" ]; then
        log_error "Arquivo de configuração não encontrado: $ZABBIX_CONF_PATH"
        exit 1
    fi

    # Verificar se o banco SQLite3 existe
    if [ ! -f "$ZABBIX_DB_PATH" ]; then
        log_error "Banco de dados SQLite3 não encontrado: $ZABBIX_DB_PATH"
        exit 1
    fi

    # Verificar integridade do banco SQLite3
    if sqlite3 "$ZABBIX_DB_PATH" "PRAGMA integrity_check;" | grep -q "ok"; then
        log_success "Integridade do banco SQLite3 verificada"
    else
        log_error "Erro na integridade do banco SQLite3: $ZABBIX_DB_PATH"
        exit 1
    fi

    # Verificar permissões do banco
    DB_OWNER=$(stat -c '%U:%G' "$ZABBIX_DB_PATH")
    if [ "$DB_OWNER" = "$ZABBIX_USER:$ZABBIX_USER" ]; then
        log_success "Permissões do banco SQLite3 corretas"
    else
        log_warning "Ajustando permissões do banco SQLite3..."
        chown $ZABBIX_USER:$ZABBIX_USER "$ZABBIX_DB_PATH"
        chmod 660 "$ZABBIX_DB_PATH"
    fi

    # Verificar sintaxe da configuração
    if zabbix_proxy -c "$ZABBIX_CONF_PATH" -t; then
        log_success "Configuração válida"
    else
        log_error "Erro na configuração. Verifique o arquivo: $ZABBIX_CONF_PATH"
        log "Backup da configuração disponível em: $BACKUP_DIR"
        exit 1
    fi
}

# Função para iniciar serviços
start_services() {
    log "🚀 Iniciando serviços do Zabbix..."

    # Habilitar serviço para iniciar automaticamente
    systemctl enable $ZABBIX_SERVICE

    # Iniciar serviço
    systemctl start $ZABBIX_SERVICE

    # Verificar status
    sleep 5
    if systemctl is-active --quiet $ZABBIX_SERVICE; then
        log_success "Serviço $ZABBIX_SERVICE iniciado com sucesso"
    else
        log_error "Falha ao iniciar o serviço $ZABBIX_SERVICE"
        log "Verificando logs..."
        journalctl -u $ZABBIX_SERVICE --no-pager -n 20
        exit 1
    fi
}

# Função para verificar conectividade
test_connectivity() {
    log "🔗 Testando conectividade..."

    # Aguardar um pouco para o serviço estabilizar
    sleep 10

    # Verificar se o processo está rodando
    if pgrep -x "zabbix_proxy" > /dev/null; then
        log_success "Processo zabbix_proxy está rodando"
    else
        log_warning "Processo zabbix_proxy não encontrado"
    fi

    # Verificar logs para erros
    if [ -f "/var/log/zabbix/zabbix_proxy.log" ]; then
        ERROR_COUNT=$(tail -n 50 /var/log/zabbix/zabbix_proxy.log | grep -i "error\|fail" | wc -l)
        if [ $ERROR_COUNT -eq 0 ]; then
            log_success "Nenhum erro encontrado nos logs recentes"
        else
            log_warning "Encontrados $ERROR_COUNT erros nos logs recentes"
            log "Últimos erros:"
            tail -n 50 /var/log/zabbix/zabbix_proxy.log | grep -i "error\|fail" | tail -n 5
        fi
    fi
}

# Função para mostrar informações finais
show_final_info() {
    log "📋 Informações da atualização:"

    # Versão instalada
    NEW_VERSION=$(zabbix_proxy --version | head -n1 | awk '{print $3}')
    log "Versão anterior: $CURRENT_VERSION"
    log "Versão atual: $NEW_VERSION"

    # Status do serviço
    SERVICE_STATUS=$(systemctl is-active $ZABBIX_SERVICE)
    log "Status do serviço: $SERVICE_STATUS"

    # Informações do banco SQLite3
    if [ -f "$ZABBIX_DB_PATH" ]; then
        DB_SIZE=$(du -h "$ZABBIX_DB_PATH" | cut -f1)
        log "Tamanho do banco SQLite3: $DB_SIZE"

        # Verificar se há dados no banco
        TABLE_COUNT=$(sqlite3 "$ZABBIX_DB_PATH" "SELECT COUNT(*) FROM sqlite_master WHERE type='table';" 2>/dev/null || echo "0")
        log "Tabelas no banco: $TABLE_COUNT"
    fi

    # Localização do backup
    log "Backup salvo em: $BACKUP_DIR"

    # Arquivos importantes
    log ""
    log "📁 Arquivos importantes:"
    log "   Configuração: $ZABBIX_CONF_PATH"
    log "   Banco SQLite3: $ZABBIX_DB_PATH"
    log "   Logs: /var/log/zabbix/zabbix_proxy.log"
    log "   Backup: $BACKUP_DIR"

    log ""
    log_success "🎉 Atualização do Zabbix Proxy concluída com sucesso!"
    log ""
    log "📝 Próximos passos recomendados:"
    log "   1. Verificar logs: tail -f /var/log/zabbix/zabbix_proxy.log"
    log "   2. Testar conectividade com o Zabbix Server"
    log "   3. Verificar se os dados estão sendo coletados"
    log "   4. Verificar integridade do banco: sqlite3 $ZABBIX_DB_PATH 'PRAGMA integrity_check;'"
    log "   5. Remover backup após confirmação: rm -rf $BACKUP_DIR"

    log ""
    log "🔧 Comandos úteis para SQLite3:"
    log "   Verificar tabelas: sqlite3 $ZABBIX_DB_PATH '.tables'"
    log "   Verificar tamanho: sqlite3 $ZABBIX_DB_PATH 'PRAGMA page_count; PRAGMA page_size;'"
    log "   Otimizar banco: sqlite3 $ZABBIX_DB_PATH 'VACUUM;'"
}

# Função principal
main() {
    log "🔧 Iniciando processo de atualização..."

    # Detectar distribuição
    detect_distro

    # Verificar versão atual
    check_current_version

    # Criar backup
    create_backup

    # Parar serviços
    stop_services

    # Instalar Zabbix 7.0 LTS
    install_zabbix_7

    # Restaurar configuração
    restore_configuration

    # Verificar configuração
    check_configuration

    # Iniciar serviços
    start_services

    # Testar conectividade
    test_connectivity

    # Mostrar informações finais
    show_final_info
}

# Função para limpeza em caso de erro
cleanup() {
    log_error "Script interrompido. Executando limpeza..."

    if [ -n "$BACKUP_DIR" ] && [ -d "$BACKUP_DIR" ]; then
        log "Backup disponível em: $BACKUP_DIR"
        log "Para restaurar manualmente:"
        log "   sudo cp $BACKUP_DIR/zabbix_proxy.conf.backup $ZABBIX_CONF_PATH"
        log "   sudo systemctl restart $ZABBIX_SERVICE"
    fi
}

# Configurar trap para limpeza
trap cleanup EXIT

# Executar função principal
main

# Remover trap de limpeza se chegou até aqui
trap - EXIT