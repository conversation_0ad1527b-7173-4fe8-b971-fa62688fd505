![enter image description here](https://i.imgur.com/vv80AvH.png)

![MarcaPonto](https://i.imgur.com/24xg3xD.jpg)

# Controle de ponto online

MarcaPonto é um sistema de gerenciamento de jornada inteligente, totalmente online e de fácil acesso.

## Justificativa

O custo de regularizar sistemas de ponto pode se tornar complexo ou até mesmo inviável, pois além da empresa ter que adquirir equipamentos específicos, também precisa disponibilizar alguém para gerir e realizar manutenções periódicas. Em muitos casos o “não fazer” também pode acarretar grandes problemas, já que questões como segurança perdem o foco sem um serviço específico. Nosso sistema será de baixo custo, acessível e seguro, dentro de todas as leis cabíveis e prezando pela entrega de qualidade.

## Tecnologias

-   React
-   SASS
-   Typescript
-   React Leaflet
-   Lottie
-   Rechart

# Funcionalidades

![Dashboard](https://i.imgur.com/MhxrgUj.png)

Os usuários possuem 2 níveis, **Colaboradores** e **Gestores**. Os colaboradores podem bater ponto e consultar seu espelho de ponto, o gestor pode fazer o cadastro de usuários, expedientes, funções, setores, e horários, além de poder aprovar pontos e fazer delimitações de onde o usuário pode bater o ponto.

## Usuários

Os usuários cadastrados receberão uma senha em seu e-mail para fazer o acesso no sistema, Após fazer o acesso, o mesmo poderá atualizar essa senha na página de configurações.

![Usuários Cadastrados](https://i.imgur.com/pqU7NFE.png)

![Atualiza usuários](https://i.imgur.com/0v6iI4H.png)

![Delimitar pontos](https://i.imgur.com/O2xnwgV.png)

Nesse exemplo, o usuário Heitor Augusto só poderá bater o ponto dentro da área delimitada em azul. O gestor tem a habilidade de adicionar várias áreas para um mesmo usuário, além de aumentar o raio dessa área, trocar essa área de lugar e deletar a mesma.

## Pontos

O gestor pode realizar a aprovação e a reprovação de pontos.

![Aprovar pontos](https://i.imgur.com/Mi2Ztbj.png)

## Espelho do ponto

![Espelho do ponto](https://i.imgur.com/nbijNIF.png)

## Relatórios

O gestor pode gerar relatórios de todos os itens cadastrados juntamente com os logs de atividades.

![Relatórios](https://i.imgur.com/b3QnVPp.png)

## Logs

Todas as ações feitas pelos usuários são salvas na sessão de logs, onde a mesma diz o que foi feito e em qual horário.

![Logs](https://i.imgur.com/42nIPQj.png)

Juntamente com os logs, o usuário tem a opção de ver essas mudanças em um sistema de notificações.

## Configurações

No menu de configurações o usuário pode atualizar sua senha e trocar a linguagem do sistema. Os idiomas disponíveis são **Português**, **Inglês**, **Espanhol** e **Italiano**.

![Configurações](https://i.imgur.com/Iqd6xnt.png)

