import requests
import json

API_URL = "https://api.centralmesh.nvirtual.com.br"
API_TOKEN = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"

headers = {"X-API-KEY": API_TOKEN}

print("=== INVESTIGANDO VALORES DE DISCO ===")

# Buscar agente específico que tem dados
agent_id = "iKKWJPQHLrzSGsSjKJEAGiQcJHnKmvpFffvskVuY"  # SIMONE-PC
r = requests.get(f"{API_URL}/agents/{agent_id}/", headers=headers)
agent = r.json()

print(f"Agente: {agent.get('hostname')}")

# Verificar dados básicos do agente
print(f"\nDados básicos do agente:")
print(f"  disks: {agent.get('disks')}")

# Verificar WMI detail
wmi = agent.get('wmi_detail', {})
if isinstance(wmi, dict):
    disk_data = wmi.get('disk', [])
    print(f"\nWMI Disk data: {len(disk_data)} discos")
    
    for i, disk_list in enumerate(disk_data):
        if isinstance(disk_list, list) and disk_list:
            disk = disk_list[0]
            print(f"\nDisco {i+1}:")
            print(f"  Model: {disk.get('Model')}")
            print(f"  Size (raw): {disk.get('Size')}")
            print(f"  InterfaceType: {disk.get('InterfaceType')}")
            print(f"  MediaType: {disk.get('MediaType')}")
            
            # Converter para GB
            size_bytes = int(disk.get('Size', 0)) if disk.get('Size') else 0
            size_gb = round(size_bytes / (1024**3), 2) if size_bytes > 0 else 0
            print(f"  Size em GB: {size_gb}")
            
            # Verificar se é USB
            interface_type = disk.get('InterfaceType', '').upper()
            media_type = disk.get('MediaType', '').upper()
            model = disk.get('Model', '').upper()
            
            is_usb = ('USB' in interface_type or 
                     'USB' in media_type or 
                     'USB' in model or
                     'REMOVABLE' in media_type)
            print(f"  É USB/Removível: {is_usb}")

# Verificar se há dados de uso em outros campos
print(f"\nOutros campos do agente:")
for key, value in agent.items():
    if 'disk' in key.lower() or 'space' in key.lower() or 'storage' in key.lower():
        print(f"  {key}: {value}")
