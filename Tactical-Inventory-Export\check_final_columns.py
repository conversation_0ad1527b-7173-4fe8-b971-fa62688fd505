import csv

filename = 'FamaSegurosMeuRelatorio_COLUNAS_ESPECIFICAS_FINAL_Cliente33_CONSOLIDADO.csv'

with open(filename, 'r', encoding='utf-8') as f:
    reader = csv.DictReader(f)
    rows = list(reader)

print(f'Total de agentes: {len(rows)}')
print(f'Total de colunas: {len(rows[0].keys())}')

print('\nPrimeiros 3 agentes:')
for i, row in enumerate(rows[:3]):
    print(f'\n{i+1}. {row["Hostname"]} - {row["Usuario_Logado"]}')
    print(f'   Cliente: {row["Cliente"]} - Site: {row["Site"]}')
    print(f'   Discos: {row["Quantidade_Discos"]} | Total: {row["Espaco_Total_GB"]} GB | Usado: {row["Espaco_Usado_GB"]} GB ({row["Percentual_Uso"]}%)')
    print(f'   RAM: {row["RAM_Total_GB"]} GB ({row["RAM_Slots_Usados"]} slots) | Tipo: {row["RAM_Tipo"]} | Velocidade: {row["RAM_Velocidade"]}')
    print(f'   CPU: {row["CPU_Nome"]} | Threads: {row["CPU_Threads"]} | Arquitetura: {row["Arquitetura"]}')
    print(f'   Sistema: {row["Sistema_Fabricante"]} {row["Sistema_Modelo"]} | SO: {row["Sistema_Operacional"]}')

print('\nVerificação das 32 colunas solicitadas:')
required_columns = [
    'Arquitetura', 'CPU_Nome', 'CPU_Threads', 'Cliente', 'Espaco_Livre_GB', 
    'Espaco_Total_GB', 'Espaco_Usado_GB', 'Hostname', 'ID_Agente', 'IP_Publico', 
    'IPs_Locais', 'Marca_Modelo', 'Percentual_Uso', 'Placa_Mae', 'Placa_Mae_Fabricante', 
    'Placa_Mae_Modelo', 'Placa_Video', 'Quantidade_Discos', 'RAM_Slots_Usados', 
    'RAM_Tipo', 'RAM_Total_GB', 'RAM_Velocidade', 'Sistema_Fabricante', 'Sistema_Modelo', 
    'Sistema_Operacional', 'Sistema_Tipo', 'Site', 'Status', 'Tipo_Monitoramento', 
    'Ultimo_Contato', 'Usuario_Logado', 'Versao_Agente'
]

actual_columns = list(rows[0].keys())
missing = [col for col in required_columns if col not in actual_columns]
extra = [col for col in actual_columns if col not in required_columns]

print(f'Colunas solicitadas: {len(required_columns)}')
print(f'Colunas presentes: {len(actual_columns)}')
print(f'Colunas faltando: {len(missing)} - {missing}')
print(f'Colunas extras: {len(extra)} - {extra}')

if len(missing) == 0 and len(extra) == 0:
    print('✅ PERFEITO! Todas as colunas solicitadas estão presentes e não há colunas extras.')
