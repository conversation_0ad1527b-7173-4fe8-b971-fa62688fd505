{"name": "hr_system", "version": "0.0.1", "private": true, "scripts": {"start": "react-native start", "test": "jest", "lint": "eslint .", "postinstall": "jetify"}, "dependencies": {"@react-native-community/async-storage": "^1.6.1", "@react-native-community/datetimepicker": "^3.0.2", "axios": "^0.19.0", "fbjs": "^3.0.0", "firebase": "^7.1.0", "moment": "^2.24.0", "native-base": "^2.13.14", "react": "16.8.6", "react-moment": "^0.9.3", "react-native": "^0.63.2", "react-native-calendars": "^1.824.0", "react-native-elements": "^1.1.0", "react-native-firebase": "^5.5.6", "react-native-geolocation-service": "^3.1.0", "react-native-gesture-handler": "^1.3.0", "react-native-gifted-chat": "^0.9.11", "react-native-image-picker": "^1.0.1", "react-native-loading-spinner-overlay": "^1.0.1", "react-native-swipe-list-view": "^3.2.5", "react-native-vector-icons": "^6.6.0", "react-navigation": "^3.11.1"}, "devDependencies": {"@babel/core": "^7.5.5", "@babel/runtime": "^7.5.5", "@react-native-community/eslint-config": "^0.0.5", "babel-jest": "^24.8.0", "eslint": "^6.0.1", "jest": "^24.8.0", "metro-react-native-babel-preset": "^0.55.0", "react-test-renderer": "16.8.6"}, "jest": {"preset": "react-native"}}