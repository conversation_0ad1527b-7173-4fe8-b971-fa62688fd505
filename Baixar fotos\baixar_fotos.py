import pandas as pd
import os
import re
from icrawler.builtin import GoogleImageCrawler
from PIL import Image
from PIL import ImageOps

# Caminho do seu arquivo Excel
CAMINHO_PLANILHA = "produtos.xlsx"  # Exemplo
COLUNA_PRODUTOS = "Nome"  # Nome da coluna com os produtos
PASTA_SAIDA = "imagens_baixadas"

# Cria pasta de saída se não existir
os.makedirs(PASTA_SAIDA, exist_ok=True)

# Lê a planilha
df = pd.read_excel(CAMINHO_PLANILHA)

# Função para deixar o nome de arquivo seguro
def limpar_nome(nome):
    return re.sub(r'[^a-zA-Z0-9_-]', '_', nome.strip())[:100]  # Limita tamanho do nome

# Percorre a lista de produtos
for produto in df[COLUNA_PRODUTOS].dropna():
    nome_limpo = limpar_nome(str(produto))
    pasta_temp = os.path.join(PASTA_SAIDA, "temp")
    os.makedirs(pasta_temp, exist_ok=True)

    # Baixa uma imagem do Google Imagens
    crawler = GoogleImageCrawler(storage={"root_dir": pasta_temp})
    crawler.crawl(keyword=produto, max_num=1)

    arquivos = os.listdir(pasta_temp)
    if arquivos:
        caminho_original = os.path.join(pasta_temp, arquivos[0])
        img = Image.open(caminho_original)

        # Ajusta a imagem para formato quadrado sem cortar (preenchendo com branco)
        largura, altura = img.size
        dim_max = max(largura, altura)

        # Cria fundo branco quadrado
        fundo = Image.new("RGB", (dim_max, dim_max), (255, 255, 255))

        # Se imagem tiver transparência ou for P, converte antes
        if img.mode in ("RGBA", "P"):
            img = img.convert("RGB")

        # Centraliza a imagem no fundo
        offset = ((dim_max - largura) // 2, (dim_max - altura) // 2)
        fundo.paste(img, offset)

        # Salva como JPEG com nome do produto
        caminho_final = os.path.join(PASTA_SAIDA, f"{nome_limpo}.jpg")
        fundo.save(caminho_final, format="JPEG")


        print(f"✅ Imagem salva: {caminho_final}")
    else:
        print(f"⚠️ Nenhuma imagem encontrada para: {produto}")

    # Limpa a pasta temporária
    for f in os.listdir(pasta_temp):
        os.remove(os.path.join(pasta_temp, f))
    os.rmdir(pasta_temp)
