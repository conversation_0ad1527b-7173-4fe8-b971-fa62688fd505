# Arquivo de configuração para instalação automatizada do Tactical RMM
# Copie este arquivo para 'config' e edite os valores conforme necessário
# 
# Uso: ./install-tactical-only.sh --config config
#
# Autor: <PERSON> Matheus - NVirtual
# Data: 2025-01-21

# ============================================================================
# CONFIGURAÇÕES OBRIGATÓRIAS
# ============================================================================

# ID do Cliente no Tactical RMM (número)
TACTICAL_CLIENT_ID=""

# Nome da Filial/Site do Cliente
TACTICAL_CLIENT_FILIAL=""

# ============================================================================
# CONFIGURAÇÕES AVANÇADAS (OPCIONAL)
# ============================================================================

# URLs dos servidores (normalmente não precisam ser alteradas)
TACTICAL_MESH_URL_X86="https://mesh.centralmesh.nvirtual.com.br/meshagents?id=7Nss2LHe67mTwByGHQ3H3lOI4x8Awfk6kwbQgxSMMq%40qIJKjK6OOSBMWfXBYgPlb&installflags=2&meshinstall=6"
TACTICAL_MESH_URL_ARM="https://mesh.centralmesh.nvirtual.com.br/meshagents?id=7Nss2LHe67mTwByGHQ3H3lOI4x8Awfk6kwbQgxSMMq%40qIJKjK6OOSBMWfXBYgPlb&installflags=0&meshinstall=26"
TACTICAL_API_URL="https://api.centralmesh.nvirtual.com.br"
TACTICAL_AUTH_KEY="ecd275ac5baa7e615674a38f2de333f00dd2635e179f9a08e4026db2e5856ae3"

# Tipo de agente (server ou workstation)
TACTICAL_AGENT_TYPE="server"

# ============================================================================
# CONFIGURAÇÕES DE INSTALAÇÃO
# ============================================================================

# Modo silencioso (true/false) - não solicita confirmações
SILENT_MODE="false"

# Pular atualização do sistema (true/false)
SKIP_SYSTEM_UPDATE="false"

# Timeout para downloads (segundos)
DOWNLOAD_TIMEOUT="30"

# Número máximo de tentativas de download
MAX_DOWNLOAD_ATTEMPTS="3"

# ============================================================================
# EXEMPLOS DE CONFIGURAÇÃO
# ============================================================================

# Exemplo para Cliente "Empresa ABC", Filial "Matriz":
# TACTICAL_CLIENT_ID="123"
# TACTICAL_CLIENT_FILIAL="Matriz"

# Exemplo para Cliente "Loja XYZ", Filial "Filial 01":
# TACTICAL_CLIENT_ID="456"
# TACTICAL_CLIENT_FILIAL="Filial 01"

# ============================================================================
# NOTAS IMPORTANTES
# ============================================================================

# 1. O TACTICAL_CLIENT_ID deve ser um número válido no sistema Tactical RMM
# 2. O TACTICAL_CLIENT_FILIAL pode conter espaços e caracteres especiais
# 3. Mantenha este arquivo seguro, pois contém informações de configuração
# 4. Para instalação silenciosa, defina SILENT_MODE="true"
# 5. As URLs dos servidores normalmente não precisam ser alteradas

# ============================================================================
# VALIDAÇÃO DE CONFIGURAÇÃO
# ============================================================================

# Descomente as linhas abaixo para validar a configuração antes da instalação
# VALIDATE_CONFIG="true"
# SHOW_CONFIG_SUMMARY="true"
