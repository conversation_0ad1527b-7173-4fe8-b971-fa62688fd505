# 📊 Tactical Inventory Export

Sistema completo para exportação de inventário de estações do Tactical RMM. Inclui scripts Python (recomendados) e PowerShell para diferentes necessidades.

## 🚀 <PERSON><PERSON><PERSON> (Python - Recomendado)

### 1. Executar via Tactical RMM
1. Acesse **Settings → Script Manager → Add Script**
2. Configure:
   - **Name:** `Inventário Python`
   - **Script Type:** `Python`
   - **Timeout:** `300` segundos
3. Cole o conteúdo do arquivo `tactical_rmm_inventory.py`
4. Execute em agentes ou grupos

### 2. Executar Localmente
```bash
# Instalar dependência
pip install requests

# Executar script básico
python tactical_rmm_inventory.py

# Script com argumentos
python tactical_inventory_export.py --format json

# Script avançado com hardware
python tactical_inventory_advanced.py --include-hardware
```

### 3. Arquivos Gerados
- **CSV:** Dados tabulares para análise
- **JSON:** Dados estruturados para integração
- **Resumo:** Estatísticas consolidadas

## 📁 Estrutura do Projeto

```
Tactical-Inventory-Export/
├── 📄 README.md                           # Este arquivo (visão geral)
├── 📄 README-Python.md                    # Documentação detalhada Python
├── 📄 README-Export-Inventory.md          # Documentação PowerShell
│
├── 🐍 Scripts Python (RECOMENDADOS)
│   ├── tactical_rmm_inventory.py          # Script simplificado para Tactical RMM
│   ├── tactical_inventory_export.py       # Script básico com argumentos
│   ├── tactical_inventory_advanced.py     # Script avançado com hardware
│   └── list_clients.py                    # Utilitário para listar clientes
│
├── 💻 Scripts PowerShell
│   ├── Export-TacticalInventory.ps1       # Exportação básica
│   ├── Export-TacticalInventory-Advanced.ps1 # Exportação avançada
│   ├── Test-TacticalInventory.ps1         # Testes
│   ├── Setup-TacticalInventory.ps1        # Configuração
│   └── config.example.ps1                 # Exemplo de configuração
```

## 🐍 Scripts Python (Recomendados)

### ✨ **Vantagens**
- ✅ **Multiplataforma** - Windows, Linux, macOS
- ✅ **Execução nativa** no Tactical RMM
- ✅ **Sem dependências** complexas (apenas requests)
- ✅ **Parsing JSON** nativo para API
- ✅ **Logs centralizados** na interface Tactical RMM

### 📊 **tactical_rmm_inventory.py** - Principal
- ✅ Execução direta via Tactical RMM
- ✅ Configuração fixa no código
- ✅ Exporta CSV, JSON e resumo automaticamente
- ✅ Estatísticas por cliente em tempo real

### 📊 **tactical_inventory_export.py** - Básico
- ✅ Argumentos de linha de comando
- ✅ Filtros por cliente e site
- ✅ Múltiplos formatos de saída

### 📊 **tactical_inventory_advanced.py** - Avançado
- ✅ Informações detalhadas de hardware via WMI
- ✅ Lista de software instalado
- ✅ Custom fields configurados

## 💻 Scripts PowerShell

### ✨ **Características**
- ✅ **Excel nativo** com múltiplas abas
- ✅ **Módulo ImportExcel** para formatação avançada
- ✅ **Filtros avançados** por cliente e site
- ✅ **Sistema de configuração** flexível

### 📊 **Export-TacticalInventory.ps1** - Básico
- Exportação essencial em Excel/CSV
- Filtros por cliente e site
- Estatísticas resumidas

### 📊 **Export-TacticalInventory-Advanced.ps1** - Avançado
- Hardware detalhado via WMI
- Software instalado
- Múltiplas abas no Excel

## 🎯 Casos de Uso

### 📋 **Relatório Diário Automático**
```python
# Via Tactical RMM - Script Python
# Agendar execução diária em todos os agentes
# Arquivos salvos em C:\TacticalInventory\
```

### 🏢 **Auditoria por Cliente**
```bash
# Local - Script Python
python tactical_inventory_export.py --client-id "Sumire" --format json

# PowerShell
.\Export-TacticalInventory.ps1 -ClientId "Sumire" -OutputFormat Excel
```

### 💻 **Inventário de Hardware**
```bash
# Python avançado
python tactical_inventory_advanced.py --include-hardware --include-software

# PowerShell avançado
.\Export-TacticalInventory-Advanced.ps1 -IncludeHardware -IncludeSoftware
```

## 📊 Dados Exportados

### **Inventário Básico:**
- **Identificação:** ID, Hostname, Cliente, Site
- **Status:** Online/Offline/Recente, Último contato
- **Sistema:** SO, Versão, Arquitetura, IP
- **Hardware:** CPU, RAM, Antivírus
- **Monitoramento:** Falhas, Manutenção

### **Inventário Avançado:**
- **Hardware Detalhado:** CPU (cores, velocidade), RAM (slots), Discos, BIOS
- **Software:** Lista completa com versões
- **Custom Fields:** Campos personalizados

## 📈 Estatísticas Geradas

### **Resumo Executivo:**
- Total de agentes por status
- Distribuição por cliente
- Agentes online vs offline
- Timestamp da coleta

### **Exemplo de Saída:**
```json
{
  "total_agentes": 452,
  "status": {
    "online": 365,
    "offline": 51,
    "recente": 36
  },
  "por_cliente": {
    "Sumire": 195,
    "Kimetais": 41,
    "Carlos Bakery": 21
  }
}
```

## 🔧 Configuração

### **Token de API:**
1. Tactical RMM → Settings → Global Settings → API Keys
2. Criar nova API Key com permissões de leitura
3. Configurar nos scripts:
```python
API_TOKEN = "SEU_TOKEN_AQUI"
```

### **Personalização:**
- **Python:** Editar constantes no início dos scripts
- **PowerShell:** Usar arquivo config.ps1

## 📞 Suporte

### **Documentação Detalhada:**
- **README-Python.md** - Guia completo Python
- **README-Export-Inventory.md** - Guia completo PowerShell

### **Scripts de Teste:**
- **list_clients.py** - Listar clientes disponíveis
- **Test-TacticalInventory.ps1** - Testar conectividade PowerShell

---

**Desenvolvido por NVirtual** 🚀

### 📋 **Parâmetros PowerShell**

#### **Obrigatórios:**
- `ClientId` - ID do cliente no Tactical RMM
- `EmailTo` - Email(s) de destino (separados por vírgula)
- `SMTPUser` - Usuário SMTP
- `SMTPPassword` - Senha SMTP

#### **Opcionais:**
- `EmailSubject` - Assunto personalizado
- `SMTPServer` - Servidor SMTP (padrão: smtp.gmail.com)
- `SMTPPort` - Porta SMTP (padrão: 587)
- `IncludeOffline` - Incluir offline (padrão: true)

### 💡 **Exemplos de Uso**

#### **Básico:**
```bash
-ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "senha123"
```

#### **Avançado:**
```bash
-ClientId 8 -EmailTo "<EMAIL>,<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "senha123" -EmailSubject "Relatório Semanal" -IncludeOffline false
```

## 📊 **O que o Script Gera**

### 📧 **Email HTML Profissional**
- Design moderno com gradientes e cores
- Cards estatísticos visuais (Total, Online, Offline, Sites)
- Tabelas organizadas por site
- Informações do servidor Ubuntu
- Nota destacada sobre anexo Excel

### 📎 **Anexo Excel Automático**
- **Múltiplas abas** (uma para cada site)
- **Aba "Resumo_Geral"** com estatísticas consolidadas
- **Formatação automática** (AutoSize, AutoFilter, FreezeTopRow)
- **Dados completos** de cada estação
- **Nome com timestamp** para organização

### 📋 **Dados Incluídos**
- ID do Agente, Hostname, Cliente, Site
- Status de conectividade com timestamp
- Sistema operacional e arquitetura
- Informações de hardware (CPU, RAM)
- IP público e versão do agente
- Usuário logado e domínio
- Falhas de serviços e checks
- Modo de manutenção e observações

## 🔧 **Configurações SMTP**

### **Gmail/Google Workspace:**
```bash
-SMTPServer "smtp.gmail.com" -SMTPPort 587 -SMTPUser "<EMAIL>" -SMTPPassword "senha_de_aplicativo"
```

### **Office 365/Outlook:**
```bash
-SMTPServer "smtp.office365.com" -SMTPPort 587 -SMTPUser "<EMAIL>" -SMTPPassword "sua_senha"
```

## 📖 **Documentação Adicional**

- **README-Complete-Script.md** - Documentação detalhada do script
- **TACTICAL-SETUP-EXAMPLES.md** - Exemplos práticos de configuração

---

**Desenvolvido por NVirtual** 🚀
