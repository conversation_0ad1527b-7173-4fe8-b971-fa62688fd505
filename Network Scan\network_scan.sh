#!/bin/bash

# Network Scanner em Bash
# Scan rápido de rede com identificação básica de dispositivos
# Autor: <PERSON> Matheus

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configurações padrão
NETWORK="***********/24"
THREADS=20
TIMEOUT=1
OUTPUT_FILE=""

# Função de ajuda
show_help() {
    echo "🌐 Network Scanner - Bash Version"
    echo "=================================="
    echo "Uso: $0 [opções] [rede]"
    echo ""
    echo "Opções:"
    echo "  -n, --network REDE    Rede para escanear (padrão: ***********/24)"
    echo "  -t, --threads NUM     Número de processos paralelos (padrão: 20)"
    echo "  -w, --timeout SEC     Timeout em segundos (padrão: 1)"
    echo "  -o, --output FILE     Arquivo de saída"
    echo "  -h, --help           Mostra esta ajuda"
    echo ""
    echo "Exemplos:"
    echo "  $0 ***********/24"
    echo "  $0 -n ***********/24 -t 50 -o scan_results.txt"
    echo "  $0 --network 10.0.0.0/24 --threads 30"
}

# Função para log
log() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Função para ping
ping_host() {
    local ip=$1
    local timeout=$2
    
    if command -v ping >/dev/null 2>&1; then
        if [[ "$OSTYPE" == "darwin"* ]] || [[ "$OSTYPE" == "linux"* ]]; then
            ping -c 1 -W $timeout "$ip" >/dev/null 2>&1
        else
            # Windows
            ping -n 1 -w $((timeout * 1000)) "$ip" >/dev/null 2>&1
        fi
    else
        return 1
    fi
}

# Função para resolver hostname
get_hostname() {
    local ip=$1
    local hostname
    
    if command -v nslookup >/dev/null 2>&1; then
        hostname=$(nslookup "$ip" 2>/dev/null | grep "name =" | cut -d'=' -f2 | tr -d ' ' | sed 's/\.$//')
    elif command -v host >/dev/null 2>&1; then
        hostname=$(host "$ip" 2>/dev/null | grep "domain name pointer" | awk '{print $NF}' | sed 's/\.$//')
    fi
    
    if [[ -z "$hostname" ]]; then
        echo "N/A"
    else
        echo "$hostname"
    fi
}

# Função para scan de portas básico
scan_ports() {
    local ip=$1
    local ports=(22 23 25 53 80 110 135 139 143 443 445 993 995 3389 5900 8080)
    local open_ports=()
    
    for port in "${ports[@]}"; do
        if timeout 0.5 bash -c "echo >/dev/tcp/$ip/$port" 2>/dev/null; then
            open_ports+=($port)
        fi
    done
    
    echo "${open_ports[@]}"
}

# Função para identificar dispositivo
identify_device() {
    local ip=$1
    local hostname=$2
    local ports=($3)
    local device_type="Unknown"
    local os_type="Unknown"
    
    # Identificação baseada em portas
    if [[ " ${ports[@]} " =~ " 3389 " ]]; then
        device_type="Windows Server/Desktop"
        os_type="Windows"
    elif [[ " ${ports[@]} " =~ " 445 " ]] || [[ " ${ports[@]} " =~ " 135 " ]]; then
        device_type="Windows Machine"
        os_type="Windows"
    elif [[ " ${ports[@]} " =~ " 22 " ]]; then
        device_type="Linux/Unix Server"
        os_type="Linux/Unix"
    elif [[ " ${ports[@]} " =~ " 80 " ]] || [[ " ${ports[@]} " =~ " 443 " ]]; then
        device_type="Web Server"
        os_type="Linux/Windows"
    elif [[ " ${ports[@]} " =~ " 23 " ]]; then
        device_type="Network Device"
        os_type="Embedded"
    fi
    
    # Refinamento por hostname
    if [[ "$hostname" != "N/A" ]]; then
        hostname_lower=$(echo "$hostname" | tr '[:upper:]' '[:lower:]')
        
        if [[ "$hostname_lower" == *"router"* ]] || [[ "$hostname_lower" == *"switch"* ]] || [[ "$hostname_lower" == *"gateway"* ]]; then
            device_type="Network Device"
            os_type="Embedded/Custom"
        elif [[ "$hostname_lower" == *"printer"* ]] || [[ "$hostname_lower" == *"print"* ]]; then
            device_type="Printer"
            os_type="Embedded"
        elif [[ "$hostname_lower" == *"windows"* ]] || [[ "$hostname_lower" == *"win"* ]] || [[ "$hostname_lower" == *"pc"* ]]; then
            device_type="Windows Machine"
            os_type="Windows"
        elif [[ "$hostname_lower" == *"linux"* ]] || [[ "$hostname_lower" == *"ubuntu"* ]] || [[ "$hostname_lower" == *"server"* ]]; then
            device_type="Linux Server"
            os_type="Linux"
        fi
    fi
    
    echo "$device_type|$os_type"
}

# Função para escanear um host
scan_host() {
    local ip=$1
    local timeout=$2
    
    if ping_host "$ip" "$timeout"; then
        local hostname=$(get_hostname "$ip")
        local open_ports=$(scan_ports "$ip")
        local device_info=$(identify_device "$ip" "$hostname" "$open_ports")
        local device_type=$(echo "$device_info" | cut -d'|' -f1)
        local os_type=$(echo "$device_info" | cut -d'|' -f2)
        
        printf "%-15s | %-25s | %-20s | %-15s | %s\n" "$ip" "$hostname" "$device_type" "$os_type" "$open_ports"
        
        if [[ -n "$OUTPUT_FILE" ]]; then
            echo "$ip|$hostname|$device_type|$os_type|$open_ports" >> "$OUTPUT_FILE"
        fi
    fi
}

# Função para gerar IPs da rede
generate_ips() {
    local network=$1
    local base_ip=$(echo "$network" | cut -d'/' -f1)
    local cidr=$(echo "$network" | cut -d'/' -f2)
    
    # Calcular range baseado no CIDR
    case $cidr in
        24)
            local base=$(echo "$base_ip" | cut -d'.' -f1-3)
            for i in {1..254}; do
                echo "$base.$i"
            done
            ;;
        16)
            local base=$(echo "$base_ip" | cut -d'.' -f1-2)
            for j in {0..255}; do
                for i in {1..254}; do
                    echo "$base.$j.$i"
                done
            done
            ;;
        *)
            error "CIDR /$cidr não suportado. Use /24 ou /16"
            exit 1
            ;;
    esac
}

# Função principal de scan
scan_network() {
    local network=$1
    local threads=$2
    local timeout=$3
    
    log "Iniciando scan da rede: $network"
    info "Threads: $threads, Timeout: ${timeout}s"
    
    if [[ -n "$OUTPUT_FILE" ]]; then
        echo "# Network Scan Results - $(date)" > "$OUTPUT_FILE"
        echo "# IP|Hostname|Device Type|OS|Open Ports" >> "$OUTPUT_FILE"
    fi
    
    echo "================================================================================"
    printf "%-15s | %-25s | %-20s | %-15s | %s\n" "IP" "Hostname" "Device Type" "OS" "Open Ports"
    echo "================================================================================"
    
    local start_time=$(date +%s)
    local total_hosts=0
    local found_hosts=0
    
    # Gerar lista de IPs e processar em paralelo
    generate_ips "$network" | while read ip; do
        ((total_hosts++))
        
        # Controlar número de processos em background
        while [[ $(jobs -r | wc -l) -ge $threads ]]; do
            sleep 0.1
        done
        
        scan_host "$ip" "$timeout" &
    done
    
    # Aguardar todos os processos terminarem
    wait
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo "================================================================================"
    log "Scan concluído em ${duration} segundos"
    
    if [[ -n "$OUTPUT_FILE" ]]; then
        found_hosts=$(grep -v "^#" "$OUTPUT_FILE" | wc -l)
        info "Resultados salvos em: $OUTPUT_FILE"
        info "Dispositivos encontrados: $found_hosts"
    fi
}

# Parse de argumentos
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--network)
            NETWORK="$2"
            shift 2
            ;;
        -t|--threads)
            THREADS="$2"
            shift 2
            ;;
        -w|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            NETWORK="$1"
            shift
            ;;
    esac
done

# Validações
if ! command -v ping >/dev/null 2>&1; then
    error "Comando 'ping' não encontrado"
    exit 1
fi

# Verificar formato da rede
if [[ ! "$NETWORK" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+/[0-9]+$ ]]; then
    error "Formato de rede inválido. Use CIDR (ex: ***********/24)"
    exit 1
fi

# Banner
echo "🌐 NETWORK SCANNER - Bash Version"
echo "=================================="

# Executar scan
scan_network "$NETWORK" "$THREADS" "$TIMEOUT"
