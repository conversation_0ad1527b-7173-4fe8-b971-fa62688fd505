# 🔧 Atualização do Cabeçalho da API

## 📋 Alteração Realizada

O cabeçalho de autenticação da API foi atualizado conforme solicitado para usar `X-API-KEY` em vez de `Authorization`.

## 🔄 Mudanças Implementadas

### **Antes:**
```powershell
-Headers @{
    "Authorization" = "Token $ApiToken"
}
```

### **Depois:**
```powershell
-Headers @{
    "X-API-KEY" = $ApiToken
}
```

## 📍 **Locais Atualizados:**

### 1. **Função Test-TacticalAPI (linha 106)**
```powershell
$null = Invoke-RestMethod -Uri "$ApiUrl/api/v1/agents/" -Headers @{
    "X-API-KEY" = $Token
} -Method GET -TimeoutSec 10
```

### 2. **Busca de Agente (linha 137)**
```powershell
$response = Invoke-RestMethod -Uri "$apiUrl/api/v1/agents/?hostname=$hostname" -Headers @{
    "X-API-KEY" = $ApiToken
} -TimeoutSec 30
```

### 3. **Atualização de Site (linha 225)**
```powershell
$updateResult = Invoke-RestMethod -Method PATCH -Uri "$apiUrl/api/v1/agents/$agentId" -Headers @{
    "X-API-KEY" = $ApiToken
    "Content-Type" = "application/json"
} -Body $body -TimeoutSec 30
```

## ✅ **Validação**

- ✅ **Sintaxe PowerShell:** Mantida correta
- ✅ **Funcionalidade:** Preservada
- ✅ **Teste de Execução:** Passou com sucesso
- ✅ **Todas as chamadas de API:** Atualizadas

## 🚀 **Como Usar**

O script continua funcionando da mesma forma:

```powershell
# Execução normal
.\Set-tactical-cliente.ps1

# Modo simulação
.\Set-tactical-cliente.ps1 -WhatIf

# Com saída detalhada
.\Set-tactical-cliente.ps1 -VerboseOutput

# Com token específico
.\Set-tactical-cliente.ps1 -ApiToken "SEU_TOKEN"
```

## 📊 **Impacto**

- ✅ **Compatibilidade:** Mantida com Tactical RMM
- ✅ **Funcionalidade:** Inalterada
- ✅ **Performance:** Sem impacto
- ✅ **Segurança:** Melhorada (uso do cabeçalho correto)

## 🎯 **Resultado**

O script agora usa o cabeçalho `X-API-KEY` conforme especificado pela API do Tactical RMM, mantendo todas as funcionalidades existentes.

---

**Data da Atualização:** 2025-06-27  
**Status:** ✅ CONCLUÍDO  
**Versão:** 2.1 (Atualizada para X-API-KEY)
