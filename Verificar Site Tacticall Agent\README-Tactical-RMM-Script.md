# Script Tactical RMM - Atualização Automática de Sites

## 📋 Resumo das Correções

O script `Set-TacticalSite-Cliente copy.ps1` foi corrigido e adaptado para uso adequado com Tactical RMM. As principais correções incluem:

### 🔧 Problemas Corrigidos

1. **Operador de Comparação PowerShell**
   - ❌ Antes: `if ($agent.site != $siteId)`
   - ✅ Depois: `if ($agentSiteId -ne $siteId)`
   - PowerShell usa `-ne` para "not equal", não `!=`

2. **Tratamento de Erros da API**
   - ✅ Adicionado try-catch para chamadas da API
   - ✅ Mensagens de erro mais detalhadas
   - ✅ Verificação de status HTTP

3. **Detecção de IP Melhorada**
   - ✅ Função `Get-LocalIP` corrigida para focar em IPs 192.168.*
   - ✅ Filtros de IP mais precisos
   - ✅ Listagem de IPs disponíveis em caso de erro

4. **Segurança do Token**
   - ✅ Suporte a variável de ambiente `$env:TACTICAL_RMM_TOKEN`
   - ✅ Parâmetro opcional para passar token
   - ✅ Fallback para token hardcoded (desenvolvimento)

5. **Validações Aprimoradas**
   - ✅ Verificação de resposta da API antes de processar
   - ✅ Validação de múltiplos agentes com mesmo hostname
   - ✅ Verificação de cliente correto

6. **Informações de Debug**
   - ✅ Logs mais detalhados durante execução
   - ✅ Listagem de sites configurados em caso de erro
   - ✅ Resumo final da execução

## 🚀 Como Usar

### Método 1: Variável de Ambiente (Recomendado)
```powershell
# Definir token como variável de ambiente
$env:TACTICAL_RMM_TOKEN = "SEU_TOKEN_AQUI"

# Executar script
.\Set-TacticalSite-Cliente copy.ps1
```

### Método 2: Parâmetro
```powershell
.\Set-TacticalSite-Cliente copy.ps1 -ApiToken "SEU_TOKEN_AQUI"
```

### Método 3: Token Hardcoded
Edite a linha 27 do script e substitua o token padrão.

## ⚙️ Configuração

### Sites Configurados
```powershell
$sites = @{
    "192.168.0." = 1      # Bauru
    "192.168.250." = 4    # São Paulo  
    "192.168.103." = 16   # Loja 03
}
```

### Cliente ID
```powershell
$clientIdEsperado = 8  # ID do cliente no Tactical RMM
```

## 🔍 Fluxo de Execução

1. **Autenticação**: Verifica token de API
2. **Busca Agente**: Localiza agente pelo hostname
3. **Validação**: Confirma que pertence ao cliente correto
4. **Detecção IP**: Identifica IP local na faixa 192.168.*
5. **Mapeamento**: Determina site baseado no IP
6. **Atualização**: Atualiza site se necessário

## 📊 Saída do Script

```
🔍 Hostname local: DESKTOP-ABC123
✅ Agente encontrado. ID: 456 | Cliente ID: 8 | Site atual: São Paulo (ID: 4)
🔎 IP detectado: ***************
📍 Site correspondente detectado: ID 4
ℹ️ O site já está correto (ID: 4). Nenhuma alteração feita.

🎉 Script executado com sucesso!
📋 Resumo:
   - Hostname: DESKTOP-ABC123
   - IP Local: ***************
   - Site ID: 4
   - Agente ID: 456
```

## 🛠️ Requisitos

- PowerShell 5.1 ou superior
- Acesso à API do Tactical RMM
- Token de API com permissões de leitura/escrita de agentes
- Rede configurada com IPs na faixa 192.168.*

## 🔐 Segurança

- Use variáveis de ambiente para tokens em produção
- Mantenha tokens seguros e com escopo limitado
- Monitore logs de execução
- Teste em ambiente controlado antes da produção

## 📝 Logs de Erro Comuns

- **"Nenhum agente encontrado"**: Hostname não registrado no Tactical RMM
- **"Múltiplos agentes"**: Hostname duplicado (resolver manualmente)
- **"Cliente incorreto"**: Agente pertence a outro cliente
- **"IP não detectado"**: Máquina sem IP na faixa 192.168.*
- **"Site não configurado"**: IP não mapeado na configuração

## 🔄 Próximos Passos

1. Teste o script em ambiente de desenvolvimento
2. Configure variáveis de ambiente para tokens
3. Adicione novos sites conforme necessário
4. Monitore execução em produção
5. Configure execução automática via Tactical RMM se desejado
