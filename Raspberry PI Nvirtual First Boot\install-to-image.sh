#!/bin/bash

# Script para instalar o first boot em uma imagem do Ra<PERSON>berry Pi
# Use este script para modificar uma imagem .img antes de gravá-la no SD card

set -e

# Verificar argumentos
if [ $# -ne 1 ]; then
    echo "Uso: $0 <caminho-para-imagem.img>"
    echo "Exemplo: $0 /path/to/raspios-lite.img"
    exit 1
fi

IMAGE_PATH="$1"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Verificar se a imagem existe
if [ ! -f "$IMAGE_PATH" ]; then
    echo "ERRO: Imagem não encontrada: $IMAGE_PATH"
    exit 1
fi

# Verificar se estamos executando como root
if [ "$EUID" -ne 0 ]; then
    echo "Este script deve ser executado como root (use sudo)"
    exit 1
fi

echo "=== Instalando First Boot na imagem do Raspberry Pi ==="
echo "Imagem: $IMAGE_PATH"

# Criar diretório temporário para mount
MOUNT_DIR=$(mktemp -d)
LOOP_DEVICE=""

# Função de cleanup
cleanup() {
    echo "Limpando..."
    if [ -n "$LOOP_DEVICE" ]; then
        umount "$MOUNT_DIR" 2>/dev/null || true
        losetup -d "$LOOP_DEVICE" 2>/dev/null || true
    fi
    rmdir "$MOUNT_DIR" 2>/dev/null || true
}

# Trap para cleanup
trap cleanup EXIT

# Montar a imagem
echo "Montando imagem..."
LOOP_DEVICE=$(losetup -P --show -f "$IMAGE_PATH")
mount "${LOOP_DEVICE}p2" "$MOUNT_DIR"  # p2 é geralmente a partição root

echo "Instalando arquivos..."

# Copiar script principal
cp "$SCRIPT_DIR/first-boot.sh" "$MOUNT_DIR/usr/local/bin/"
chmod +x "$MOUNT_DIR/usr/local/bin/first-boot.sh"

# Copiar serviço systemd
cp "$SCRIPT_DIR/first-boot.service" "$MOUNT_DIR/etc/systemd/system/"

# Habilitar o serviço (criar symlink)
mkdir -p "$MOUNT_DIR/etc/systemd/system/multi-user.target.wants"
ln -sf "/etc/systemd/system/first-boot.service" \
       "$MOUNT_DIR/etc/systemd/system/multi-user.target.wants/first-boot.service"

echo "=== Instalação concluída ==="
echo ""
echo "IMPORTANTE:"
echo "1. Edite o arquivo first-boot.sh na imagem se necessário:"
echo "   $MOUNT_DIR/usr/local/bin/first-boot.sh"
echo ""
echo "2. Configure as variáveis:"
echo "   - GITHUB_REPO: URL do seu repositório"
echo "   - SCRIPT_PATH: Caminho do script no repositório"
echo "   - GITHUB_TOKEN: Token se for repositório privado"
echo ""
echo "3. A imagem está pronta para ser gravada no SD card"

# O cleanup será executado automaticamente
