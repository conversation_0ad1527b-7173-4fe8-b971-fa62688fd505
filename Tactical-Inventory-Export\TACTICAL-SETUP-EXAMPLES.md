# 📋 Exemplos de Configuração no Tactical RMM

Guia prático com exemplos reais de como configurar o script `Tactical-Inventory-Complete.ps1` no Tactical RMM para diferentes cenários.

## 🎯 Cenário 1: Relatório Diário Básico

### **Configuração do Script:**
- **Name:** `Relatório Diário - Cliente 8`
- **Description:** `Relatório diário de inventário para monitoramento`
- **Category:** `Daily Reports`
- **Timeout:** `300` (5 minutos)

### **Arguments:**
```
-ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "nwjdCoBE3P" -EmailSubject "Status Diário das Estações" -IncludeOffline false
```

### **Task Automation:**
- **Task Type:** Run Script
- **Name:** `Relatório Diário Sumire`
- **Schedule:** Daily, 8:00 AM
- **Run on:** Any Agent

---

## 🎯 Cenário 2: Relatório Semanal Completo

### **Configuração do Script:**
- **Name:** `Relatório Semanal Completo`
- **Description:** `Relatório semanal completo com todas as estações`
- **Category:** `Weekly Reports`
- **Timeout:** `600` (10 minutos)

### **Arguments:**
```
-ClientId 8 -EmailTo "<EMAIL>,<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "nwjdCoBE3P" -EmailSubject "Relatório Semanal de Inventário - Sumire" -IncludeOffline true
```

### **Task Automation:**
- **Task Type:** Run Script
- **Name:** `Relatório Semanal Sumire`
- **Schedule:** Weekly, Monday, 7:00 AM
- **Run on:** Any Agent

---

## 🎯 Cenário 3: Múltiplos Clientes

### **Cliente 1 - NVirtual (ID: 1)**

#### **Script Configuration:**
- **Name:** `Relatório NVirtual`
- **Arguments:**
```
-ClientId 1 -EmailTo "<EMAIL>,<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "nwjdCoBE3P" -EmailSubject "Inventário Interno NVirtual"
```

#### **Task Automation:**
- **Schedule:** Daily, 8:00 AM

### **Cliente 8 - Sumire (ID: 8)**

#### **Script Configuration:**
- **Name:** `Relatório Sumire`
- **Arguments:**
```
-ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "nwjdCoBE3P" -EmailSubject "Inventário Sumire"
```

#### **Task Automation:**
- **Schedule:** Daily, 8:30 AM

---

## 🎯 Cenário 4: Diferentes Servidores SMTP

### **Gmail/Google Workspace:**
```
-ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "senha_de_aplicativo_gmail" -SMTPServer "smtp.gmail.com" -SMTPPort 587
```

### **Office 365:**
```
-ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "senha_office365" -SMTPServer "smtp.office365.com" -SMTPPort 587
```

### **Servidor SMTP Personalizado:**
```
-ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "senha_smtp" -SMTPServer "mail.empresa.com" -SMTPPort 587
```

---

## 🎯 Cenário 5: Monitoramento Contínuo

### **Configuração para Alertas:**
- **Name:** `Monitoramento Estações Online`
- **Description:** `Monitoramento contínuo apenas de estações online`
- **Timeout:** `180` (3 minutos)

### **Arguments:**
```
-ClientId 8 -EmailTo "<EMAIL>,<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "nwjdCoBE3P" -EmailSubject "Monitoramento - Estações Ativas" -IncludeOffline false
```

### **Task Automation:**
- **Schedule:** Every 4 hours (6:00, 10:00, 14:00, 18:00)
- **Days:** Monday to Friday

---

## 🎯 Cenário 6: Relatório Mensal Executivo

### **Configuração:**
- **Name:** `Relatório Mensal Executivo`
- **Description:** `Relatório mensal completo para diretoria`
- **Timeout:** `900` (15 minutos)

### **Arguments:**
```
-ClientId 8 -EmailTo "<EMAIL>,<EMAIL>,<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "nwjdCoBE3P" -EmailSubject "Relatório Mensal de TI - $(Get-Date -Format 'MMMM yyyy')" -IncludeOffline true
```

### **Task Automation:**
- **Schedule:** Monthly, 1st day, 6:00 AM

---

## 🔧 Configurações Avançadas

### **1. Configuração com Variáveis de Ambiente**

Se você quiser usar variáveis de ambiente para senhas (mais seguro):

1. **Configurar no servidor:**
```bash
export TACTICAL_SMTP_PASSWORD="sua_senha_aqui"
```

2. **Modificar arguments:**
```
-ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "$env:TACTICAL_SMTP_PASSWORD"
```

### **2. Configuração com Múltiplos Formatos de Email**

#### **Apenas para Suporte (Online):**
```
-ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "senha" -EmailSubject "🟢 Estações Online - $(Get-Date -Format 'dd/MM/yyyy HH:mm')" -IncludeOffline false
```

#### **Para Gerência (Completo):**
```
-ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "senha" -EmailSubject "📊 Relatório Completo - $(Get-Date -Format 'dd/MM/yyyy')" -IncludeOffline true
```

### **3. Configuração por Horário**

#### **Manhã - Status Rápido:**
```
-ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "senha" -EmailSubject "🌅 Status Matinal" -IncludeOffline false
```

#### **Tarde - Relatório Completo:**
```
-ClientId 8 -EmailTo "<EMAIL>" -SMTPUser "<EMAIL>" -SMTPPassword "senha" -EmailSubject "📋 Relatório Vespertino" -IncludeOffline true
```

---

## 📅 Cronograma Sugerido

### **Segunda a Sexta:**
- **08:00** - Relatório diário (apenas online) → Suporte
- **12:00** - Status meio-dia (apenas online) → Equipe TI
- **18:00** - Relatório fim do dia (completo) → Gerência

### **Segunda-feira:**
- **07:00** - Relatório semanal completo → Diretoria

### **1º dia do mês:**
- **06:00** - Relatório mensal executivo → Todos os stakeholders

---

## 🎨 Personalização de Assuntos

### **Templates de Assunto:**
```bash
# Diário
"📊 Inventário Diário - [CLIENTE] - $(Get-Date -Format 'dd/MM/yyyy')"

# Semanal  
"📈 Relatório Semanal - [CLIENTE] - Semana $(Get-Date -UFormat '%V')"

# Mensal
"📋 Inventário Mensal - [CLIENTE] - $(Get-Date -Format 'MMMM yyyy')"

# Monitoramento
"🔍 Monitoramento - [CLIENTE] - $(Get-Date -Format 'dd/MM HH:mm')"

# Alerta
"⚠️ Alerta de Sistema - [CLIENTE] - $(Get-Date -Format 'dd/MM/yyyy HH:mm')"
```

---

## 🔍 Troubleshooting por Cenário

### **Cenário 1 - Relatório não chega:**
1. Verificar logs no Tactical RMM
2. Testar credenciais SMTP manualmente
3. Verificar se ClientId existe

### **Cenário 2 - Timeout frequente:**
1. Aumentar timeout para 600-900 segundos
2. Usar `-IncludeOffline false` para reduzir carga
3. Verificar conectividade da API

### **Cenário 3 - Excel corrompido:**
1. Verificar se módulo ImportExcel está atualizado
2. Verificar espaço em disco no servidor
3. Testar com menos dados primeiro

### **Cenário 4 - Múltiplos emails não chegam:**
1. Verificar formato dos emails (separados por vírgula)
2. Testar com um email primeiro
3. Verificar limites do servidor SMTP

---

## ✅ Checklist de Implementação

### **Antes de Configurar:**
- [ ] Servidor Tactical RMM com PowerShell Core instalado
- [ ] Credenciais SMTP válidas e testadas
- [ ] IDs dos clientes identificados
- [ ] Lista de destinatários definida
- [ ] Horários de execução planejados

### **Durante a Configuração:**
- [ ] Script salvo no Tactical RMM
- [ ] Arguments configurados corretamente
- [ ] Timeout adequado definido
- [ ] Tasks de automação criadas
- [ ] Teste manual executado com sucesso

### **Após a Configuração:**
- [ ] Primeiro email recebido e verificado
- [ ] Anexo Excel abre corretamente
- [ ] Logs verificados no Tactical RMM
- [ ] Cronograma de execução confirmado
- [ ] Equipe treinada para interpretar relatórios

---

**Configuração realizada por:** _______________  
**Data:** _______________  
**Testado e aprovado por:** _______________
