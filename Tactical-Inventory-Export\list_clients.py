#!/usr/bin/env python3
import requests

API_URL = "https://api.centralmesh.nvirtual.com.br"
API_TOKEN = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"

headers = {"X-API-KEY": API_TOKEN}

print("=== CLIENTES DISPONÍVEIS ===")
try:
    r = requests.get(f"{API_URL}/clients/", headers=headers)
    clients = r.json()
    for c in clients[:10]:
        print(f"ID: {c['id']} - Nome: {c['name']}")
except Exception as e:
    print(f"Erro: {e}")

print("\n=== SITES DISPONÍVEIS ===")
try:
    r = requests.get(f"{API_URL}/clients/sites/", headers=headers)
    sites = r.json()
    for s in sites[:10]:
        print(f"ID: {s['id']} - Nome: {s['name']} - Cliente: {s.get('client', 'N/A')}")
except Exception as e:
    print(f"Erro: {e}")
