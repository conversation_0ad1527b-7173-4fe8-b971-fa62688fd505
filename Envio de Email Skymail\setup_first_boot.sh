#!/bin/bash

# Script de instalação automatizada para Ubuntu Server 24.04
# Instala Zabbix Proxy, Zabbix Agent, Tactical RMM e configura IP fixo
# Este script é baixado automaticamente do GitHub pelo bootstrap
# Autor: <PERSON>
# Data: $(date +%Y-%m-%d)

set -e  # Para o script em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Detectar versão do Ubuntu e arquitetura
UBUNTU_VERSION=""
ARCHITECTURE=$(uname -m)
IS_ARM=false

if grep -q "Ubuntu 24.04" /etc/os-release; then
    UBUNTU_VERSION="24.04"
    log "Ubuntu 24.04 detectado"
elif grep -q "Ubuntu 22.04" /etc/os-release; then
    UBUNTU_VERSION="22.04"
    log "Ubuntu 22.04 detectado"
elif grep -q "Ubuntu 20.04" /etc/os-release; then
    UBUNTU_VERSION="20.04"
    log "Ubuntu 20.04 detectado"
else
    error "Este script foi desenvolvido para Ubuntu 20.04, 22.04 ou 24.04"
fi

# Detectar arquitetura ARM
if [[ "$ARCHITECTURE" == "aarch64" ]] || [[ "$ARCHITECTURE" == "armv7l" ]] || [[ "$ARCHITECTURE" == "arm64" ]]; then
    IS_ARM=true
    log "Arquitetura ARM detectada: $ARCHITECTURE"
else
    log "Arquitetura x86_64 detectada: $ARCHITECTURE"
fi

log "Bem Vindo(a) a instalação automatizada do Zabbix Proxy, Zabbix Agent e Tactical RMM para Ubuntu Server 20.04/22.04/24.04 (x86_64/ARM)."

# Configurações padrão (definidas pelo bootstrap)
ZABBIX_HOSTNAME="MUDAR"
TACTICAL_CLIENT_ID="1"
TACTICAL_CLIENT_FILIAL="1"

log "Configurações aplicadas automaticamente:"
info "• Zabbix Hostname: $ZABBIX_HOSTNAME"
info "• Tactical Client ID: $TACTICAL_CLIENT_ID"
info "• Tactical Site ID: $TACTICAL_CLIENT_FILIAL"

# Detectar IP atual e calcular IP fixo
log "Detectando configuração de rede atual..."
CURRENT_IP=$(ip route get ******* | awk '{print $7; exit}')
INTERFACE=$(ip route get ******* | awk '{print $5; exit}')
GATEWAY=$(ip route | grep default | awk '{print $3; exit}')

if [[ -z "$CURRENT_IP" ]]; then
    error "Não foi possível detectar o IP atual"
fi

# Extrair a rede e calcular o IP .222
IFS='.' read -ra IP_PARTS <<< "$CURRENT_IP"
NETWORK="${IP_PARTS[0]}.${IP_PARTS[1]}.${IP_PARTS[2]}"
PROPOSED_IP="${NETWORK}.222"

info "IP atual detectado: $CURRENT_IP"
info "Interface de rede: $INTERFACE"
info "Gateway: $GATEWAY"
info "IP proposto: $PROPOSED_IP"

# Verificar se o IP .222 já está em uso
log "Verificando se o IP $PROPOSED_IP já está em uso..."
if ping -c 3 -W 2 "$PROPOSED_IP" > /dev/null 2>&1; then
    warning "O IP $PROPOSED_IP já está em uso por outro equipamento!"
    warning "Mantendo configuração DHCP para evitar conflitos de IP."
    KEEP_DHCP=true
    FINAL_IP="$CURRENT_IP (DHCP)"
else
    log "IP $PROPOSED_IP está disponível!"
    FIXED_IP="$PROPOSED_IP"
    KEEP_DHCP=false
    FINAL_IP="$FIXED_IP (Fixo)"
fi

# Detectar DNS servers
DNS_SERVERS=$(systemd-resolve --status 2>/dev/null | grep "DNS Servers" | head -1 | awk '{print $3,$4}' | tr ' ' ',' || echo "*******,*******")
if [[ -z "$DNS_SERVERS" ]] || [[ "$DNS_SERVERS" == "," ]]; then
    DNS_SERVERS="*******,*******"
    warning "Usando DNS padrão: $DNS_SERVERS"
fi

log "Atualizando sistema..."
sudo apt-get update -y
sudo apt-get upgrade -y

log "Instalando pacotes básicos..."
sudo apt-get install vim traceroute snmp build-essential snmp-mibs-downloader iputils-ping net-tools curl wget -y

# Função para comparar versões
version_compare() {
    local version1=$1
    local version2=$2

    # Converter versões para números comparáveis (ex: 6.4.0 -> 060400, 7.0.0 -> 070000)
    local v1_major=$(echo $version1 | cut -d. -f1)
    local v1_minor=$(echo $version1 | cut -d. -f2)
    local v1_patch=$(echo $version1 | cut -d. -f3 2>/dev/null || echo "0")

    local v2_major=$(echo $version2 | cut -d. -f1)
    local v2_minor=$(echo $version2 | cut -d. -f2)
    local v2_patch=$(echo $version2 | cut -d. -f3 2>/dev/null || echo "0")

    local v1_num=$(printf "%02d%02d%02d" $v1_major $v1_minor $v1_patch)
    local v2_num=$(printf "%02d%02d%02d" $v2_major $v2_minor $v2_patch)

    if [[ $v1_num -lt $v2_num ]]; then
        return 0  # version1 < version2
    else
        return 1  # version1 >= version2
    fi
}

# Verificar se Zabbix já está instalado e verificar versões
ZABBIX_PROXY_INSTALLED=false
ZABBIX_AGENT_INSTALLED=false
NEED_VERSION_UPGRADE=false

log "Verificando instalações existentes do Zabbix..."

# Verificar Zabbix Proxy
if dpkg -l | grep -q "zabbix-proxy"; then
    ZABBIX_PROXY_INSTALLED=true
    ZABBIX_PROXY_VERSION=$(zabbix_proxy --version 2>/dev/null | head -1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1 || echo "")
    
    if [[ -n "$ZABBIX_PROXY_VERSION" ]] && version_compare "$ZABBIX_PROXY_VERSION" "7.0.0"; then
        warning "⚠️  Zabbix Proxy versão $ZABBIX_PROXY_VERSION é inferior à 7.0!"
        NEED_VERSION_UPGRADE=true
    fi
fi

# Verificar Zabbix Agent
if dpkg -l | grep -q "zabbix-agent"; then
    ZABBIX_AGENT_INSTALLED=true
    ZABBIX_AGENT_VERSION=$(zabbix_agentd --version 2>/dev/null | head -1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1 || echo "")
    
    if [[ -n "$ZABBIX_AGENT_VERSION" ]] && version_compare "$ZABBIX_AGENT_VERSION" "7.0.0"; then
        warning "⚠️  Zabbix Agent versão $ZABBIX_AGENT_VERSION é inferior à 7.0!"
        NEED_VERSION_UPGRADE=true
    fi
fi

# Se precisar de upgrade de versão, remover versões antigas
if [[ "$NEED_VERSION_UPGRADE" == "true" ]]; then
    log "Removendo versões antigas do Zabbix para instalar versão 7.0..."
    sudo systemctl stop zabbix-proxy 2>/dev/null || true
    sudo systemctl stop zabbix-agent 2>/dev/null || true
    sudo apt-get remove --purge zabbix-proxy* zabbix-agent* zabbix-release -y 2>/dev/null || true
    sudo apt-get autoremove -y
    sudo rm -f /etc/apt/sources.list.d/zabbix*.list
    ZABBIX_PROXY_INSTALLED=false
    ZABBIX_AGENT_INSTALLED=false
fi

# Instalar/Reinstalar Zabbix conforme necessário
if [[ "$ZABBIX_PROXY_INSTALLED" == "false" ]] || [[ "$ZABBIX_AGENT_INSTALLED" == "false" ]]; then
    log "Configurando repositório e instalando Zabbix..."
    cd /tmp

    # Configurar repositório Zabbix baseado na versão e arquitetura
    if [[ "$IS_ARM" == "true" ]]; then
        # Para ARM (Raspberry Pi)
        ZABBIX_URL="https://repo.zabbix.com/zabbix/7.0/raspbian/pool/main/z/zabbix-release/zabbix-release_latest_7.0%2Bdebian11_all.deb"
    elif [[ "$UBUNTU_VERSION" == "20.04" ]]; then
        ZABBIX_URL="https://repo.zabbix.com/zabbix/7.0/ubuntu/pool/main/z/zabbix-release/zabbix-release_latest_7.0%2Bubuntu20.04_all.deb"
    elif [[ "$UBUNTU_VERSION" == "22.04" ]]; then
        ZABBIX_URL="https://repo.zabbix.com/zabbix/7.0/ubuntu/pool/main/z/zabbix-release/zabbix-release_latest%2Bubuntu22.04_all.deb"
    else
        ZABBIX_URL="https://repo.zabbix.com/zabbix/7.0/ubuntu/pool/main/z/zabbix-release/zabbix-release_latest_7.0%2Bubuntu24.04_all.deb"
    fi

    wget "$ZABBIX_URL"
    sudo dpkg -i zabbix-release_*.deb
    sudo apt-get update -y

    if [[ "$ZABBIX_PROXY_INSTALLED" == "false" ]]; then
        log "Instalando Zabbix Proxy SQLite3..."
        sudo apt-get install zabbix-proxy-sqlite3 -y
    fi

    if [[ "$ZABBIX_AGENT_INSTALLED" == "false" ]]; then
        log "Instalando Zabbix Agent..."
        sudo apt-get install zabbix-agent -y
    fi
fi

log "Configurando Zabbix Proxy..."
sudo cp /etc/zabbix/zabbix_proxy.conf /etc/zabbix/zabbix_proxy.conf.backup.$(date +%Y%m%d_%H%M%S)
sudo sed -i "s/^# Server=.*/Server=monitora.nvirtual.com.br/" /etc/zabbix/zabbix_proxy.conf
sudo sed -i "s/^Server=.*/Server=monitora.nvirtual.com.br/" /etc/zabbix/zabbix_proxy.conf
sudo sed -i "s/^# Hostname=.*/Hostname=$ZABBIX_HOSTNAME/" /etc/zabbix/zabbix_proxy.conf
sudo sed -i "s/^Hostname=.*/Hostname=$ZABBIX_HOSTNAME/" /etc/zabbix/zabbix_proxy.conf
sudo sed -i "s/^# DBName=.*/DBName=\/tmp\/zabbix/" /etc/zabbix/zabbix_proxy.conf
sudo sed -i "s/^DBName=.*/DBName=\/tmp\/zabbix/" /etc/zabbix/zabbix_proxy.conf
sudo sed -i "s/^# EnableRemoteCommands=.*/EnableRemoteCommands=1/" /etc/zabbix/zabbix_proxy.conf
sudo sed -i "s/^EnableRemoteCommands=.*/EnableRemoteCommands=1/" /etc/zabbix/zabbix_proxy.conf

log "Configurando Zabbix Agent..."
sudo cp /etc/zabbix/zabbix_agentd.conf /etc/zabbix/zabbix_agentd.conf.backup.$(date +%Y%m%d_%H%M%S)
sudo sed -i "s/^# Server=.*/Server=127.0.0.1/" /etc/zabbix/zabbix_agentd.conf
sudo sed -i "s/^Server=.*/Server=127.0.0.1/" /etc/zabbix/zabbix_agentd.conf
sudo sed -i "s/^# ServerActive=.*/ServerActive=127.0.0.1/" /etc/zabbix/zabbix_agentd.conf
sudo sed -i "s/^ServerActive=.*/ServerActive=127.0.0.1/" /etc/zabbix/zabbix_agentd.conf
sudo sed -i "s/^# Hostname=.*/Hostname=$ZABBIX_HOSTNAME/" /etc/zabbix/zabbix_agentd.conf
sudo sed -i "s/^Hostname=.*/Hostname=$ZABBIX_HOSTNAME/" /etc/zabbix/zabbix_agentd.conf
sudo sed -i "s/^# EnableRemoteCommands=.*/EnableRemoteCommands=1/" /etc/zabbix/zabbix_agentd.conf
sudo sed -i "s/^EnableRemoteCommands=.*/EnableRemoteCommands=1/" /etc/zabbix/zabbix_agentd.conf

log "Habilitando e iniciando serviços Zabbix..."
sudo systemctl unmask zabbix-agent 2>/dev/null || true
sudo systemctl unmask zabbix-proxy 2>/dev/null || true
sudo systemctl stop zabbix-agent 2>/dev/null || true
sudo systemctl stop zabbix-proxy 2>/dev/null || true
sudo systemctl enable zabbix-agent
sudo systemctl enable zabbix-proxy
sudo systemctl start zabbix-agent
sudo systemctl start zabbix-proxy

sleep 5

# Verificar status dos serviços
if sudo systemctl is-active --quiet zabbix-agent; then
    log "✅ Zabbix Agent iniciado com sucesso"
else
    warning "❌ Falha ao iniciar zabbix-agent"
    sudo systemctl restart zabbix-agent
fi

if sudo systemctl is-active --quiet zabbix-proxy; then
    log "✅ Zabbix Proxy iniciado com sucesso"
else
    warning "❌ Falha ao iniciar zabbix-proxy"
    sudo systemctl restart zabbix-proxy
fi

# Instalar Tactical RMM
log "Instalando Tactical RMM Agent..."

# Baixar script do Tactical RMM
cd /tmp
rm -f rmmagent-linux.sh

if wget https://raw.githubusercontent.com/netvolt/LinuxRMM-Script/main/rmmagent-linux.sh; then
    chmod +x rmmagent-linux.sh
    
    # Executar instalação do Tactical RMM baseado na arquitetura
    if [[ "$IS_ARM" == "true" ]]; then
        TACTICAL_MESH_URL='https://mesh.centralmesh.nvirtual.com.br/meshagents?id=7Nss2LHe67mTwByGHQ3H3lOI4x8Awfk6kwbQgxSMMq%40qIJKjK6OOSBMWfXBYgPlb&installflags=0&meshinstall=26'
    else
        TACTICAL_MESH_URL='https://mesh.centralmesh.nvirtual.com.br/meshagents?id=7Nss2LHe67mTwByGHQ3H3lOI4x8Awfk6kwbQgxSMMq%40qIJKjK6OOSBMWfXBYgPlb&installflags=2&meshinstall=6'
    fi
    
    ./rmmagent-linux.sh install "$TACTICAL_MESH_URL" 'https://api.centralmesh.nvirtual.com.br' "$TACTICAL_CLIENT_ID" "$TACTICAL_CLIENT_FILIAL" 'ecd275ac5baa7e615674a38f2de333f00dd2635e179f9a08e4026db2e5856ae3' 'server'
    
    if [[ $? -eq 0 ]]; then
        log "Tactical RMM Agent instalado com sucesso!"
    else
        warning "Houve um problema na instalação do Tactical RMM Agent."
    fi
else
    warning "Não foi possível baixar o script do Tactical RMM"
fi

# Configurar firewall
log "Configuração de firewall básico..."
if ! command -v ufw &> /dev/null; then
    sudo apt-get install ufw -y
fi

sudo ufw allow 22/tcp
sudo ufw allow 10050/tcp
sudo ufw allow 10051/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable

# Configurar IP (fixo ou manter DHCP)
log "Configurando rede..."
if [[ "$KEEP_DHCP" == "true" ]]; then
    log "Mantendo configuração DHCP atual..."
else
    log "Configurando IP fixo..."
    NETPLAN_FILE="/etc/netplan/01-netcfg.yaml"
    
    sudo cp $NETPLAN_FILE ${NETPLAN_FILE}.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true
    
    DNS_ARRAY=$(echo $DNS_SERVERS | sed 's/,/, /g')
    
    sudo tee $NETPLAN_FILE > /dev/null <<EOF
network:
  version: 2
  renderer: networkd
  ethernets:
    $INTERFACE:
      dhcp4: false
      addresses:
        - $FIXED_IP/24
      routes:
        - to: default
          via: $GATEWAY
      nameservers:
        addresses: [$DNS_ARRAY]
EOF

    sudo netplan apply
    sleep 5
fi

# Determinar diretório do usuário
USER_HOME="/home/<USER>"
if [[ ! -d "/home/<USER>" ]]; then
    if [[ -d "/home/<USER>" ]]; then
        USER_HOME="/home/<USER>"
    elif [[ -d "/home/<USER>" ]]; then
        USER_HOME="/home/<USER>"
    else
        mkdir -p /home/<USER>
        USER_HOME="/home/<USER>"
        if ! id "suportenv" &>/dev/null; then
            useradd -m -s /bin/bash suportenv 2>/dev/null || true
            usermod -aG sudo suportenv 2>/dev/null || true
        fi
    fi
fi

mkdir -p "$USER_HOME"

# Criar arquivo de informações do sistema
cat > "$USER_HOME/SISTEMA_INFO.txt" << EOF
=== RASPBERRY PI CONFIGURADO AUTOMATICAMENTE ===
Data da configuração: $(date)
Desenvolvido por: Paulo Matheus - NVirtual

=== CONFIGURAÇÕES APLICADAS ===
Zabbix Hostname: $ZABBIX_HOSTNAME
Tactical Client ID: $TACTICAL_CLIENT_ID
Tactical Site ID: $TACTICAL_CLIENT_FILIAL

=== PRÓXIMOS PASSOS ===
1. Acesse o Zabbix Server (monitora.nvirtual.com.br)
2. Altere o nome do proxy de "$ZABBIX_HOSTNAME" para o nome desejado
3. O sistema já está monitorado pelo Tactical RMM

=== INFORMAÇÕES TÉCNICAS ===
IP do sistema: $(hostname -I | awk '{print $1}')
Hostname: $(hostname)
Zabbix Server: monitora.nvirtual.com.br
Tactical Mesh: mesh.centralmesh.nvirtual.com.br
Tactical API: api.centralmesh.nvirtual.com.br

=== COMANDOS ÚTEIS ===
Verificar status Zabbix Proxy: sudo systemctl status zabbix-proxy
Verificar status Zabbix Agent: sudo systemctl status zabbix-agent
Logs Zabbix Proxy: sudo tail -f /var/log/zabbix/zabbix_proxy.log
Logs Zabbix Agent: sudo tail -f /var/log/zabbix/zabbix_agentd.log

=== ALTERAÇÃO DO HOSTNAME ZABBIX ===
Para alterar o hostname do Zabbix Proxy:
1. sudo nano /etc/zabbix/zabbix_proxy.conf
2. Alterar linha: Hostname=NOVO_NOME
3. sudo systemctl restart zabbix-proxy
EOF

# Definir proprietário correto do arquivo
USER_NAME=$(basename "$USER_HOME")
if id "$USER_NAME" &>/dev/null; then
    chown "$USER_NAME:$USER_NAME" "$USER_HOME/SISTEMA_INFO.txt"
else
    chmod 644 "$USER_HOME/SISTEMA_INFO.txt"
fi

log "Instalação concluída com sucesso!"
echo
echo "=========================================="
echo "RESUMO DA INSTALAÇÃO"
echo "=========================================="
echo "Configuração de IP: $FINAL_IP"
echo "Interface de rede: $INTERFACE"
echo "Gateway: $GATEWAY"
echo "DNS: $DNS_SERVERS"
echo "Hostname Zabbix: $ZABBIX_HOSTNAME"
echo "Zabbix Server: monitora.nvirtual.com.br"
echo
echo "Tactical RMM:"
echo "- Cliente ID: $TACTICAL_CLIENT_ID"
echo "- Filial: $TACTICAL_CLIENT_FILIAL"
echo "- Mesh Server: mesh.centralmesh.nvirtual.com.br"
echo "- API Server: api.centralmesh.nvirtual.com.br"
echo
echo "Serviços instalados e ativos:"
echo "- Zabbix Proxy (porta 10051)"
echo "- Zabbix Agent (porta 10050)"
echo "- Tactical RMM Agent"
echo
echo "Logs importantes:"
echo "- Zabbix Proxy: /var/log/zabbix/zabbix_proxy.log"
echo "- Zabbix Agent: /var/log/zabbix/zabbix_agentd.log"
echo
echo "Para verificar status dos serviços:"
echo "sudo systemctl status zabbix-proxy"
echo "sudo systemctl status zabbix-agent"
echo
echo "Desenvolvido por: Paulo Matheus"
echo "=========================================="

log "Script finalizado!"
