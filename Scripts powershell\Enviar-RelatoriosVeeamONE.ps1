<#
.SYNOPSIS
    Script para envio manual dos relatórios agendados do Veeam ONE

.DESCRIPTION
    Este script permite disparar manualmente os relatórios que estão configurados 
    no "Scheduling" do Veeam ONE, sem precisar aguardar o horário agendado.
    
    O script pode:
    - Listar todos os relatórios agendados
    - Executar um relatório específico
    - Executar todos os relatórios agendados
    - Verificar o status de execução dos relatórios

.PARAMETER VeeamOneServer
    Servidor do Veeam ONE (padrão: localhost)

.PARAMETER Port
    Porta do serviço Veeam ONE (padrão: 9393)

.PARAMETER ReportName
    Nome específico do relatório para executar (opcional)

.PARAMETER ExecuteAll
    Switch para executar todos os relatórios agendados

.PARAMETER ListOnly
    Switch para apenas listar os relatórios disponíveis

.PARAMETER Credential
    Credenciais para autenticação (opcional - usa credenciais atuais se não especificado)

.EXAMPLE
    .\Enviar-RelatoriosVeeamONE.ps1 -ListOnly
    Lista todos os relatórios agendados

.EXAMPLE
    .\Enviar-RelatoriosVeeamONE.ps1 -ReportName "Backup Status Report"
    Executa um relatório específico

.EXAMPLE
    .\Enviar-RelatoriosVeeamONE.ps1 -ExecuteAll
    Executa todos os relatórios agendados

.NOTES
    Autor: Paulo Matheus - NVirtual
    Data: $(Get-Date -Format "dd/MM/yyyy")
    Versão: 1.0
    
    Requisitos:
    - Veeam ONE instalado e configurado
    - Permissões adequadas para acessar a API do Veeam ONE
    - PowerShell 5.1 ou superior
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$VeeamOneServer = "localhost",
    
    [Parameter(Mandatory = $false)]
    [int]$Port = 9393,
    
    [Parameter(Mandatory = $false)]
    [string]$ReportName,
    
    [Parameter(Mandatory = $false)]
    [switch]$ExecuteAll,
    
    [Parameter(Mandatory = $false)]
    [switch]$ListOnly,
    
    [Parameter(Mandatory = $false)]
    [System.Management.Automation.PSCredential]$Credential
)

# Configurações globais
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# URLs da API do Veeam ONE
$BaseUrl = "https://$VeeamOneServer`:$Port/api"
$AuthUrl = "$BaseUrl/auth"
$ReportsUrl = "$BaseUrl/reports"

# Variáveis globais
$AuthToken = $null
$Headers = @{}

#region Funções Auxiliares

function Write-Log {
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [ValidateSet("INFO", "WARNING", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "INFO" { "White" }
        "WARNING" { "Yellow" }
        "ERROR" { "Red" }
        "SUCCESS" { "Green" }
    }
    
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Connect-VeeamOneAPI {
    param(
        [Parameter(Mandatory = $false)]
        [System.Management.Automation.PSCredential]$Credential
    )
    
    try {
        Write-Log "Conectando ao Veeam ONE Server: $VeeamOneServer" -Level "INFO"
        
        # Ignorar certificados SSL auto-assinados
        if (-not ([System.Management.Automation.PSTypeName]'ServerCertificateValidationCallback').Type) {
            $certCallback = @"
                using System;
                using System.Net;
                using System.Net.Security;
                using System.Security.Cryptography.X509Certificates;
                public class ServerCertificateValidationCallback
                {
                    public static void Ignore()
                    {
                        if(ServicePointManager.ServerCertificateValidationCallback ==null)
                        {
                            ServicePointManager.ServerCertificateValidationCallback += 
                                delegate
                                (
                                    Object obj, 
                                    X509Certificate certificate, 
                                    X509Chain chain, 
                                    SslPolicyErrors errors
                                )
                                {
                                    return true;
                                };
                        }
                    }
                }
"@
            Add-Type $certCallback
        }
        [ServerCertificateValidationCallback]::Ignore()
        
        # Preparar credenciais
        if (-not $Credential) {
            $Credential = Get-Credential -Message "Digite as credenciais para o Veeam ONE"
        }
        
        # Autenticação
        $authBody = @{
            username = $Credential.UserName
            password = $Credential.GetNetworkCredential().Password
        } | ConvertTo-Json
        
        $authResponse = Invoke-RestMethod -Uri $AuthUrl -Method POST -Body $authBody -ContentType "application/json"
        
        if ($authResponse.access_token) {
            $script:AuthToken = $authResponse.access_token
            $script:Headers = @{
                "Authorization" = "Bearer $AuthToken"
                "Content-Type" = "application/json"
            }
            Write-Log "Autenticação realizada com sucesso" -Level "SUCCESS"
            return $true
        } else {
            Write-Log "Falha na autenticação" -Level "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "Erro ao conectar com Veeam ONE: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

function Get-ScheduledReports {
    try {
        Write-Log "Obtendo lista de relatórios agendados..." -Level "INFO"
        
        $response = Invoke-RestMethod -Uri "$ReportsUrl/scheduled" -Method GET -Headers $Headers
        
        if ($response -and $response.Count -gt 0) {
            Write-Log "Encontrados $($response.Count) relatórios agendados" -Level "SUCCESS"
            return $response
        } else {
            Write-Log "Nenhum relatório agendado encontrado" -Level "WARNING"
            return @()
        }
    }
    catch {
        Write-Log "Erro ao obter relatórios agendados: $($_.Exception.Message)" -Level "ERROR"
        return @()
    }
}

function Start-ReportExecution {
    param(
        [Parameter(Mandatory = $true)]
        [string]$ReportId,
        
        [Parameter(Mandatory = $true)]
        [string]$ReportName
    )
    
    try {
        Write-Log "Iniciando execução do relatório: $ReportName" -Level "INFO"
        
        $executeUrl = "$ReportsUrl/$ReportId/execute"
        $response = Invoke-RestMethod -Uri $executeUrl -Method POST -Headers $Headers
        
        if ($response.jobId) {
            Write-Log "Relatório '$ReportName' iniciado com sucesso. Job ID: $($response.jobId)" -Level "SUCCESS"
            return $response.jobId
        } else {
            Write-Log "Falha ao iniciar relatório '$ReportName'" -Level "ERROR"
            return $null
        }
    }
    catch {
        Write-Log "Erro ao executar relatório '$ReportName': $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

function Get-ReportStatus {
    param(
        [Parameter(Mandatory = $true)]
        [string]$JobId
    )
    
    try {
        $statusUrl = "$BaseUrl/jobs/$JobId"
        $response = Invoke-RestMethod -Uri $statusUrl -Method GET -Headers $Headers
        return $response
    }
    catch {
        Write-Log "Erro ao verificar status do job $JobId`: $($_.Exception.Message)" -Level "WARNING"
        return $null
    }
}

#endregion

#region Função Principal

function Main {
    Write-Log "=== SCRIPT DE ENVIO MANUAL DE RELATÓRIOS VEEAM ONE ===" -Level "INFO"
    Write-Log "Desenvolvido por: Paulo Matheus - NVirtual" -Level "INFO"
    Write-Log "Data: $(Get-Date -Format 'dd/MM/yyyy HH:mm:ss')" -Level "INFO"
    Write-Log "========================================================" -Level "INFO"
    
    # Conectar à API do Veeam ONE
    if (-not (Connect-VeeamOneAPI -Credential $Credential)) {
        Write-Log "Não foi possível conectar ao Veeam ONE. Encerrando script." -Level "ERROR"
        exit 1
    }
    
    # Obter relatórios agendados
    $scheduledReports = Get-ScheduledReports
    
    if ($scheduledReports.Count -eq 0) {
        Write-Log "Nenhum relatório agendado encontrado. Encerrando script." -Level "WARNING"
        exit 0
    }
    
    # Listar relatórios se solicitado
    if ($ListOnly) {
        Write-Log "=== RELATÓRIOS AGENDADOS DISPONÍVEIS ===" -Level "INFO"
        foreach ($report in $scheduledReports) {
            Write-Host "ID: $($report.id) | Nome: $($report.name) | Próxima Execução: $($report.nextRun)" -ForegroundColor Cyan
        }
        Write-Log "=========================================" -Level "INFO"
        exit 0
    }
    
    # Executar relatório específico
    if ($ReportName) {
        $targetReport = $scheduledReports | Where-Object { $_.name -like "*$ReportName*" }
        
        if (-not $targetReport) {
            Write-Log "Relatório '$ReportName' não encontrado" -Level "ERROR"
            Write-Log "Relatórios disponíveis:" -Level "INFO"
            $scheduledReports | ForEach-Object { Write-Host "- $($_.name)" -ForegroundColor Yellow }
            exit 1
        }
        
        $jobId = Start-ReportExecution -ReportId $targetReport.id -ReportName $targetReport.name
        
        if ($jobId) {
            Write-Log "Monitorando execução do relatório..." -Level "INFO"
            do {
                Start-Sleep -Seconds 5
                $status = Get-ReportStatus -JobId $jobId
                if ($status) {
                    Write-Log "Status: $($status.state)" -Level "INFO"
                }
            } while ($status -and $status.state -eq "Running")
            
            if ($status -and $status.state -eq "Completed") {
                Write-Log "Relatório '$($targetReport.name)' executado com sucesso!" -Level "SUCCESS"
            } else {
                Write-Log "Relatório '$($targetReport.name)' falhou ou foi cancelado" -Level "ERROR"
            }
        }
        exit 0
    }
    
    # Executar todos os relatórios
    if ($ExecuteAll) {
        Write-Log "Iniciando execução de todos os relatórios agendados..." -Level "INFO"
        $jobIds = @()
        
        foreach ($report in $scheduledReports) {
            $jobId = Start-ReportExecution -ReportId $report.id -ReportName $report.name
            if ($jobId) {
                $jobIds += @{
                    JobId = $jobId
                    ReportName = $report.name
                }
            }
            Start-Sleep -Seconds 2  # Pequena pausa entre execuções
        }
        
        if ($jobIds.Count -gt 0) {
            Write-Log "Monitorando execução de $($jobIds.Count) relatórios..." -Level "INFO"
            
            do {
                Start-Sleep -Seconds 10
                $runningJobs = 0
                
                foreach ($job in $jobIds) {
                    $status = Get-ReportStatus -JobId $job.JobId
                    if ($status -and $status.state -eq "Running") {
                        $runningJobs++
                    } elseif ($status -and $status.state -eq "Completed") {
                        Write-Log "✓ Relatório '$($job.ReportName)' concluído" -Level "SUCCESS"
                    } elseif ($status -and $status.state -ne "Running") {
                        Write-Log "✗ Relatório '$($job.ReportName)' falhou: $($status.state)" -Level "ERROR"
                    }
                }
                
                if ($runningJobs -gt 0) {
                    Write-Log "$runningJobs relatórios ainda em execução..." -Level "INFO"
                }
                
            } while ($runningJobs -gt 0)
            
            Write-Log "Execução de todos os relatórios concluída!" -Level "SUCCESS"
        }
        exit 0
    }
    
    # Se nenhuma opção específica foi escolhida, mostrar menu interativo
    Write-Log "=== MENU INTERATIVO ===" -Level "INFO"
    Write-Host "1. Listar relatórios agendados" -ForegroundColor Cyan
    Write-Host "2. Executar relatório específico" -ForegroundColor Cyan
    Write-Host "3. Executar todos os relatórios" -ForegroundColor Cyan
    Write-Host "4. Sair" -ForegroundColor Cyan
    
    $choice = Read-Host "Escolha uma opção (1-4)"
    
    switch ($choice) {
        "1" {
            Write-Log "=== RELATÓRIOS AGENDADOS DISPONÍVEIS ===" -Level "INFO"
            foreach ($report in $scheduledReports) {
                Write-Host "ID: $($report.id) | Nome: $($report.name) | Próxima Execução: $($report.nextRun)" -ForegroundColor Cyan
            }
        }
        "2" {
            Write-Log "Relatórios disponíveis:" -Level "INFO"
            for ($i = 0; $i -lt $scheduledReports.Count; $i++) {
                Write-Host "$($i + 1). $($scheduledReports[$i].name)" -ForegroundColor Yellow
            }
            
            $reportChoice = Read-Host "Digite o número do relatório para executar"
            $reportIndex = [int]$reportChoice - 1
            
            if ($reportIndex -ge 0 -and $reportIndex -lt $scheduledReports.Count) {
                $selectedReport = $scheduledReports[$reportIndex]
                $jobId = Start-ReportExecution -ReportId $selectedReport.id -ReportName $selectedReport.name
                
                if ($jobId) {
                    Write-Log "Relatório '$($selectedReport.name)' iniciado com sucesso!" -Level "SUCCESS"
                }
            } else {
                Write-Log "Opção inválida" -Level "ERROR"
            }
        }
        "3" {
            Write-Log "Executando todos os relatórios agendados..." -Level "INFO"
            foreach ($report in $scheduledReports) {
                Start-ReportExecution -ReportId $report.id -ReportName $report.name
                Start-Sleep -Seconds 2
            }
            Write-Log "Todos os relatórios foram iniciados!" -Level "SUCCESS"
        }
        "4" {
            Write-Log "Saindo..." -Level "INFO"
            exit 0
        }
        default {
            Write-Log "Opção inválida" -Level "ERROR"
        }
    }
}

#endregion

# Executar função principal
Main
