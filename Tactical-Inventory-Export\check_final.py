import csv

filename = 'FamaSegurosMeuRelatorio_FINAL_SIMPLIFICADO_Cliente33_CONSOLIDADO.csv'

with open(filename, 'r', encoding='utf-8') as f:
    reader = csv.DictReader(f)
    rows = list(reader)

print(f'Total de agentes: {len(rows)}')
print('\nPrimeiros 3 agentes:')

for i, row in enumerate(rows[:3]):
    print(f'{i+1}. {row["Hostname"]} - Cliente: {row["Cliente"]} - Site: {row["Site"]}')
    print(f'   CPU: {row.get("CPU_Nome", "N/A")}')
    print(f'   RAM: {row.get("RAM_Total_GB", "N/A")} GB')
    print(f'   Discos: {row.get("Quantidade_Discos", "N/A")}')
    print(f'   Espaço Total: {row.get("Espaco_Total_GB", "N/A")} GB')
    print(f'   Espaço Usado: {row.get("Espaco_Usado_GB", "N/A")} GB ({row.get("Percentual_Uso", "N/A")}%)')
    print()

# Verificar colunas de disco
headers = list(rows[0].keys())
disk_cols = [h for h in headers if 'Disco' in h]
print(f'Colunas de disco: {len(disk_cols)}')
for col in disk_cols[:10]:  # Primeiras 10
    print(f'  {col}')

# Verificar colunas de hardware
hw_cols = [h for h in headers if any(x in h for x in ['CPU', 'RAM', 'BIOS', 'Placa', 'Sistema'])]
print(f'\nColunas de hardware: {len(hw_cols)}')
for col in hw_cols[:10]:  # Primeiras 10
    print(f'  {col}')
