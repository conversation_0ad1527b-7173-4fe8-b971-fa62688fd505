# 📊 Guia de Exportação para Excel

Documentação completa para exportar resultados do Network Scanner para arquivos Excel (.xlsx).

## 🎯 Funcionalidades Excel

### 📋 **Abas Criadas Automaticamente:**

1. **📱 Dispositivos Encontrados** (Principal)
   - Lista completa de todos os dispositivos
   - Colunas: IP, Hostname, OS, Método, Tipo/Modelo, TTL, Confiança, Portas, Data/Hora

2. **📊 Estatísticas OS**
   - Contagem por sistema operacional
   - Percentuais calculados automaticamente

3. **📈 Estatísticas Dispositivos**
   - Contagem por tipo de dispositivo
   - Percentuais de cada categoria

4. **🔍 Métodos Detecção**
   - Estatísticas dos métodos utilizados
   - Eficácia de cada técnica

5. **ℹ️ Informações do Scan**
   - Detalhes técnicos da execução
   - Configurações utilizadas

## 🚀 Como Usar

### **Instalação da Dependência:**
```bash
# Instalar openpyxl (necessário para Excel)
pip install openpyxl
```

### **Scanner Principal:**
```bash
# Exportar apenas para Excel
python3 network_scanner.py --excel

# Exportar Excel + JSON
python3 network_scanner.py --excel --save

# Especificar nome do arquivo
python3 network_scanner.py --excel --output "scan_empresa_2025"

# Scan completo com Excel
python3 network_scanner.py -n ***********/24 -t 100 --excel -o "rede_filial"
```

### **Scanner Tactical RMM:**
```bash
# Exportar para Excel
python3 tactical_network_scanner.py ***********/24 --excel

# Com nome personalizado
python3 tactical_network_scanner.py ***********/24 --excel --output "scan_cliente"
```

### **Script PowerShell (Tactical RMM):**
```powershell
# Argumentos no Tactical RMM:
-NetworkRange "***********/24" -ExcelOutput

# Com arquivo personalizado:
-NetworkRange "***********/24" -ExcelOutput -OutputFile "scan_cliente_2025"

# Excel + JSON:
-NetworkRange "***********/24" -ExcelOutput -JsonOutput -OutputFile "relatorio_completo"
```

## 📋 Estrutura do Arquivo Excel

### **Aba 1: Dispositivos Encontrados**
| Coluna | Descrição | Exemplo |
|--------|-----------|---------|
| **IP** | Endereço IP do dispositivo | *********** |
| **Hostname** | Nome do dispositivo na rede | archer-c5.local |
| **Sistema Operacional** | OS identificado | Network Device |
| **Método Detecção** | Como foi identificado | TTL=255+HTTP |
| **Tipo/Modelo** | Dispositivo específico | TP-Link Archer C5 Router |
| **TTL** | Time-To-Live do ping | 255 |
| **Confiança (%)** | Precisão da identificação | 95 |
| **Portas Abertas** | Serviços detectados | 80(HTTP), 443(HTTPS), 23(Telnet) |
| **Data/Hora Scan** | Timestamp da detecção | 08/01/2025 16:45:30 |

### **Aba 2: Estatísticas OS**
| Sistema Operacional | Quantidade | Percentual |
|-------------------|------------|------------|
| Windows | 15 | 45.5% |
| Linux/Unix | 10 | 30.3% |
| Network Device | 6 | 18.2% |
| iOS | 2 | 6.0% |

### **Aba 3: Estatísticas Dispositivos**
| Tipo de Dispositivo | Quantidade | Percentual |
|-------------------|------------|------------|
| Windows Server | 8 | 24.2% |
| TP-Link Router | 4 | 12.1% |
| Linux Server | 6 | 18.2% |
| iPhone/iPad | 2 | 6.0% |

### **Aba 4: Métodos Detecção**
| Método de Detecção | Quantidade | Percentual |
|-------------------|------------|------------|
| TTL=128+Ports | 8 | 24.2% |
| TTL=255+HTTP | 4 | 12.1% |
| TTL=64+Ports | 6 | 18.2% |
| Hostname | 5 | 15.2% |

### **Aba 5: Informações do Scan**
| Campo | Valor |
|-------|-------|
| **Rede Escaneada** | ***********/24 |
| **Total de IPs** | 254 |
| **Dispositivos Encontrados** | 33 |
| **Threads Utilizadas** | 100 |
| **Timeout (segundos)** | 2 |
| **Data/Hora do Scan** | 08/01/2025 16:45:30 |
| **Desenvolvido por** | Paulo Matheus |

## 🎨 Formatação Automática

### **Estilos Aplicados:**
- ✅ **Cabeçalhos**: Fundo azul, texto branco, negrito
- ✅ **Larguras**: Colunas ajustadas automaticamente
- ✅ **Alinhamento**: Centralizado para cabeçalhos
- ✅ **Cores**: Esquema profissional azul/branco

### **Cálculos Automáticos:**
- ✅ **Percentuais**: Calculados automaticamente
- ✅ **Totais**: Contagens precisas
- ✅ **Formatação de data**: DD/MM/AAAA HH:MM:SS

## 📁 Nomes de Arquivo

### **Padrões Automáticos:**
- **Scanner Principal**: `network_scan_YYYYMMDD_HHMMSS.xlsx`
- **Tactical RMM**: `tactical_scan_YYYYMMDD_HHMMSS.xlsx`

### **Nomes Personalizados:**
```bash
# Gera: scan_empresa_2025.xlsx
--output "scan_empresa_2025"

# Gera: relatorio_rede_filial.xlsx
--output "relatorio_rede_filial"
```

## 🔧 Troubleshooting

### **Erro: openpyxl não encontrado**
```bash
# Solução:
pip install openpyxl

# Ou no Windows:
py -m pip install openpyxl
```

### **Erro: Permissão negada**
- Feche o arquivo Excel se estiver aberto
- Verifique permissões da pasta de destino
- Execute como administrador se necessário

### **Arquivo muito grande**
- Para redes grandes (>1000 dispositivos), o Excel pode demorar
- Use filtros no Excel para analisar subconjuntos
- Considere dividir em múltiplos scans menores

## 📊 Casos de Uso

### **1. Relatórios Executivos**
- Abra a aba "Estatísticas OS" para visão geral
- Use gráficos do Excel para apresentações
- Exporte estatísticas para PowerPoint

### **2. Auditoria Técnica**
- Aba "Dispositivos Encontrados" para análise detalhada
- Filtre por tipo de dispositivo
- Ordene por IP ou hostname

### **3. Inventário de Rede**
- Use como base para documentação
- Importe para sistemas de inventário
- Mantenha histórico de scans

### **4. Análise de Segurança**
- Identifique dispositivos desconhecidos
- Verifique portas abertas suspeitas
- Compare com scans anteriores

## 💡 Dicas Avançadas

### **Análise no Excel:**
```excel
# Filtrar apenas roteadores:
Coluna "Tipo/Modelo" contém "Router"

# Contar dispositivos Windows:
=COUNTIF(C:C,"Windows")

# Dispositivos com muitas portas abertas:
Filtrar coluna "Portas Abertas" por comprimento
```

### **Gráficos Recomendados:**
- **Pizza**: Distribuição de OS
- **Barras**: Tipos de dispositivos
- **Linha**: Evolução temporal (múltiplos scans)

### **Formatação Condicional:**
- **Verde**: Confiança > 90%
- **Amarelo**: Confiança 70-90%
- **Vermelho**: Confiança < 70%

## 📋 Checklist de Uso

- [ ] Instalar openpyxl (`pip install openpyxl`)
- [ ] Executar scan com `--excel`
- [ ] Verificar arquivo .xlsx gerado
- [ ] Abrir no Excel/LibreOffice
- [ ] Revisar todas as 5 abas
- [ ] Aplicar filtros conforme necessário
- [ ] Salvar cópia para histórico

---

**📊 Excel Export v1.0**  
**✅ 5 abas automáticas com formatação profissional**  
**🎯 Ideal para relatórios, auditorias e inventários**
