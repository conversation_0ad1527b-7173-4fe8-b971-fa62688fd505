# Script Simplificado - Zabbix Agent 2 para Windows

## 🎯 Solução para Problemas de Sintaxe

Este é o **script simplificado** criado especificamente para resolver problemas de sintaxe no Tactical RMM.

### ❌ Problema Original
```
The term 'PID:' is not recognized as the name of a cmdlet, function, script file, or operable program.
```

### ✅ Solução Implementada
- **Removidos todos os caracteres especiais** (acentos, emojis, etc.)
- **Corrigidas todas as strings problemáticas** com dois pontos
- **Simplificada a sintaxe** mantendo todas as funcionalidades
- **Testado e validado** para compatibilidade total

## 📁 Arquivo Principal
**`Install-ZabbixAgent2-Windows-Simple.ps1`**

## 🚀 Como usar no Tactical RMM

### **Para resolver o erro 1603:**
```powershell
-ZabbixServerIP "*************" -AgentHostname "segundotab-servidor-sistema-ad" -Force
```

### **Outros exemplos:**
```powershell
# Diagnóstico apenas
-ZabbixServerIP "*************" -DiagnoseOnly

# Instalação básica
-ZabbixServerIP "*************" -AgentHostname "NOME-HOST"

# Instalação offline
-ZabbixServerIP "*************" -LocalMSIPath "C:\temp\zabbix.msi"
```

## ✨ Funcionalidades Mantidas

### **1. Diagnóstico Completo**
- ✅ Detecta instalações anteriores corrompidas
- ✅ Verifica serviços e processos do Zabbix
- ✅ Identifica conflitos de arquivos/diretórios
- ✅ Valida privilégios de administrador

### **2. Correção Automática**
- ✅ Remove instalações anteriores com `-Force`
- ✅ Para e remove serviços conflitantes
- ✅ Termina processos do Zabbix em execução
- ✅ Limpa diretórios residuais

### **3. Instalação Robusta**
- ✅ Download automático com fallback SSL/TLS
- ✅ URLs alternativas para conectividade
- ✅ Instalação offline com `-LocalMSIPath`
- ✅ Logs detalhados de instalação MSI

### **4. Configuração Completa**
- ✅ Configuração automática do agent
- ✅ Serviço configurado para inicialização automática
- ✅ Regras de firewall para porta 10050
- ✅ Testes de conectividade pós-instalação

## 🔧 Parâmetros Disponíveis

| Parâmetro | Obrigatório | Descrição | Exemplo |
|-----------|-------------|-----------|---------|
| `ZabbixServerIP` | ✅ Sim | IP do servidor Zabbix | `"*************"` |
| `AgentHostname` | ❌ Não | Nome do host | `"SERVIDOR-01"` |
| `ZabbixVersion` | ❌ Não | Versão do Zabbix | `"7.0.6"` |
| `InstallPath` | ❌ Não | Caminho de instalação | `"C:\Zabbix"` |
| `LocalMSIPath` | ❌ Não | Arquivo MSI local | `"C:\temp\zabbix.msi"` |
| `DiagnoseOnly` | ❌ Não | Apenas diagnóstico | `-DiagnoseOnly` |
| `Force` | ❌ Não | Correção automática | `-Force` |

## 🎮 Modos de Operação

### **1. Diagnóstico Apenas**
```powershell
-ZabbixServerIP "*************" -DiagnoseOnly
```
- Verifica problemas sem fazer alterações
- Retorna código 0 se sistema estiver limpo
- Retorna código 1 se houver problemas

### **2. Instalação Normal**
```powershell
-ZabbixServerIP "*************" -AgentHostname "SERVIDOR-01"
```
- Executa diagnóstico básico
- Instala se não houver problemas críticos
- Exibe avisos sobre problemas encontrados

### **3. Instalação com Correção** (Recomendado para erro 1603)
```powershell
-ZabbixServerIP "*************" -AgentHostname "SERVIDOR-01" -Force
```
- Executa diagnóstico completo
- **Corrige automaticamente** todos os problemas
- Instala após limpeza completa

### **4. Instalação Offline**
```powershell
-ZabbixServerIP "*************" -LocalMSIPath "C:\temp\zabbix.msi"
```
- Usa arquivo MSI baixado manualmente
- Ideal para problemas de conectividade/proxy

## 📊 Tratamento de Erros MSI

O script identifica e trata especificamente:

- **1603**: Erro fatal (instalação anterior corrompida)
- **1618**: Outra instalação em andamento
- **1619**: Pacote não encontrado ou corrompido
- **1620**: Pacote não pode ser aberto
- **1633**: Plataforma não suportada

## 🔍 Logs e Diagnóstico

### **Logs gerados:**
- **Script**: Logs com timestamp no console
- **MSI**: `%TEMP%\zabbix_install.log`
- **Agent**: `C:\Program Files\Zabbix Agent 2\zabbix_agent2.log`

### **Verificação pós-instalação:**
```powershell
# Verificar serviço
Get-Service "Zabbix Agent 2"

# Verificar configuração
Get-Content "C:\Program Files\Zabbix Agent 2\zabbix_agent2.conf"

# Verificar logs
Get-Content "C:\Program Files\Zabbix Agent 2\zabbix_agent2.log" -Tail 20
```

## 🚨 Resolução do Erro 1603

### **Comando específico para seu caso:**
```powershell
-ZabbixServerIP "*************" -AgentHostname "segundotab-servidor-sistema-ad" -Force
```

### **O que o script fará:**
1. **Diagnóstico completo** do sistema
2. **Remoção automática** de instalações anteriores
3. **Limpeza de serviços** e processos conflitantes
4. **Download seguro** com protocolos SSL/TLS
5. **Instalação limpa** com logs detalhados
6. **Configuração completa** do agent

## ✅ Vantagens do Script Simplificado

- **100% compatível** com Tactical RMM
- **Sem caracteres especiais** que causam problemas
- **Sintaxe limpa** e validada
- **Todas as funcionalidades** mantidas
- **Logs em português simples** (sem acentos)
- **Tratamento robusto** de erros

## 🎯 Próximos Passos

1. **Substitua** o script atual no Tactical RMM por `Install-ZabbixAgent2-Windows-Simple.ps1`
2. **Execute** com os argumentos:
   ```
   -ZabbixServerIP "*************" -AgentHostname "segundotab-servidor-sistema-ad" -Force
   ```
3. **Monitore** os logs para verificar a correção automática

---

**💡 Este script resolve definitivamente os problemas de sintaxe mantendo todas as funcionalidades!**
