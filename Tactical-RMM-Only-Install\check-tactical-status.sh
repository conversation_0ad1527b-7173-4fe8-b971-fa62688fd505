#!/bin/bash

# Script de verificação de status do Tactical RMM Agent
# Autor: <PERSON>eus - NVirtual
# Data: 2025-01-21
# Versão: 1.0.0

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

success() {
    echo -e "${GREEN}[OK] $1${NC}"
}

# Banner
echo "
=========================================="
echo "  VERIFICADOR DE STATUS - TACTICAL RMM"
echo "=========================================="
log "Verificador de Status Tactical RMM - NVirtual"
log "Desenvolvido por Paulo Matheus"
echo

# Função para verificar status de serviços
check_services() {
    log "Verificando status dos serviços..."
    echo
    
    local services=("tacticalagent" "rmmagent" "meshagent")
    local found_service=false
    
    for service in "${services[@]}"; do
        if systemctl list-units --type=service | grep -q "$service"; then
            found_service=true
            echo "🔍 Serviço: $service"
            
            if systemctl is-active --quiet "$service"; then
                success "  Status: ATIVO ✅"
            else
                error "  Status: INATIVO ❌"
            fi
            
            if systemctl is-enabled --quiet "$service" 2>/dev/null; then
                success "  Inicialização: HABILITADO ✅"
            else
                warning "  Inicialização: DESABILITADO ⚠️"
            fi
            
            # Mostrar tempo de atividade
            local uptime=$(systemctl show "$service" --property=ActiveEnterTimestamp --value 2>/dev/null)
            if [[ -n "$uptime" && "$uptime" != "n/a" ]]; then
                info "  Ativo desde: $uptime"
            fi
            
            echo
        fi
    done
    
    if [[ "$found_service" == "false" ]]; then
        error "Nenhum serviço do Tactical RMM encontrado!"
        info "O Tactical RMM pode não estar instalado."
    fi
}

# Função para verificar processos em execução
check_processes() {
    log "Verificando processos em execução..."
    echo
    
    local processes=("tacticalagent" "rmmagent" "meshagent")
    local found_process=false
    
    for process in "${processes[@]}"; do
        local pids=$(pgrep "$process" 2>/dev/null || true)
        if [[ -n "$pids" ]]; then
            found_process=true
            success "🔍 Processo: $process"
            echo "$pids" | while read -r pid; do
                if [[ -n "$pid" ]]; then
                    local cmd=$(ps -p "$pid" -o cmd --no-headers 2>/dev/null || echo "N/A")
                    local mem=$(ps -p "$pid" -o rss --no-headers 2>/dev/null || echo "N/A")
                    local cpu=$(ps -p "$pid" -o %cpu --no-headers 2>/dev/null || echo "N/A")
                    
                    info "  PID: $pid"
                    info "  Comando: $cmd"
                    info "  Memória: ${mem} KB"
                    info "  CPU: ${cpu}%"
                fi
            done
            echo
        fi
    done
    
    if [[ "$found_process" == "false" ]]; then
        warning "Nenhum processo do Tactical RMM encontrado em execução."
    fi
}

# Função para verificar arquivos de instalação
check_files() {
    log "Verificando arquivos de instalação..."
    echo
    
    local files=(
        "/usr/local/bin/tacticalagent"
        "/usr/local/bin/rmmagent"
        "/opt/tacticalagent"
        "/opt/rmmagent"
        "/etc/systemd/system/tacticalagent.service"
        "/etc/systemd/system/rmmagent.service"
    )
    
    local found_file=false
    
    for file in "${files[@]}"; do
        if [[ -e "$file" ]]; then
            found_file=true
            success "📁 Arquivo encontrado: $file"
            
            # Mostrar informações do arquivo
            local size=$(du -h "$file" 2>/dev/null | cut -f1)
            local perms=$(ls -la "$file" 2>/dev/null | awk '{print $1}')
            local modified=$(stat -c %y "$file" 2>/dev/null | cut -d'.' -f1)
            
            info "  Tamanho: $size"
            info "  Permissões: $perms"
            info "  Modificado: $modified"
            echo
        fi
    done
    
    if [[ "$found_file" == "false" ]]; then
        warning "Nenhum arquivo principal do Tactical RMM encontrado."
        info "O Tactical RMM pode não estar instalado corretamente."
    fi
}

# Função para verificar conectividade
check_connectivity() {
    log "Verificando conectividade com servidores..."
    echo
    
    local servers=(
        "mesh.centralmesh.nvirtual.com.br"
        "api.centralmesh.nvirtual.com.br"
    )
    
    for server in "${servers[@]}"; do
        echo "🌐 Testando: $server"
        
        # Teste de resolução DNS
        if nslookup "$server" >/dev/null 2>&1; then
            success "  DNS: OK ✅"
        else
            error "  DNS: FALHA ❌"
        fi
        
        # Teste de conectividade HTTP/HTTPS
        if curl -I "https://$server" --connect-timeout 10 >/dev/null 2>&1; then
            success "  HTTPS: OK ✅"
        else
            error "  HTTPS: FALHA ❌"
        fi
        
        # Teste de ping
        if ping -c 3 "$server" >/dev/null 2>&1; then
            success "  PING: OK ✅"
        else
            warning "  PING: FALHA ⚠️"
        fi
        
        echo
    done
}

# Função para mostrar logs recentes
show_recent_logs() {
    log "Mostrando logs recentes..."
    echo
    
    local services=("tacticalagent" "rmmagent")
    
    for service in "${services[@]}"; do
        if systemctl list-units --type=service | grep -q "$service"; then
            echo "📋 Logs recentes do $service:"
            echo "----------------------------------------"
            sudo journalctl -u "$service" -n 10 --no-pager 2>/dev/null || echo "Nenhum log encontrado"
            echo
        fi
    done
}

# Função para mostrar informações do sistema
show_system_info() {
    log "Informações do sistema..."
    echo
    
    echo "💻 Sistema:"
    info "  OS: $(lsb_release -d 2>/dev/null | cut -f2 || cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
    info "  Kernel: $(uname -r)"
    info "  Arquitetura: $(uname -m)"
    info "  Uptime: $(uptime -p 2>/dev/null || uptime)"
    echo
    
    echo "🌐 Rede:"
    local ip=$(ip route get ******* 2>/dev/null | awk '{print $7; exit}' || echo "N/A")
    local interface=$(ip route get ******* 2>/dev/null | awk '{print $5; exit}' || echo "N/A")
    info "  IP Principal: $ip"
    info "  Interface: $interface"
    echo
}

# Menu de opções
show_menu() {
    echo "=========================================="
    echo "OPÇÕES DISPONÍVEIS:"
    echo "=========================================="
    echo "1. Verificação completa (padrão)"
    echo "2. Apenas status de serviços"
    echo "3. Apenas processos"
    echo "4. Apenas arquivos"
    echo "5. Apenas conectividade"
    echo "6. Apenas logs recentes"
    echo "7. Informações do sistema"
    echo "8. Monitoramento contínuo (logs em tempo real)"
    echo "0. Sair"
    echo
}

# Função para monitoramento contínuo
continuous_monitoring() {
    log "Iniciando monitoramento contínuo..."
    info "Pressione Ctrl+C para parar"
    echo
    
    local services=("tacticalagent" "rmmagent")
    
    for service in "${services[@]}"; do
        if systemctl list-units --type=service | grep -q "$service"; then
            echo "📋 Monitorando logs do $service em tempo real:"
            echo "----------------------------------------"
            sudo journalctl -u "$service" -f
            break
        fi
    done
    
    if ! systemctl list-units --type=service | grep -qE "(tacticalagent|rmmagent)"; then
        error "Nenhum serviço do Tactical RMM encontrado para monitorar."
    fi
}

# Verificação completa
full_check() {
    show_system_info
    check_services
    check_processes
    check_files
    check_connectivity
    show_recent_logs
}

# Programa principal
if [[ $# -eq 0 ]]; then
    # Modo interativo
    while true; do
        show_menu
        read -p "Escolha uma opção (1-8, 0 para sair): " choice
        echo
        
        case $choice in
            1) full_check ;;
            2) check_services ;;
            3) check_processes ;;
            4) check_files ;;
            5) check_connectivity ;;
            6) show_recent_logs ;;
            7) show_system_info ;;
            8) continuous_monitoring ;;
            0) log "Saindo..."; exit 0 ;;
            *) error "Opção inválida!" ;;
        esac
        
        if [[ $choice != "8" ]]; then
            echo
            read -p "Pressione Enter para continuar..."
            clear
        fi
    done
else
    # Modo não-interativo (verificação completa)
    full_check
fi
