# Script simples para validar sintaxe
Write-Host "Validando sintaxe do script..."

$scriptPath = "Set-TacticalSite-Cliente copy.ps1"

if (Test-Path $scriptPath) {
    Write-Host "OK - Arquivo encontrado: $scriptPath"

    # Tentar carregar o conteúdo
    try {
        $content = Get-Content $scriptPath -Raw
        Write-Host "OK - Conteudo carregado com sucesso"

        # Verificar se contém as principais seções
        if ($content -match "function Get-LocalIP") {
            Write-Host "OK - Funcao Get-LocalIP encontrada"
        }

        if ($content -match "param\(") {
            Write-Host "OK - Parametros definidos"
        }

        if ($content -match "Invoke-RestMethod") {
            Write-Host "OK - Chamadas de API encontradas"
        }

        Write-Host "SUCESSO - Validacao concluida!"

    } catch {
        Write-Host "ERRO ao processar arquivo: $($_.Exception.Message)"
    }
} else {
    Write-Host "ERRO - Arquivo nao encontrado: $scriptPath"
}
