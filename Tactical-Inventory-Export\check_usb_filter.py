import csv

filename = 'FamaSegurosMeuRelatorio_FINAL_GB_CORRETO_Cliente33_CONSOLIDADO.csv'

with open(filename, 'r', encoding='utf-8') as f:
    reader = csv.DictReader(f)
    rows = list(reader)

print('Verificação de discos (sem USB):')
for i, row in enumerate(rows):
    print(f'{i+1}. {row["Hostname"]}:')
    print(f'   Discos: {row["Quantidade_Discos"]}')
    print(f'   Total: {row["Espaco_Total_GB"]} GB')
    print(f'   Usado: {row["Espaco_Usado_GB"]} GB')
    print(f'   Livre: {row["Espaco_Livre_GB"]} GB')
    print(f'   Uso: {row["Percentual_Uso"]}%')
    print()

print('Verificação se valores estão em GB:')
for row in rows:
    if float(row["Espaco_Total_GB"]) > 0:
        print(f'{row["Hostname"]}: {row["Espaco_Total_GB"]} GB total')
        # Verificar se valores são razoáveis para GB (não TB ou MB)
        total_gb = float(row["Espaco_Total_GB"])
        if total_gb > 10000:  # Mais de 10TB seria suspeito
            print(f'  ⚠️ ATENÇÃO: Valor muito alto, pode estar em MB ou bytes')
        elif total_gb < 1:  # Menos de 1GB seria suspeito
            print(f'  ⚠️ ATENÇÃO: Valor muito baixo, pode estar em TB')
        else:
            print(f'  ✅ Valor em GB parece correto')
